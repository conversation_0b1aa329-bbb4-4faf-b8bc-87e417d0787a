# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@adraffy/ens-normalize@npm:1.10.1":
  version: 1.10.1
  resolution: "@adraffy/ens-normalize@npm:1.10.1"
  checksum: 0836f394ea256972ec19a0b5e78cb7f5bcdfd48d8a32c7478afc94dd53ae44c04d1aa2303d7f3077b4f3ac2323b1f557ab9188e8059978748fdcd83e04a80dcc
  languageName: node
  linkType: hard

"@adraffy/ens-normalize@npm:^1.10.1, @adraffy/ens-normalize@npm:^1.8.8":
  version: 1.11.0
  resolution: "@adraffy/ens-normalize@npm:1.11.0"
  checksum: b2911269e3e0ec6396a2e5433a99e0e1f9726befc6c167994448cd0e53dbdd0be22b4835b4f619558b568ed9aa7312426b8fa6557a13999463489daa88169ee5
  languageName: node
  linkType: hard

"@arbitrum/nitro-contracts@npm:1.1.1":
  version: 1.1.1
  resolution: "@arbitrum/nitro-contracts@npm:1.1.1"
  dependencies:
    "@offchainlabs/upgrade-executor": 1.1.0-beta.0
    "@openzeppelin/contracts": 4.5.0
    "@openzeppelin/contracts-upgradeable": 4.5.2
    patch-package: ^6.4.7
  checksum: 3e457098b5b0e70fc7444d2a1cf4471868301c0511deef67c4947f6cb63367766744ddb7b46239cb65d58d9f3ad08818089a243067c1b7dd207b1293060cc843
  languageName: node
  linkType: hard

"@arbitrum/nitro-contracts@npm:^1.0.0-beta.8":
  version: 1.3.0
  resolution: "@arbitrum/nitro-contracts@npm:1.3.0"
  dependencies:
    "@offchainlabs/upgrade-executor": 1.1.0-beta.0
    "@openzeppelin/contracts": 4.5.0
    "@openzeppelin/contracts-upgradeable": 4.5.2
    patch-package: ^6.4.7
  checksum: 9f3d7080301851609fb190d79df4c2b37ac10408db58992687732d94f1a0138badb63afec03a2b611b0b634e6b37dfbe65abcbbf9f68ca70126a6ea0021a9c9f
  languageName: node
  linkType: hard

"@arbitrum/token-bridge-contracts@npm:1.1.2":
  version: 1.1.2
  resolution: "@arbitrum/token-bridge-contracts@npm:1.1.2"
  dependencies:
    "@arbitrum/nitro-contracts": ^1.0.0-beta.8
    "@offchainlabs/upgrade-executor": 1.1.0-beta.0
    "@openzeppelin/contracts": 4.8.3
    "@openzeppelin/contracts-upgradeable": 4.8.3
    "@openzeppelin/upgrades-core": ^1.24.1
  dependenciesMeta:
    "@openzeppelin/upgrades-core":
      optional: true
  checksum: 526aafb541df793635c30a9a0a2b358274f450e2ccbb15eb0df52279f4fa845c3ba1511b886e21f1eba52baeb262c9e27e66ba8aca977a2461ddfe854aa799c7
  languageName: node
  linkType: hard

"@aws-crypto/crc32@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^2.6.2
  checksum: 1ddf7ec3fccf106205ff2476d90ae1d6625eabd47752f689c761b71e41fe451962b7a1c9ed25fe54e17dd747a62fbf4de06030fe56fe625f95285f6f70b96c57
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": ^5.2.0
    "@aws-crypto/supports-web-crypto": ^5.2.0
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    "@aws-sdk/util-locate-window": ^3.0.0
    "@smithy/util-utf8": ^2.0.0
    tslib: ^2.6.2
  checksum: 773f12f2026d82a6bb4a23a8f491894a6d32525bd9b8bfbc12896526cf11882a7607a671c478c45f9cd7d6ba1caaed48a62b67c6f725244bd83a1275108f46c7
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:1.2.2":
  version: 1.2.2
  resolution: "@aws-crypto/sha256-js@npm:1.2.2"
  dependencies:
    "@aws-crypto/util": ^1.2.2
    "@aws-sdk/types": ^3.1.0
    tslib: ^1.11.1
  checksum: b6aeb71f88ecc219c5473803345bb15150ecd056a337582638dd60fb2344e0ff63908c684ef55268b249290fe0776e8e6fc830605f0aad850ff325b9cfe0dc6a
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^2.6.2
  checksum: 007fbe0436d714d0d0d282e2b61c90e45adcb9ad75eac9ac7ba03d32b56624afd09b2a9ceb4d659661cf17c51d74d1900ab6b00eacafc002da1101664955ca53
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 6ffc21de48b2b2c3e918193101d7e8fe949d47b37688892e1c39eaedaa938be80c0f404fe1c874c30cce16781026777a53bf47d5d90143ca91d0feb7c4a6f830
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:^1.2.2":
  version: 1.2.2
  resolution: "@aws-crypto/util@npm:1.2.2"
  dependencies:
    "@aws-sdk/types": ^3.1.0
    "@aws-sdk/util-utf8-browser": ^3.0.0
    tslib: ^1.11.1
  checksum: 54d72ce4945b52f3fcbcb62574a55bc038cc3ff165742f340cabca1bdc979faf69c97709cf56daf434e4ad69e33582a04a64da33b4e4e13b25c6ff67f8abe5ae
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": ^3.222.0
    "@smithy/util-utf8": ^2.0.0
    tslib: ^2.6.2
  checksum: f0f81d9d2771c59946cfec48b86cb23d39f78a966c4a1f89d4753abdc3cb38de06f907d1e6450059b121d48ac65d612ab88bdb70014553a077fc3dabddfbf8d6
  languageName: node
  linkType: hard

"@aws-sdk/client-lambda@npm:^3.563.0":
  version: 3.726.1
  resolution: "@aws-sdk/client-lambda@npm:3.726.1"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/client-sso-oidc": 3.726.0
    "@aws-sdk/client-sts": 3.726.1
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/credential-provider-node": 3.726.0
    "@aws-sdk/middleware-host-header": 3.723.0
    "@aws-sdk/middleware-logger": 3.723.0
    "@aws-sdk/middleware-recursion-detection": 3.723.0
    "@aws-sdk/middleware-user-agent": 3.726.0
    "@aws-sdk/region-config-resolver": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@aws-sdk/util-endpoints": 3.726.0
    "@aws-sdk/util-user-agent-browser": 3.723.0
    "@aws-sdk/util-user-agent-node": 3.726.0
    "@smithy/config-resolver": ^4.0.0
    "@smithy/core": ^3.0.0
    "@smithy/eventstream-serde-browser": ^4.0.0
    "@smithy/eventstream-serde-config-resolver": ^4.0.0
    "@smithy/eventstream-serde-node": ^4.0.0
    "@smithy/fetch-http-handler": ^5.0.0
    "@smithy/hash-node": ^4.0.0
    "@smithy/invalid-dependency": ^4.0.0
    "@smithy/middleware-content-length": ^4.0.0
    "@smithy/middleware-endpoint": ^4.0.0
    "@smithy/middleware-retry": ^4.0.0
    "@smithy/middleware-serde": ^4.0.0
    "@smithy/middleware-stack": ^4.0.0
    "@smithy/node-config-provider": ^4.0.0
    "@smithy/node-http-handler": ^4.0.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/smithy-client": ^4.0.0
    "@smithy/types": ^4.0.0
    "@smithy/url-parser": ^4.0.0
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.0
    "@smithy/util-defaults-mode-node": ^4.0.0
    "@smithy/util-endpoints": ^3.0.0
    "@smithy/util-middleware": ^4.0.0
    "@smithy/util-retry": ^4.0.0
    "@smithy/util-stream": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    "@smithy/util-waiter": ^4.0.0
    tslib: ^2.6.2
  checksum: 28478926cd1eba23dda6e53d72949f91f2897069f775d1025a62340b9982d64609cdbbab0cc6bf5c7d3740bece46fc51307ddc73dbcd019d225ea7a7a8f76cd6
  languageName: node
  linkType: hard

"@aws-sdk/client-sso-oidc@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/client-sso-oidc@npm:3.726.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/credential-provider-node": 3.726.0
    "@aws-sdk/middleware-host-header": 3.723.0
    "@aws-sdk/middleware-logger": 3.723.0
    "@aws-sdk/middleware-recursion-detection": 3.723.0
    "@aws-sdk/middleware-user-agent": 3.726.0
    "@aws-sdk/region-config-resolver": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@aws-sdk/util-endpoints": 3.726.0
    "@aws-sdk/util-user-agent-browser": 3.723.0
    "@aws-sdk/util-user-agent-node": 3.726.0
    "@smithy/config-resolver": ^4.0.0
    "@smithy/core": ^3.0.0
    "@smithy/fetch-http-handler": ^5.0.0
    "@smithy/hash-node": ^4.0.0
    "@smithy/invalid-dependency": ^4.0.0
    "@smithy/middleware-content-length": ^4.0.0
    "@smithy/middleware-endpoint": ^4.0.0
    "@smithy/middleware-retry": ^4.0.0
    "@smithy/middleware-serde": ^4.0.0
    "@smithy/middleware-stack": ^4.0.0
    "@smithy/node-config-provider": ^4.0.0
    "@smithy/node-http-handler": ^4.0.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/smithy-client": ^4.0.0
    "@smithy/types": ^4.0.0
    "@smithy/url-parser": ^4.0.0
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.0
    "@smithy/util-defaults-mode-node": ^4.0.0
    "@smithy/util-endpoints": ^3.0.0
    "@smithy/util-middleware": ^4.0.0
    "@smithy/util-retry": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  peerDependencies:
    "@aws-sdk/client-sts": ^3.726.0
  checksum: 10e4613eff357fa0646b260c268635a39ed10e65148370e630031809ca06ec392b34eb6bc729fc5fbc33dca6a0fe30bce7ead4ba04fbf89c939dbe525460c5ef
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/client-sso@npm:3.726.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/middleware-host-header": 3.723.0
    "@aws-sdk/middleware-logger": 3.723.0
    "@aws-sdk/middleware-recursion-detection": 3.723.0
    "@aws-sdk/middleware-user-agent": 3.726.0
    "@aws-sdk/region-config-resolver": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@aws-sdk/util-endpoints": 3.726.0
    "@aws-sdk/util-user-agent-browser": 3.723.0
    "@aws-sdk/util-user-agent-node": 3.726.0
    "@smithy/config-resolver": ^4.0.0
    "@smithy/core": ^3.0.0
    "@smithy/fetch-http-handler": ^5.0.0
    "@smithy/hash-node": ^4.0.0
    "@smithy/invalid-dependency": ^4.0.0
    "@smithy/middleware-content-length": ^4.0.0
    "@smithy/middleware-endpoint": ^4.0.0
    "@smithy/middleware-retry": ^4.0.0
    "@smithy/middleware-serde": ^4.0.0
    "@smithy/middleware-stack": ^4.0.0
    "@smithy/node-config-provider": ^4.0.0
    "@smithy/node-http-handler": ^4.0.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/smithy-client": ^4.0.0
    "@smithy/types": ^4.0.0
    "@smithy/url-parser": ^4.0.0
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.0
    "@smithy/util-defaults-mode-node": ^4.0.0
    "@smithy/util-endpoints": ^3.0.0
    "@smithy/util-middleware": ^4.0.0
    "@smithy/util-retry": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 9669529543728d071be76f97205a1a184286b2b5b13c2098bfffb59e38274122cf92aad243da34e359e6ca8ad65dae1f2863f5cdb7d3622b6a40dd5876041027
  languageName: node
  linkType: hard

"@aws-sdk/client-sts@npm:3.726.1":
  version: 3.726.1
  resolution: "@aws-sdk/client-sts@npm:3.726.1"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/client-sso-oidc": 3.726.0
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/credential-provider-node": 3.726.0
    "@aws-sdk/middleware-host-header": 3.723.0
    "@aws-sdk/middleware-logger": 3.723.0
    "@aws-sdk/middleware-recursion-detection": 3.723.0
    "@aws-sdk/middleware-user-agent": 3.726.0
    "@aws-sdk/region-config-resolver": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@aws-sdk/util-endpoints": 3.726.0
    "@aws-sdk/util-user-agent-browser": 3.723.0
    "@aws-sdk/util-user-agent-node": 3.726.0
    "@smithy/config-resolver": ^4.0.0
    "@smithy/core": ^3.0.0
    "@smithy/fetch-http-handler": ^5.0.0
    "@smithy/hash-node": ^4.0.0
    "@smithy/invalid-dependency": ^4.0.0
    "@smithy/middleware-content-length": ^4.0.0
    "@smithy/middleware-endpoint": ^4.0.0
    "@smithy/middleware-retry": ^4.0.0
    "@smithy/middleware-serde": ^4.0.0
    "@smithy/middleware-stack": ^4.0.0
    "@smithy/node-config-provider": ^4.0.0
    "@smithy/node-http-handler": ^4.0.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/smithy-client": ^4.0.0
    "@smithy/types": ^4.0.0
    "@smithy/url-parser": ^4.0.0
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.0
    "@smithy/util-defaults-mode-node": ^4.0.0
    "@smithy/util-endpoints": ^3.0.0
    "@smithy/util-middleware": ^4.0.0
    "@smithy/util-retry": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 524038171f5570a9e0adc8053d6574c4fb64f34b048a6b7cf1ecd0563a1a1736b9ae4556df1e8a62f5ada292078f8c2d325fa93006f518861f998cdde74ed124
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/core@npm:3.723.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/core": ^3.0.0
    "@smithy/node-config-provider": ^4.0.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/signature-v4": ^5.0.0
    "@smithy/smithy-client": ^4.0.0
    "@smithy/types": ^4.0.0
    "@smithy/util-middleware": ^4.0.0
    fast-xml-parser: 4.4.1
    tslib: ^2.6.2
  checksum: d9a5ebd8a7aeaf33682fedbe49774afe4574fa17905e2b26d9cd362702821aea3af291c780dcf1c8df80e0440561f64a90d032c5e70107fc3bec630f6acfb890
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.723.0"
  dependencies:
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: 46ddaade584f43190d3b889a235570a25074dc6f09a089241f9d65960b3fb7257f875d069919a7908b48162e0898e6aa666437385ae7786e0a433755e0c0cbed
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.723.0"
  dependencies:
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@smithy/fetch-http-handler": ^5.0.0
    "@smithy/node-http-handler": ^4.0.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/smithy-client": ^4.0.0
    "@smithy/types": ^4.0.0
    "@smithy/util-stream": ^4.0.0
    tslib: ^2.6.2
  checksum: bf9d7d5795b52fd937cd71f66377886ce2db00c271b295d98910ca7ab3e850a26aef298aad7a73e01f2b182316f2e3b054dd533b2c3482b6c0acbe80385e0e14
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.726.0"
  dependencies:
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/credential-provider-env": 3.723.0
    "@aws-sdk/credential-provider-http": 3.723.0
    "@aws-sdk/credential-provider-process": 3.723.0
    "@aws-sdk/credential-provider-sso": 3.726.0
    "@aws-sdk/credential-provider-web-identity": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@smithy/credential-provider-imds": ^4.0.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/shared-ini-file-loader": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  peerDependencies:
    "@aws-sdk/client-sts": ^3.726.0
  checksum: 935c75ca351f69e67b28357dadbecbcf6738df39c22b84e89f8c06a90ca8fadc2f78fa1dda3774c05d7aca60b0b039fde0e339299309a44a5974da0b46719fce
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.726.0"
  dependencies:
    "@aws-sdk/credential-provider-env": 3.723.0
    "@aws-sdk/credential-provider-http": 3.723.0
    "@aws-sdk/credential-provider-ini": 3.726.0
    "@aws-sdk/credential-provider-process": 3.723.0
    "@aws-sdk/credential-provider-sso": 3.726.0
    "@aws-sdk/credential-provider-web-identity": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@smithy/credential-provider-imds": ^4.0.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/shared-ini-file-loader": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: abca27ebc69c8f908c3272a434be228375291b838002b282ec85115b6bf87fd38ab618da4d260e24ad2235b9eb395a903172cf7e4d7e707ba1569516f83d2bfc
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.723.0"
  dependencies:
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/shared-ini-file-loader": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: 2ebbe80865db30269ef255b7587eefe86851fe1787e5e8f846dabed728e670834c79a9ecd5e258b1c476dca44c02e55a5b0b214f6cd59faf0de2cbb29187668d
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.726.0"
  dependencies:
    "@aws-sdk/client-sso": 3.726.0
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/token-providers": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/shared-ini-file-loader": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: 37e3ece21f072ca8b74a15007842249062defa3acb623351417e47808b491830766329fd52e775dbb31db1f0d2769ca29598eb05ffe1031ecec472c6841113ce
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.723.0"
  dependencies:
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  peerDependencies:
    "@aws-sdk/client-sts": ^3.723.0
  checksum: f011707011361bd4d91b655aec9aaba40f00b653f2b67b2c6802721bc5ff6b18eb9d7345327085cac90310ef91706787a7beeffb59233f82b6cc1cf276f75c48
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.723.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: e2517de66beee4eb019f1423855a53c7231ce79ee9447c3efba608f3f7c9f6a2ebe8bc3e1f41a41d2744c110267d98c9479b063b9e7dbc21a31a65ede7a0e9af
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/middleware-logger@npm:3.723.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: 7ea206deaafc77a6d79713a5dd9480b1784e8edd1e0cdb0cfce8d0fe2ee69c6f41fad07934f39c73f0d69b74511850847f007f3e717e65efb2a1bb9326423ab7
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.723.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: 06ca854166cf68b81551a57665bd1ebdb96b5d8d9f7cb6faaf92a29d6efcb72bbb50b53c111fb670284586ac4bcb93d0687ec461a7bf04099faa28d02a1ca303
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.726.0"
  dependencies:
    "@aws-sdk/core": 3.723.0
    "@aws-sdk/types": 3.723.0
    "@aws-sdk/util-endpoints": 3.726.0
    "@smithy/core": ^3.0.0
    "@smithy/protocol-http": ^5.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: b50b3361d38ace26f1fba1a868d0057b5a7baec3a9b370a38d36af523fa66dd91215ca81ddc8ec619e4a1bb572abbef0011a3f78c6ca09f90ebce38b12753089
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.723.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/node-config-provider": ^4.0.0
    "@smithy/types": ^4.0.0
    "@smithy/util-config-provider": ^4.0.0
    "@smithy/util-middleware": ^4.0.0
    tslib: ^2.6.2
  checksum: 351160af2b7e9d57971cde6efe9e162133ba04c0082fbe62c3b1c9b4341f821b24504f1bff382b6c1d00eccf1b7cda45e6b06ab6d2991f4823814b44d9946a83
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/token-providers@npm:3.723.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/property-provider": ^4.0.0
    "@smithy/shared-ini-file-loader": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  peerDependencies:
    "@aws-sdk/client-sso-oidc": ^3.723.0
  checksum: 61984cc6f749a3eab3bbe65974400feadfb37acbdfc2bcb44437f1e5a4deeecaddb68fc9c6e5aa6360b1e4dff1d3763b88cd934e36d0ede6278413f641ebb173
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.723.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.723.0
  resolution: "@aws-sdk/types@npm:3.723.0"
  dependencies:
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  checksum: 670840051b6575e3b94f4963fceb75b08a4ea027e1acbbe60b7e39a373494d537cd7c4a9825d0455be62e0c1849903f7a1b41c3a6212877d2fd4e62ffd4d26ef
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:^3.1.0":
  version: 3.714.0
  resolution: "@aws-sdk/types@npm:3.714.0"
  dependencies:
    "@smithy/types": ^3.7.2
    tslib: ^2.6.2
  checksum: 2cc6ea0ec3331a24b5cc6dfba0963ff5a673c7149eadbaf9f9ab8bfdfaa56a7a02a7df80f6921108aca3ef022047f931fd8f36b34bfe6c963e8606f72e068143
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/util-endpoints@npm:3.726.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/types": ^4.0.0
    "@smithy/util-endpoints": ^3.0.0
    tslib: ^2.6.2
  checksum: d472cdbf6917ac88fc36778af3d5db1ac49966bce2277a602ec5952118ad7112580b411d2748af715e65c5d6c60ff04178709eda8c100da54593b5a982be8c59
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.723.0
  resolution: "@aws-sdk/util-locate-window@npm:3.723.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 2ac0322b579fd4008727ca2af5daa3527395b24270cbbda1bf00fe07c8f7727e6e192a88ed864285e285fb1447e39925fa0433efebf664e6cdbc968d07e51470
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.723.0":
  version: 3.723.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.723.0"
  dependencies:
    "@aws-sdk/types": 3.723.0
    "@smithy/types": ^4.0.0
    bowser: ^2.11.0
    tslib: ^2.6.2
  checksum: 40fac8acab1a935d920b3c0b5e152b000b184627b1b502424719fdf4df82068080b65db7ef08538aaac2d98b551aacbee5816cce9e6b3fcdfbeb7767c2989dae
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.726.0":
  version: 3.726.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.726.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": 3.726.0
    "@aws-sdk/types": 3.723.0
    "@smithy/node-config-provider": ^4.0.0
    "@smithy/types": ^4.0.0
    tslib: ^2.6.2
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: 04ebc304b65d0c17477a1872e8d2230c65c41e45d36af73acfe34557c505b2015de488eaebff0de33cec5481848bc6d199e9496882ca7c8294f44245e9afcc3c
  languageName: node
  linkType: hard

"@aws-sdk/util-utf8-browser@npm:^3.0.0":
  version: 3.259.0
  resolution: "@aws-sdk/util-utf8-browser@npm:3.259.0"
  dependencies:
    tslib: ^2.3.1
  checksum: b6a1e580da1c9b62c749814182a7649a748ca4253edb4063aa521df97d25b76eae3359eb1680b86f71aac668e05cc05c514379bca39ebf4ba998ae4348412da8
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.5.5":
  version: 7.26.0
  resolution: "@babel/runtime@npm:7.26.0"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: c8e2c0504ab271b3467a261a8f119bf2603eb857a0d71e37791f4e3fae00f681365073cc79f141ddaa90c6077c60ba56448004ad5429d07ac73532be9f7cf28a
  languageName: node
  linkType: hard

"@bytecodealliance/preview2-shim@npm:0.17.0":
  version: 0.17.0
  resolution: "@bytecodealliance/preview2-shim@npm:0.17.0"
  checksum: b5561ccb27dad50698ee82c3735453fa9bb84c18658c45707402a473ca81cde338d1a7965e507d63ef1ac90ee395a134f250c910c83d09c2205142c2e939bdfe
  languageName: node
  linkType: hard

"@chainlink/contracts@npm:^1.3.0":
  version: 1.3.0
  resolution: "@chainlink/contracts@npm:1.3.0"
  dependencies:
    "@arbitrum/nitro-contracts": 1.1.1
    "@arbitrum/token-bridge-contracts": 1.1.2
    "@changesets/changelog-github": ^0.5.0
    "@changesets/cli": ~2.27.8
    "@eth-optimism/contracts": 0.6.0
    "@openzeppelin/contracts": 4.9.3
    "@openzeppelin/contracts-upgradeable": 4.9.3
    "@scroll-tech/contracts": 0.1.0
    "@zksync/contracts": "git+https://github.com/matter-labs/era-contracts.git#446d391d34bdb48255d5f8fef8a8248925fc98b9"
    semver: ^7.6.3
  checksum: 170da3a3275074934608f992b5b392e7774d110480ab5d143ea4048d6deaeded129ffd092f00534098119dd03563337321aa2100d8d0d378a38195912c05c7f1
  languageName: node
  linkType: hard

"@changesets/apply-release-plan@npm:^7.0.7":
  version: 7.0.7
  resolution: "@changesets/apply-release-plan@npm:7.0.7"
  dependencies:
    "@changesets/config": ^3.0.5
    "@changesets/get-version-range-type": ^0.4.0
    "@changesets/git": ^3.0.2
    "@changesets/should-skip-package": ^0.1.1
    "@changesets/types": ^6.0.0
    "@manypkg/get-packages": ^1.1.3
    detect-indent: ^6.0.0
    fs-extra: ^7.0.1
    lodash.startcase: ^4.4.0
    outdent: ^0.5.0
    prettier: ^2.7.1
    resolve-from: ^5.0.0
    semver: ^7.5.3
  checksum: 775e8f86ab8b82ebdb44471dfbdf14e669144e63b6c972bc8502d608247ef909baca63973f71b8d15acd4fff8740a61802256bc261de12b956e4c2b8fd34d929
  languageName: node
  linkType: hard

"@changesets/assemble-release-plan@npm:^6.0.5":
  version: 6.0.5
  resolution: "@changesets/assemble-release-plan@npm:6.0.5"
  dependencies:
    "@changesets/errors": ^0.2.0
    "@changesets/get-dependents-graph": ^2.1.2
    "@changesets/should-skip-package": ^0.1.1
    "@changesets/types": ^6.0.0
    "@manypkg/get-packages": ^1.1.3
    semver: ^7.5.3
  checksum: d13c28a8666509a1e20dd496d2ec640ab11f610fcd88f69bd6c6166ae285ce647862e6a6a786d537f8d5070720dbca883cc4d1b08f83ed08b8a89b26221ad6c2
  languageName: node
  linkType: hard

"@changesets/changelog-git@npm:^0.2.0":
  version: 0.2.0
  resolution: "@changesets/changelog-git@npm:0.2.0"
  dependencies:
    "@changesets/types": ^6.0.0
  checksum: 132660f7fdabbdda00ac803cc822d6427a1a38a17a5f414e87ad32f6dc4cbef5280a147ecdc087a28dc06c8bd0762f8d6e7132d01b8a4142b59fbe1bc2177034
  languageName: node
  linkType: hard

"@changesets/changelog-github@npm:^0.5.0":
  version: 0.5.0
  resolution: "@changesets/changelog-github@npm:0.5.0"
  dependencies:
    "@changesets/get-github-info": ^0.6.0
    "@changesets/types": ^6.0.0
    dotenv: ^8.1.0
  checksum: 4ab43d8104693f970d878f2b1657ff67b4d4dcb7452ddf118575153bab74286cdfd125381c2ab92b205bce4b2c653c36552138bf2900f7165ac39a868b7fe22c
  languageName: node
  linkType: hard

"@changesets/cli@npm:~2.27.8":
  version: 2.27.11
  resolution: "@changesets/cli@npm:2.27.11"
  dependencies:
    "@changesets/apply-release-plan": ^7.0.7
    "@changesets/assemble-release-plan": ^6.0.5
    "@changesets/changelog-git": ^0.2.0
    "@changesets/config": ^3.0.5
    "@changesets/errors": ^0.2.0
    "@changesets/get-dependents-graph": ^2.1.2
    "@changesets/get-release-plan": ^4.0.6
    "@changesets/git": ^3.0.2
    "@changesets/logger": ^0.1.1
    "@changesets/pre": ^2.0.1
    "@changesets/read": ^0.6.2
    "@changesets/should-skip-package": ^0.1.1
    "@changesets/types": ^6.0.0
    "@changesets/write": ^0.3.2
    "@manypkg/get-packages": ^1.1.3
    ansi-colors: ^4.1.3
    ci-info: ^3.7.0
    enquirer: ^2.4.1
    external-editor: ^3.1.0
    fs-extra: ^7.0.1
    mri: ^1.2.0
    p-limit: ^2.2.0
    package-manager-detector: ^0.2.0
    picocolors: ^1.1.0
    resolve-from: ^5.0.0
    semver: ^7.5.3
    spawndamnit: ^3.0.1
    term-size: ^2.1.0
  bin:
    changeset: bin.js
  checksum: 0c9d1f03ad113494ad3091caa7eca1ee167761af8257911f74c64bff670fb8a542fe9f84921b1e7e29c584b2f5883b8a6287c4f42afe59007369f4fd1cc94cec
  languageName: node
  linkType: hard

"@changesets/config@npm:^3.0.5":
  version: 3.0.5
  resolution: "@changesets/config@npm:3.0.5"
  dependencies:
    "@changesets/errors": ^0.2.0
    "@changesets/get-dependents-graph": ^2.1.2
    "@changesets/logger": ^0.1.1
    "@changesets/types": ^6.0.0
    "@manypkg/get-packages": ^1.1.3
    fs-extra: ^7.0.1
    micromatch: ^4.0.8
  checksum: 75cdb5c4965656935277c69591336dd3aa825eef91d971c1d3e37777d181b6e3c1f1d78bf7ec826bbae9d70841af28c4a9cb1ea477808b3d9101413bd9f04fcf
  languageName: node
  linkType: hard

"@changesets/errors@npm:^0.2.0":
  version: 0.2.0
  resolution: "@changesets/errors@npm:0.2.0"
  dependencies:
    extendable-error: ^0.1.5
  checksum: 4b79373f92287af4f723e8dbbccaf0299aa8735fc043243d0ad587f04a7614615ea50180be575d4438b9f00aa82d1cf85e902b77a55bdd3e0a8dd97e77b18c60
  languageName: node
  linkType: hard

"@changesets/get-dependents-graph@npm:^2.1.2":
  version: 2.1.2
  resolution: "@changesets/get-dependents-graph@npm:2.1.2"
  dependencies:
    "@changesets/types": ^6.0.0
    "@manypkg/get-packages": ^1.1.3
    picocolors: ^1.1.0
    semver: ^7.5.3
  checksum: 38446343e43f9b8731098e3b42d2525d5399d59cfccc09bdb62c9a48de60c7a893882050202badca3b5cab8405e6deb82e88258a56a318e42749fa60d96d874a
  languageName: node
  linkType: hard

"@changesets/get-github-info@npm:^0.6.0":
  version: 0.6.0
  resolution: "@changesets/get-github-info@npm:0.6.0"
  dependencies:
    dataloader: ^1.4.0
    node-fetch: ^2.5.0
  checksum: 753173bda536aa79cb0502f59ce13889b23ae8463d04893d43ff22966818060837d9db4052b6cbfbd95dfb242fbfd38890a38c56832948e83bf358a47812b708
  languageName: node
  linkType: hard

"@changesets/get-release-plan@npm:^4.0.6":
  version: 4.0.6
  resolution: "@changesets/get-release-plan@npm:4.0.6"
  dependencies:
    "@changesets/assemble-release-plan": ^6.0.5
    "@changesets/config": ^3.0.5
    "@changesets/pre": ^2.0.1
    "@changesets/read": ^0.6.2
    "@changesets/types": ^6.0.0
    "@manypkg/get-packages": ^1.1.3
  checksum: 1aabe351e39ce0b0a057f96eb3ac4183907ad0458cffe1a7e0b5651ae1ef3aefb22cc6021df5114d646a2b5dc2a665bc670f9b705a85dca0bd31f7792b22403c
  languageName: node
  linkType: hard

"@changesets/get-version-range-type@npm:^0.4.0":
  version: 0.4.0
  resolution: "@changesets/get-version-range-type@npm:0.4.0"
  checksum: 2e8c511e658e193f48de7f09522649c4cf072932f0cbe0f252a7f2703d7775b0b90b632254526338795d0658e340be9dff3879cfc8eba4534b8cd6071efff8c9
  languageName: node
  linkType: hard

"@changesets/git@npm:^3.0.2":
  version: 3.0.2
  resolution: "@changesets/git@npm:3.0.2"
  dependencies:
    "@changesets/errors": ^0.2.0
    "@manypkg/get-packages": ^1.1.3
    is-subdir: ^1.1.1
    micromatch: ^4.0.8
    spawndamnit: ^3.0.1
  checksum: 56d15fd74881fccd7d46aa8e4d6b808e8d5a349103a5a54f2dd52d91a17056b271b04c28f7709fb162bab48ecaee2f51678fca01ed77be5a8392bf8efa6340c7
  languageName: node
  linkType: hard

"@changesets/logger@npm:^0.1.1":
  version: 0.1.1
  resolution: "@changesets/logger@npm:0.1.1"
  dependencies:
    picocolors: ^1.1.0
  checksum: acca50ef6bf6e446b46eb576b32f1955bf4579dbf4bbc316768ed2c1d4ba4066c9c73b114eedefaa1b3e360b1060a020e6bd3dbdbc44b74da732df92307beab0
  languageName: node
  linkType: hard

"@changesets/parse@npm:^0.4.0":
  version: 0.4.0
  resolution: "@changesets/parse@npm:0.4.0"
  dependencies:
    "@changesets/types": ^6.0.0
    js-yaml: ^3.13.1
  checksum: 3dd970b244479746233ebd357cfff3816cf9f344ebf2cf0c7c55ce8579adfd3f506978e86ad61222dc3acf1548a2105ffdd8b3e940b3f82b225741315cee2bf0
  languageName: node
  linkType: hard

"@changesets/pre@npm:^2.0.1":
  version: 2.0.1
  resolution: "@changesets/pre@npm:2.0.1"
  dependencies:
    "@changesets/errors": ^0.2.0
    "@changesets/types": ^6.0.0
    "@manypkg/get-packages": ^1.1.3
    fs-extra: ^7.0.1
  checksum: fbe94283dce0223ee79c12fa221105752ac89eb885b77e300ec755682cb06cc0145e10335f4bc6cb26d63473e549556c2b1c8c866242419aee5e41986379652a
  languageName: node
  linkType: hard

"@changesets/read@npm:^0.6.2":
  version: 0.6.2
  resolution: "@changesets/read@npm:0.6.2"
  dependencies:
    "@changesets/git": ^3.0.2
    "@changesets/logger": ^0.1.1
    "@changesets/parse": ^0.4.0
    "@changesets/types": ^6.0.0
    fs-extra: ^7.0.1
    p-filter: ^2.1.0
    picocolors: ^1.1.0
  checksum: e47e9a6417c92d9d81f1fb51bf8ba03296daa812acd84a0b30e007f89918822bec6d06148db00ed2fd2eab1fb705b9fa54da931ee8841cd470b1d36e42784ea1
  languageName: node
  linkType: hard

"@changesets/should-skip-package@npm:^0.1.1":
  version: 0.1.1
  resolution: "@changesets/should-skip-package@npm:0.1.1"
  dependencies:
    "@changesets/types": ^6.0.0
    "@manypkg/get-packages": ^1.1.3
  checksum: d187ef22495deb63e678d0ff65e8627701e2b52c25bd59dde10ce8646be8d605c0ed0a6af020dd825b137c2fc748fdc6cef52e7774bad4c7a4f404bf182a85cf
  languageName: node
  linkType: hard

"@changesets/types@npm:^4.0.1":
  version: 4.1.0
  resolution: "@changesets/types@npm:4.1.0"
  checksum: 72c1f58044178ca867dd9349ecc4b7c233ce3781bb03b5b72a70c3166fbbab54a2f2cb19a81f96b4649ba004442c8734569fba238be4dd737fb4624a135c6098
  languageName: node
  linkType: hard

"@changesets/types@npm:^6.0.0":
  version: 6.0.0
  resolution: "@changesets/types@npm:6.0.0"
  checksum: d528b5d712f62c26ea422c7d34ccf6eac57a353c0733d96716db3c796ecd9bba5d496d48b37d5d46b784dc45b69c06ce3345fa3515df981bb68456cad68e6465
  languageName: node
  linkType: hard

"@changesets/write@npm:^0.3.2":
  version: 0.3.2
  resolution: "@changesets/write@npm:0.3.2"
  dependencies:
    "@changesets/types": ^6.0.0
    fs-extra: ^7.0.1
    human-id: ^1.0.2
    prettier: ^2.7.1
  checksum: 553ed0ba6bd6397784f5e0e2921794bd7417a3c4fb810f1abb15e7072bf9d312af74308ff743161c6ea01478884cebcaf9cee02e5c70e2c7552b2774960ee07c
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: d64d5260bed1d5012ae3fc617d38d1afc0329fec05342f4e6b838f46998855ba56e0a73833f4a80fa8378c84810da254f76a8a19c39d038260dc06dc4e007425
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": 0.3.9
  checksum: 5718f267085ed8edb3e7ef210137241775e607ee18b77d95aa5bd7514f47f5019aa2d82d96b3bf342ef7aa890a346fa1044532ff7cc3009e7d24fce3ce6200fa
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: a7ffc838eb6a9ef594cda348458ccf38f34439ac77dc090fa1c120024bcd4eb911dfd74d5ef44d42063e7949fa7c5123ce714a015c4abb917d4124be1bd32bfe
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.6.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^9.6.0
    globals: ^13.19.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 10957c7592b20ca0089262d8c2a8accbad14b4f6507e35416c32ee6b4dbf9cad67dfb77096bbd405405e9ada2b107f3797fe94362e1c55e0b09d6e90dd149127
  languageName: node
  linkType: hard

"@eslint/js@npm:8.57.1":
  version: 8.57.1
  resolution: "@eslint/js@npm:8.57.1"
  checksum: 2afb77454c06e8316793d2e8e79a0154854d35e6782a1217da274ca60b5044d2c69d6091155234ed0551a1e408f86f09dd4ece02752c59568fa403e60611e880
  languageName: node
  linkType: hard

"@eth-optimism/contracts@npm:0.6.0":
  version: 0.6.0
  resolution: "@eth-optimism/contracts@npm:0.6.0"
  dependencies:
    "@eth-optimism/core-utils": 0.12.0
    "@ethersproject/abstract-provider": ^5.7.0
    "@ethersproject/abstract-signer": ^5.7.0
  peerDependencies:
    ethers: ^5
  checksum: 52e9a6cc6ad9bf3ab085d3be501fa4c89e48865baa8aee01aff39c2b007b69600304c7e8f8f4e00d67396e48a0dbfe3a260437efd3a4d7216424cece52639870
  languageName: node
  linkType: hard

"@eth-optimism/core-utils@npm:0.12.0":
  version: 0.12.0
  resolution: "@eth-optimism/core-utils@npm:0.12.0"
  dependencies:
    "@ethersproject/abi": ^5.7.0
    "@ethersproject/abstract-provider": ^5.7.0
    "@ethersproject/address": ^5.7.0
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/constants": ^5.7.0
    "@ethersproject/contracts": ^5.7.0
    "@ethersproject/hash": ^5.7.0
    "@ethersproject/keccak256": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/providers": ^5.7.0
    "@ethersproject/rlp": ^5.7.0
    "@ethersproject/transactions": ^5.7.0
    "@ethersproject/web": ^5.7.0
    bufio: ^1.0.7
    chai: ^4.3.4
  checksum: 1c820107c44bdbb46becb1b00fd0dabb44f3ac8f54e6da7872a5a134411fad26f53b193225da55e79d6a8d7f0d01cc16a123db5d41ebaf02ca78360249a4b52a
  languageName: node
  linkType: hard

"@ethereum-waffle/chai@npm:4.0.10":
  version: 4.0.10
  resolution: "@ethereum-waffle/chai@npm:4.0.10"
  dependencies:
    "@ethereum-waffle/provider": 4.0.5
    debug: ^4.3.4
    json-bigint: ^1.0.0
  peerDependencies:
    ethers: "*"
  checksum: 11a2fa51224e98ee4cbf346a731be68b17b3c172e68391b25fc0027545d7477dbeca916bbd10be0fa9de612eaed6115a0578f9d3e312e9fe95af2b7791fd0981
  languageName: node
  linkType: hard

"@ethereum-waffle/compiler@npm:4.0.3":
  version: 4.0.3
  resolution: "@ethereum-waffle/compiler@npm:4.0.3"
  dependencies:
    "@resolver-engine/imports": ^0.3.3
    "@resolver-engine/imports-fs": ^0.3.3
    "@typechain/ethers-v5": ^10.0.0
    "@types/mkdirp": ^0.5.2
    "@types/node-fetch": ^2.6.1
    mkdirp: ^0.5.1
    node-fetch: ^2.6.7
  peerDependencies:
    ethers: "*"
    solc: "*"
    typechain: ^8.0.0
  checksum: ec7839b0f79a40a77fa05bb6941e00b2b3b1e0aa5514a617400c988b302bbbc5e9373b25ba52c7319bb00b83923dce42011384883c7a234c52f8c44eb5b571fc
  languageName: node
  linkType: hard

"@ethereum-waffle/ens@npm:4.0.3":
  version: 4.0.3
  resolution: "@ethereum-waffle/ens@npm:4.0.3"
  peerDependencies:
    "@ensdomains/ens": ^0.4.4
    "@ensdomains/resolver": ^0.2.4
    ethers: "*"
  checksum: 84435c3dda78c416b332c481cfa9322f5bf06168d5887607d320cb30f20283ccde52eabd18adb5791485125d44d99ff7b5f84bd7bbdb1fde86465abb40a81966
  languageName: node
  linkType: hard

"@ethereum-waffle/mock-contract@npm:4.0.4":
  version: 4.0.4
  resolution: "@ethereum-waffle/mock-contract@npm:4.0.4"
  peerDependencies:
    ethers: "*"
  checksum: 45bea2ba4615a0bb81692c3d647ad39c2c37fcf250b577aeb6c5aad03fd4d0912d8d9ef5de638ab276ece1eb9ca1d09d23ed297d96da683fe34e0b01ba631da9
  languageName: node
  linkType: hard

"@ethereum-waffle/provider@npm:4.0.5":
  version: 4.0.5
  resolution: "@ethereum-waffle/provider@npm:4.0.5"
  dependencies:
    "@ethereum-waffle/ens": 4.0.3
    "@ganache/ethereum-options": 0.1.4
    debug: ^4.3.4
    ganache: 7.4.3
  peerDependencies:
    ethers: "*"
  checksum: b1282ea28cbfba05343cc101bceab3f72fdd9ed391f7c7e0f252aead81aec46b6a0ab7d7ca8504e080e306d6554c21e5420765965d92ed5b303a12543dc85cf4
  languageName: node
  linkType: hard

"@ethereumjs/block@npm:^3.5.0, @ethereumjs/block@npm:^3.6.0, @ethereumjs/block@npm:^3.6.2":
  version: 3.6.3
  resolution: "@ethereumjs/block@npm:3.6.3"
  dependencies:
    "@ethereumjs/common": ^2.6.5
    "@ethereumjs/tx": ^3.5.2
    ethereumjs-util: ^7.1.5
    merkle-patricia-tree: ^4.2.4
  checksum: d08c78134d15bc09c08b9a355ab736faa0f6b04ab87d2962e60df9c8bf977ebc68fe10aec6ca50bc2486532f489d7968fb5046defcd839b3b5ce28ca9dbce40f
  languageName: node
  linkType: hard

"@ethereumjs/blockchain@npm:^5.5.0":
  version: 5.5.3
  resolution: "@ethereumjs/blockchain@npm:5.5.3"
  dependencies:
    "@ethereumjs/block": ^3.6.2
    "@ethereumjs/common": ^2.6.4
    "@ethereumjs/ethash": ^1.1.0
    debug: ^4.3.3
    ethereumjs-util: ^7.1.5
    level-mem: ^5.0.1
    lru-cache: ^5.1.1
    semaphore-async-await: ^1.5.1
  checksum: eeefb4735ac06e6fe5ec5457eb9ac7aa26ced8651093d05067aee264f23704d79eacb1b2742e0651b73d2528aa8a9a40f3cc9e479f1837253c2dbb784a7a8e59
  languageName: node
  linkType: hard

"@ethereumjs/common@npm:2.6.0":
  version: 2.6.0
  resolution: "@ethereumjs/common@npm:2.6.0"
  dependencies:
    crc-32: ^1.2.0
    ethereumjs-util: ^7.1.3
  checksum: f1e775f0d3963011f84cd6f6de985b342064331c8fd41bc81a6497abe959078704bf4febd8c59a3fc51c3527b1261441436d55d032f85f0453ff1af4a8dbccb3
  languageName: node
  linkType: hard

"@ethereumjs/common@npm:^2.6.0, @ethereumjs/common@npm:^2.6.4, @ethereumjs/common@npm:^2.6.5":
  version: 2.6.5
  resolution: "@ethereumjs/common@npm:2.6.5"
  dependencies:
    crc-32: ^1.2.0
    ethereumjs-util: ^7.1.5
  checksum: 0143386f267ef01b7a8bb1847596f964ad58643c084e5fd8e3a0271a7bf8428605dbf38cbb92c84f6622080ad095abeb765f178c02d86ec52abf9e8a4c0e4ecf
  languageName: node
  linkType: hard

"@ethereumjs/ethash@npm:^1.1.0":
  version: 1.1.0
  resolution: "@ethereumjs/ethash@npm:1.1.0"
  dependencies:
    "@ethereumjs/block": ^3.5.0
    "@types/levelup": ^4.3.0
    buffer-xor: ^2.0.1
    ethereumjs-util: ^7.1.1
    miller-rabin: ^4.0.0
  checksum: 152bc0850eeb0f2507383ca005418697b0a6a4487b120d7b3fadae4cb3b4781403c96c01f0c47149031431e518fb174c284ff38806b457f86f00c500eb213df3
  languageName: node
  linkType: hard

"@ethereumjs/rlp@npm:^4.0.1":
  version: 4.0.1
  resolution: "@ethereumjs/rlp@npm:4.0.1"
  bin:
    rlp: bin/rlp
  checksum: 30db19c78faa2b6ff27275ab767646929207bb207f903f09eb3e4c273ce2738b45f3c82169ddacd67468b4f063d8d96035f2bf36f02b6b7e4d928eefe2e3ecbc
  languageName: node
  linkType: hard

"@ethereumjs/rlp@npm:^5.0.2":
  version: 5.0.2
  resolution: "@ethereumjs/rlp@npm:5.0.2"
  bin:
    rlp: bin/rlp.cjs
  checksum: b569061ddb1f4cf56a82f7a677c735ba37f9e94e2bbaf567404beb9e2da7aa1f595e72fc12a17c61f7aec67fd5448443efe542967c685a2fe0ffc435793dcbab
  languageName: node
  linkType: hard

"@ethereumjs/tx@npm:3.4.0":
  version: 3.4.0
  resolution: "@ethereumjs/tx@npm:3.4.0"
  dependencies:
    "@ethereumjs/common": ^2.6.0
    ethereumjs-util: ^7.1.3
  checksum: 381cbb872edb0ae83a56bf5d5657ac4f594f43ca0956b6577fb762840033081252345d67151d4feafde3f97caaab9a9826348780553c05d5a8ca2984259ad555
  languageName: node
  linkType: hard

"@ethereumjs/tx@npm:^3.4.0, @ethereumjs/tx@npm:^3.5.2":
  version: 3.5.2
  resolution: "@ethereumjs/tx@npm:3.5.2"
  dependencies:
    "@ethereumjs/common": ^2.6.4
    ethereumjs-util: ^7.1.5
  checksum: a34a7228a623b40300484d15875b9f31f0a612cfeab64a845f6866cf0bfe439519e9455ac6396149f29bc527cf0ee277ace082ae013a1075dcbf7193220a0146
  languageName: node
  linkType: hard

"@ethereumjs/util@npm:^8.1.0":
  version: 8.1.0
  resolution: "@ethereumjs/util@npm:8.1.0"
  dependencies:
    "@ethereumjs/rlp": ^4.0.1
    ethereum-cryptography: ^2.0.0
    micro-ftch: ^0.3.1
  checksum: 9ae5dee8f12b0faf81cd83f06a41560e79b0ba96a48262771d897a510ecae605eb6d84f687da001ab8ccffd50f612ae50f988ef76e6312c752897f462f3ac08d
  languageName: node
  linkType: hard

"@ethereumjs/vm@npm:5.6.0":
  version: 5.6.0
  resolution: "@ethereumjs/vm@npm:5.6.0"
  dependencies:
    "@ethereumjs/block": ^3.6.0
    "@ethereumjs/blockchain": ^5.5.0
    "@ethereumjs/common": ^2.6.0
    "@ethereumjs/tx": ^3.4.0
    async-eventemitter: ^0.2.4
    core-js-pure: ^3.0.1
    debug: ^2.2.0
    ethereumjs-util: ^7.1.3
    functional-red-black-tree: ^1.0.1
    mcl-wasm: ^0.7.1
    merkle-patricia-tree: ^4.2.2
    rustbn.js: ~0.2.0
  checksum: 67f803f7dc851aeed9996cdab6751dc3f7565146ff8a24982526d33a07c173e84dd449b2fbb9202a479c7446b628105af41465b75c1f9143e1e68cf573fed4c3
  languageName: node
  linkType: hard

"@ethersproject/abi@npm:^5.0.9, @ethersproject/abi@npm:^5.1.2, @ethersproject/abi@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abi@npm:5.7.0"
  dependencies:
    "@ethersproject/address": ^5.7.0
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/constants": ^5.7.0
    "@ethersproject/hash": ^5.7.0
    "@ethersproject/keccak256": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/strings": ^5.7.0
  checksum: bc6962bb6cb854e4d2a4d65b2c49c716477675b131b1363312234bdbb7e19badb7d9ce66f4ca2a70ae2ea84f7123dbc4e300a1bfe5d58864a7eafabc1466627e
  languageName: node
  linkType: hard

"@ethersproject/abstract-provider@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-provider@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/networks": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/transactions": ^5.7.0
    "@ethersproject/web": ^5.7.0
  checksum: 74cf4696245cf03bb7cc5b6cbf7b4b89dd9a79a1c4688126d214153a938126d4972d42c93182198653ce1de35f2a2cad68be40337d4774b3698a39b28f0228a8
  languageName: node
  linkType: hard

"@ethersproject/abstract-signer@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-signer@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-provider": ^5.7.0
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/properties": ^5.7.0
  checksum: a823dac9cfb761e009851050ebebd5b229d1b1cc4a75b125c2da130ff37e8218208f7f9d1386f77407705b889b23d4a230ad67185f8872f083143e0073cbfbe3
  languageName: node
  linkType: hard

"@ethersproject/address@npm:5.6.1":
  version: 5.6.1
  resolution: "@ethersproject/address@npm:5.6.1"
  dependencies:
    "@ethersproject/bignumber": ^5.6.2
    "@ethersproject/bytes": ^5.6.1
    "@ethersproject/keccak256": ^5.6.1
    "@ethersproject/logger": ^5.6.0
    "@ethersproject/rlp": ^5.6.1
  checksum: 262096ef05a1b626c161a72698a5d8b06aebf821fe01a1651ab40f80c29ca2481b96be7f972745785fd6399906509458c4c9a38f3bc1c1cb5afa7d2f76f7309a
  languageName: node
  linkType: hard

"@ethersproject/address@npm:^5.0.2, @ethersproject/address@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/address@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/keccak256": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/rlp": ^5.7.0
  checksum: 64ea5ebea9cc0e845c413e6cb1e54e157dd9fc0dffb98e239d3a3efc8177f2ff798cd4e3206cf3660ee8faeb7bef1a47dc0ebef0d7b132c32e61e550c7d4c843
  languageName: node
  linkType: hard

"@ethersproject/base64@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/base64@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
  checksum: 7dd5d734d623582f08f665434f53685041a3d3b334a0e96c0c8afa8bbcaab934d50e5b6b980e826a8fde8d353e0b18f11e61faf17468177274b8e7c69cd9742b
  languageName: node
  linkType: hard

"@ethersproject/basex@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/basex@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/properties": ^5.7.0
  checksum: 326087b7e1f3787b5fe6cd1cf2b4b5abfafbc355a45e88e22e5e9d6c845b613ffc5301d629b28d5c4d5e2bfe9ec424e6782c804956dff79be05f0098cb5817de
  languageName: node
  linkType: hard

"@ethersproject/bignumber@npm:^5.6.2, @ethersproject/bignumber@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bignumber@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    bn.js: ^5.2.1
  checksum: 8c9a134b76f3feb4ec26a5a27379efb4e156b8fb2de0678a67788a91c7f4e30abe9d948638458e4b20f2e42380da0adacc7c9389d05fce070692edc6ae9b4904
  languageName: node
  linkType: hard

"@ethersproject/bytes@npm:^5.6.1, @ethersproject/bytes@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bytes@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": ^5.7.0
  checksum: 66ad365ceaab5da1b23b72225c71dce472cf37737af5118181fa8ab7447d696bea15ca22e3a0e8836fdd8cfac161afe321a7c67d0dde96f9f645ddd759676621
  languageName: node
  linkType: hard

"@ethersproject/constants@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/constants@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": ^5.7.0
  checksum: 6d4b1355747cce837b3e76ec3bde70e4732736f23b04f196f706ebfa5d4d9c2be50904a390d4d40ce77803b98d03d16a9b6898418e04ba63491933ce08c4ba8a
  languageName: node
  linkType: hard

"@ethersproject/contracts@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/contracts@npm:5.7.0"
  dependencies:
    "@ethersproject/abi": ^5.7.0
    "@ethersproject/abstract-provider": ^5.7.0
    "@ethersproject/abstract-signer": ^5.7.0
    "@ethersproject/address": ^5.7.0
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/constants": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/transactions": ^5.7.0
  checksum: 6ccf1121cba01b31e02f8c507cb971ab6bfed85706484a9ec09878ef1594a62215f43c4fdef8f4a4875b99c4a800bc95e3be69b1803f8ce479e07634b5a740c0
  languageName: node
  linkType: hard

"@ethersproject/hash@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/hash@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": ^5.7.0
    "@ethersproject/address": ^5.7.0
    "@ethersproject/base64": ^5.7.0
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/keccak256": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/strings": ^5.7.0
  checksum: 6e9fa8d14eb08171cd32f17f98cc108ec2aeca74a427655f0d689c550fee0b22a83b3b400fad7fb3f41cf14d4111f87f170aa7905bcbcd1173a55f21b06262ef
  languageName: node
  linkType: hard

"@ethersproject/keccak256@npm:^5.6.1, @ethersproject/keccak256@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/keccak256@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    js-sha3: 0.8.0
  checksum: ff70950d82203aab29ccda2553422cbac2e7a0c15c986bd20a69b13606ed8bb6e4fdd7b67b8d3b27d4f841e8222cbaccd33ed34be29f866fec7308f96ed244c6
  languageName: node
  linkType: hard

"@ethersproject/logger@npm:^5.6.0, @ethersproject/logger@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/logger@npm:5.7.0"
  checksum: 075ab2f605f1fd0813f2e39c3308f77b44a67732b36e712d9bc085f22a84aac4da4f71b39bee50fe78da3e1c812673fadc41180c9970fe5e486e91ea17befe0d
  languageName: node
  linkType: hard

"@ethersproject/networks@npm:^5.7.0":
  version: 5.7.1
  resolution: "@ethersproject/networks@npm:5.7.1"
  dependencies:
    "@ethersproject/logger": ^5.7.0
  checksum: 0339f312304c17d9a0adce550edb825d4d2c8c9468c1634c44172c67a9ed256f594da62c4cda5c3837a0f28b7fabc03aca9b492f68ff1fdad337ee861b27bd5d
  languageName: node
  linkType: hard

"@ethersproject/properties@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/properties@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": ^5.7.0
  checksum: 6ab0ccf0c3aadc9221e0cdc5306ce6cd0df7f89f77d77bccdd1277182c9ead0202cd7521329ba3acde130820bf8af299e17cf567d0d497c736ee918207bbf59f
  languageName: node
  linkType: hard

"@ethersproject/providers@npm:^5.7.0":
  version: 5.7.2
  resolution: "@ethersproject/providers@npm:5.7.2"
  dependencies:
    "@ethersproject/abstract-provider": ^5.7.0
    "@ethersproject/abstract-signer": ^5.7.0
    "@ethersproject/address": ^5.7.0
    "@ethersproject/base64": ^5.7.0
    "@ethersproject/basex": ^5.7.0
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/constants": ^5.7.0
    "@ethersproject/hash": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/networks": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/random": ^5.7.0
    "@ethersproject/rlp": ^5.7.0
    "@ethersproject/sha2": ^5.7.0
    "@ethersproject/strings": ^5.7.0
    "@ethersproject/transactions": ^5.7.0
    "@ethersproject/web": ^5.7.0
    bech32: 1.1.4
    ws: 7.4.6
  checksum: 1754c731a5ca6782ae9677f4a9cd8b6246c4ef21a966c9a01b133750f3c578431ec43ec254e699969c4a0f87e84463ded50f96b415600aabd37d2056aee58c19
  languageName: node
  linkType: hard

"@ethersproject/random@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/random@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
  checksum: 017829c91cff6c76470852855108115b0b52c611b6be817ed1948d56ba42d6677803ec2012aa5ae298a7660024156a64c11fcf544e235e239ab3f89f0fff7345
  languageName: node
  linkType: hard

"@ethersproject/rlp@npm:^5.6.1, @ethersproject/rlp@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/rlp@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
  checksum: bce165b0f7e68e4d091c9d3cf47b247cac33252df77a095ca4281d32d5eeaaa3695d9bc06b2b057c5015353a68df89f13a4a54a72e888e4beeabbe56b15dda6e
  languageName: node
  linkType: hard

"@ethersproject/sha2@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/sha2@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    hash.js: 1.1.7
  checksum: 09321057c022effbff4cc2d9b9558228690b5dd916329d75c4b1ffe32ba3d24b480a367a7cc92d0f0c0b1c896814d03351ae4630e2f1f7160be2bcfbde435dbc
  languageName: node
  linkType: hard

"@ethersproject/signing-key@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/signing-key@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    bn.js: ^5.2.1
    elliptic: 6.5.4
    hash.js: 1.1.7
  checksum: 8f8de09b0aac709683bbb49339bc0a4cd2f95598f3546436c65d6f3c3a847ffa98e06d35e9ed2b17d8030bd2f02db9b7bd2e11c5cf8a71aad4537487ab4cf03a
  languageName: node
  linkType: hard

"@ethersproject/strings@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/strings@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/constants": ^5.7.0
    "@ethersproject/logger": ^5.7.0
  checksum: 5ff78693ae3fdf3cf23e1f6dc047a61e44c8197d2408c42719fef8cb7b7b3613a4eec88ac0ed1f9f5558c74fe0de7ae3195a29ca91a239c74b9f444d8e8b50df
  languageName: node
  linkType: hard

"@ethersproject/transactions@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/transactions@npm:5.7.0"
  dependencies:
    "@ethersproject/address": ^5.7.0
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/constants": ^5.7.0
    "@ethersproject/keccak256": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/rlp": ^5.7.0
    "@ethersproject/signing-key": ^5.7.0
  checksum: a31b71996d2b283f68486241bff0d3ea3f1ba0e8f1322a8fffc239ccc4f4a7eb2ea9994b8fd2f093283fd75f87bae68171e01b6265261f821369aca319884a79
  languageName: node
  linkType: hard

"@ethersproject/units@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/units@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": ^5.7.0
    "@ethersproject/constants": ^5.7.0
    "@ethersproject/logger": ^5.7.0
  checksum: 304714f848cd32e57df31bf545f7ad35c2a72adae957198b28cbc62166daa929322a07bff6e9c9ac4577ab6aa0de0546b065ed1b2d20b19e25748b7d475cb0fc
  languageName: node
  linkType: hard

"@ethersproject/web@npm:^5.7.0":
  version: 5.7.1
  resolution: "@ethersproject/web@npm:5.7.1"
  dependencies:
    "@ethersproject/base64": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/logger": ^5.7.0
    "@ethersproject/properties": ^5.7.0
    "@ethersproject/strings": ^5.7.0
  checksum: 7028c47103f82fd2e2c197ce0eecfacaa9180ffeec7de7845b1f4f9b19d84081b7a48227aaddde05a4aaa526af574a9a0ce01cc0fc75e3e371f84b38b5b16b2b
  languageName: node
  linkType: hard

"@fastify/busboy@npm:^2.0.0":
  version: 2.1.1
  resolution: "@fastify/busboy@npm:2.1.1"
  checksum: 42c32ef75e906c9a4809c1e1930a5ca6d4ddc8d138e1a8c8ba5ea07f997db32210617d23b2e4a85fe376316a41a1a0439fc6ff2dedf5126d96f45a9d80754fb2
  languageName: node
  linkType: hard

"@ganache/ethereum-address@npm:0.1.4":
  version: 0.1.4
  resolution: "@ganache/ethereum-address@npm:0.1.4"
  dependencies:
    "@ganache/utils": 0.1.4
  checksum: 0e07102c67aa2c25233e30f8ca359cbd012ad0e0b9580b8e6aff6f87dd46daee89bbc60d9518f2204f36f943e7bd5d20e571646c85e6a68a98784c3ede2ccec0
  languageName: node
  linkType: hard

"@ganache/ethereum-options@npm:0.1.4":
  version: 0.1.4
  resolution: "@ganache/ethereum-options@npm:0.1.4"
  dependencies:
    "@ganache/ethereum-address": 0.1.4
    "@ganache/ethereum-utils": 0.1.4
    "@ganache/options": 0.1.4
    "@ganache/utils": 0.1.4
    bip39: 3.0.4
    seedrandom: 3.0.5
  checksum: 597b32502654ce9f3fec02fdf308f1e6b2c657b5e25c6d4219d71ecbea94e45faa4135c6d4e25cacf1faac1509f2b7546f3d8bbdba1e9121975d820999d3aa76
  languageName: node
  linkType: hard

"@ganache/ethereum-utils@npm:0.1.4":
  version: 0.1.4
  resolution: "@ganache/ethereum-utils@npm:0.1.4"
  dependencies:
    "@ethereumjs/common": 2.6.0
    "@ethereumjs/tx": 3.4.0
    "@ethereumjs/vm": 5.6.0
    "@ganache/ethereum-address": 0.1.4
    "@ganache/rlp": 0.1.4
    "@ganache/utils": 0.1.4
    emittery: 0.10.0
    ethereumjs-abi: 0.6.8
    ethereumjs-util: 7.1.3
  checksum: 74bc7b7fb62e9dc2f114f6f428c465d9365c0bb84c85144d34935d24a2893c5471c8cae263ae4bea51f1335803b6b1771f57c25a276e9c0539a08a90c3289bca
  languageName: node
  linkType: hard

"@ganache/options@npm:0.1.4":
  version: 0.1.4
  resolution: "@ganache/options@npm:0.1.4"
  dependencies:
    "@ganache/utils": 0.1.4
    bip39: 3.0.4
    seedrandom: 3.0.5
  checksum: 4f1cda646f5599b18a0eeaf27286499c78c3e2e0eb2541db756c27f2ca16e58539c9db99aab8441e593f9fe586269940d800900f63312fcb10231ab95ebdc3dc
  languageName: node
  linkType: hard

"@ganache/rlp@npm:0.1.4":
  version: 0.1.4
  resolution: "@ganache/rlp@npm:0.1.4"
  dependencies:
    "@ganache/utils": 0.1.4
    rlp: 2.2.6
  checksum: 425c5d4a0a36b834cfac005d05679b3df970a6d8d953f385a5f9bd003a326ba23c1bce06095729879136699b70389d7f2627f939f712ed8897c96e542766771f
  languageName: node
  linkType: hard

"@ganache/utils@npm:0.1.4":
  version: 0.1.4
  resolution: "@ganache/utils@npm:0.1.4"
  dependencies:
    "@trufflesuite/bigint-buffer": 1.1.9
    emittery: 0.10.0
    keccak: 3.0.1
    seedrandom: 3.0.5
  dependenciesMeta:
    "@trufflesuite/bigint-buffer":
      optional: true
  checksum: c81b7380d56292a44e9cdb0e581d815b9510fb1be87661fe135e2d3611b495f1cd01aa4b91e287bfc28d08d1b4b4210f7b54e32ea28e054faa006cf232fb3dcc
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.13.0":
  version: 0.13.0
  resolution: "@humanwhocodes/config-array@npm:0.13.0"
  dependencies:
    "@humanwhocodes/object-schema": ^2.0.3
    debug: ^4.3.1
    minimatch: ^3.0.5
  checksum: eae69ff9134025dd2924f0b430eb324981494be26f0fddd267a33c28711c4db643242cf9fddf7dadb9d16c96b54b2d2c073e60a56477df86e0173149313bd5d6
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.3":
  version: 2.0.3
  resolution: "@humanwhocodes/object-schema@npm:2.0.3"
  checksum: d3b78f6c5831888c6ecc899df0d03bcc25d46f3ad26a11d7ea52944dc36a35ef543fad965322174238d677a43d5c694434f6607532cff7077062513ad7022631
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 05df4f2538b3b0f998ea4c1cd34574d0feba216fa5d4ccaef0187d12abf82eafe6021cec8b49f9bb4d90f2ba4582ccc581e72986a5fcf4176ae0cfeb04cf52ec
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": ^3.0.3
    "@jridgewell/sourcemap-codec": ^1.4.10
  checksum: d89597752fd88d3f3480845691a05a44bd21faac18e2185b6f436c3b0fd0c5a859fbbd9aaa92050c4052caf325ad3e10e2e1d1b64327517471b7d51babc0ddef
  languageName: node
  linkType: hard

"@manypkg/find-root@npm:^1.1.0":
  version: 1.1.0
  resolution: "@manypkg/find-root@npm:1.1.0"
  dependencies:
    "@babel/runtime": ^7.5.5
    "@types/node": ^12.7.1
    find-up: ^4.1.0
    fs-extra: ^8.1.0
  checksum: f0fd881a5a81a351cb6561cd24117e8ee9481bbf3b6d1c7d9d10bef1f4744ca2ba3d064713e83c0a0574416d1e5b4a4c6c414aad91913c4a1c6040d87283ac50
  languageName: node
  linkType: hard

"@manypkg/get-packages@npm:^1.1.3":
  version: 1.1.3
  resolution: "@manypkg/get-packages@npm:1.1.3"
  dependencies:
    "@babel/runtime": ^7.5.5
    "@changesets/types": ^4.0.1
    "@manypkg/find-root": ^1.1.0
    fs-extra: ^8.1.0
    globby: ^11.0.0
    read-yaml-file: ^1.1.0
  checksum: f5a756e5a659e0e1c33f48852d56826d170d5b10a3cdea89ce4fcaa77678d8799aa4004b30e1985c87b73dbc390b95bb6411b78336dd1e0db87c08c74b5c0e74
  languageName: node
  linkType: hard

"@metamask/eth-sig-util@npm:^4.0.0":
  version: 4.0.1
  resolution: "@metamask/eth-sig-util@npm:4.0.1"
  dependencies:
    ethereumjs-abi: ^0.6.8
    ethereumjs-util: ^6.2.1
    ethjs-util: ^0.1.6
    tweetnacl: ^1.0.3
    tweetnacl-util: ^0.15.1
  checksum: 740df4c92a1282e6be4c00c86c1a8ccfb93e767596e43f6da895aa5bab4a28fc3c2209f0327db34924a4a1e9db72bc4d3dddfcfc45cca0b218c9ccbf7d1b1445
  languageName: node
  linkType: hard

"@noble/curves@npm:1.2.0":
  version: 1.2.0
  resolution: "@noble/curves@npm:1.2.0"
  dependencies:
    "@noble/hashes": 1.3.2
  checksum: bb798d7a66d8e43789e93bc3c2ddff91a1e19fdb79a99b86cd98f1e5eff0ee2024a2672902c2576ef3577b6f282f3b5c778bebd55761ddbb30e36bf275e83dd0
  languageName: node
  linkType: hard

"@noble/curves@npm:1.4.2, @noble/curves@npm:~1.4.0":
  version: 1.4.2
  resolution: "@noble/curves@npm:1.4.2"
  dependencies:
    "@noble/hashes": 1.4.0
  checksum: c475a83c4263e2c970eaba728895b9b5d67e0ca880651e9c6e3efdc5f6a4f07ceb5b043bf71c399fc80fada0b8706e69d0772bffdd7b9de2483b988973a34cba
  languageName: node
  linkType: hard

"@noble/curves@npm:1.8.2, @noble/curves@npm:~1.8.1":
  version: 1.8.2
  resolution: "@noble/curves@npm:1.8.2"
  dependencies:
    "@noble/hashes": 1.7.2
  checksum: f26fd77b4d78fe26dba2754cbcaddee5da23a711a0c9778ee57764eb0084282d97659d9b0a760718f42493adf68665dbffdca9d6213950f03f079d09c465c096
  languageName: node
  linkType: hard

"@noble/curves@npm:^1.3.0":
  version: 1.8.1
  resolution: "@noble/curves@npm:1.8.1"
  dependencies:
    "@noble/hashes": 1.7.1
  checksum: 4143f1248ed57c1ae46dfef5c692a91383e5830420b9c72d3ff1061aa9ebbf8999297da6d2aed8a9716fef8e6b1f5a45737feeab02abf55ca2a4f514bf9339ec
  languageName: node
  linkType: hard

"@noble/curves@npm:^1.6.0, @noble/curves@npm:~1.9.0":
  version: 1.9.0
  resolution: "@noble/curves@npm:1.9.0"
  dependencies:
    "@noble/hashes": 1.8.0
  checksum: dc82f8c095e90ab0d88f4bfcdf3a6f8500444dac3644b02d7b81a268f516573f18fae66aa3030f5e8886d86dff63dd57a5bb639d968d6cdfadae10436680840c
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.2.0, @noble/hashes@npm:~1.2.0":
  version: 1.2.0
  resolution: "@noble/hashes@npm:1.2.0"
  checksum: 8ca080ce557b8f40fb2f78d3aedffd95825a415ac8e13d7ffe3643f8626a8c2d99a3e5975b555027ac24316d8b3c02a35b8358567c0c23af681e6573602aa434
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.3.2":
  version: 1.3.2
  resolution: "@noble/hashes@npm:1.3.2"
  checksum: fe23536b436539d13f90e4b9be843cc63b1b17666a07634a2b1259dded6f490be3d050249e6af98076ea8f2ea0d56f578773c2197f2aa0eeaa5fba5bc18ba474
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.4.0, @noble/hashes@npm:~1.4.0":
  version: 1.4.0
  resolution: "@noble/hashes@npm:1.4.0"
  checksum: 8ba816ae26c90764b8c42493eea383716396096c5f7ba6bea559993194f49d80a73c081f315f4c367e51bd2d5891700bcdfa816b421d24ab45b41cb03e4f3342
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.1, @noble/hashes@npm:^1.3.3":
  version: 1.7.1
  resolution: "@noble/hashes@npm:1.7.1"
  checksum: 4f1b56428a10323feef17e4f437c9093556cb18db06f94d254043fadb69c3da8475f96eb3f8322d41e8670117d7486475a8875e68265c2839f60fd03edd6a616
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.2, @noble/hashes@npm:~1.7.1":
  version: 1.7.2
  resolution: "@noble/hashes@npm:1.7.2"
  checksum: f9e3c2e62c2850073f8d6ac30cc33b03a25cae859eb2209b33ae90ed3d1e003cb2a1ddacd2aacd6b7c98a5ad70795a234ccce04b0526657cd8020ce4ffdb491f
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.8.0, @noble/hashes@npm:^1.5.0, @noble/hashes@npm:~1.8.0":
  version: 1.8.0
  resolution: "@noble/hashes@npm:1.8.0"
  checksum: c94e98b941963676feaba62475b1ccfa8341e3f572adbb3b684ee38b658df44100187fa0ef4220da580b13f8d27e87d5492623c8a02ecc61f23fb9960c7918f5
  languageName: node
  linkType: hard

"@noble/hashes@npm:^1.4.0":
  version: 1.6.1
  resolution: "@noble/hashes@npm:1.6.1"
  checksum: 57c62f65ee217c0293b4321b547792aa6d79812bfe70a7d62dc83e0f936cc677b14ed981b4e88cf8fdad37cd6d3a0cbd3bd0908b0728adc9daf066e678be8901
  languageName: node
  linkType: hard

"@noble/secp256k1@npm:1.7.1, @noble/secp256k1@npm:~1.7.0":
  version: 1.7.1
  resolution: "@noble/secp256k1@npm:1.7.1"
  checksum: d2301f1f7690368d8409a3152450458f27e54df47e3f917292de3de82c298770890c2de7c967d237eff9c95b70af485389a9695f73eb05a43e2bd562d18b18cb
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@nomicfoundation/edr-darwin-arm64@npm:0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr-darwin-arm64@npm:0.7.0"
  checksum: 15541029227f65df2cf1df93343f6c8e9484494ddf26cf12289cea7410446ce5b2ebb9ac750a7d438276c427da7678aba0fd8fda33ed7c1e8c743955fb657055
  languageName: node
  linkType: hard

"@nomicfoundation/edr-darwin-x64@npm:0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr-darwin-x64@npm:0.7.0"
  checksum: 4b7cbbe6e6b0484805814ca7f2151de780dfc6102d443126666a4e5a19955343cb6e709c0f99b3a80b0b07fce50d5c2cee9958c516a1c2b0f2ac568a30db1712
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-arm64-gnu@npm:0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr-linux-arm64-gnu@npm:0.7.0"
  checksum: 836786bbe5f9ee9fce220c7177fd5f6565b4049e42ca8163d2676c01b211aa7c688d6662cf2e469ba44147bfeaf2152f5cc168aaba171af1507477511f3289cc
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-arm64-musl@npm:0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr-linux-arm64-musl@npm:0.7.0"
  checksum: 005faee82c11965430a72eaa8a55d87db0ea8b149b6d0f483b505e099152c7148a959412c387e143efc0fd51fb2221dae4e1d7004c83a9f323819db1049713f7
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-x64-gnu@npm:0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr-linux-x64-gnu@npm:0.7.0"
  checksum: 18b60a0bbb7ad35240fc5f4bbe62cd83cdbc0c3e6a513d3bca3c291874d9cb02da7eb55ec3af0d8e273060347d588f80b7fbf304704c2c4cf9f7b302d4d1b466
  languageName: node
  linkType: hard

"@nomicfoundation/edr-linux-x64-musl@npm:0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr-linux-x64-musl@npm:0.7.0"
  checksum: 84e4cc834f71db02e21ea218919e9cec67983894372c232d5f36519d2d5579268d0019eb4f548826bfd1677867c78a0db81fce26bced330b375da68c91458a38
  languageName: node
  linkType: hard

"@nomicfoundation/edr-win32-x64-msvc@npm:0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr-win32-x64-msvc@npm:0.7.0"
  checksum: 9f070a202cecab2d1f28353e5d90cd113ef98b9473169c83b97ab140898bd47a301a0cae69733346fe4cc57c31aa6f3796ab3280f1316aa79d561fecd00cc222
  languageName: node
  linkType: hard

"@nomicfoundation/edr@npm:^0.7.0":
  version: 0.7.0
  resolution: "@nomicfoundation/edr@npm:0.7.0"
  dependencies:
    "@nomicfoundation/edr-darwin-arm64": 0.7.0
    "@nomicfoundation/edr-darwin-x64": 0.7.0
    "@nomicfoundation/edr-linux-arm64-gnu": 0.7.0
    "@nomicfoundation/edr-linux-arm64-musl": 0.7.0
    "@nomicfoundation/edr-linux-x64-gnu": 0.7.0
    "@nomicfoundation/edr-linux-x64-musl": 0.7.0
    "@nomicfoundation/edr-win32-x64-msvc": 0.7.0
  checksum: 83c54e2e2815c57ce56262f1010a0c5a2917b366bcf0acfad7662cbf2ccf46fd281e66c6db67bca9db7d91f699254216708045e882fda08c81361f111a07cb48
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-common@npm:4.0.4":
  version: 4.0.4
  resolution: "@nomicfoundation/ethereumjs-common@npm:4.0.4"
  dependencies:
    "@nomicfoundation/ethereumjs-util": 9.0.4
  checksum: ce3f6e4ae15b976efdb7ccda27e19aadb62b5ffee209f9503e68b4fd8633715d4d697c0cc10ccd35f5e4e977edd05100d0f214e28880ec64fff77341dc34fcdf
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-rlp@npm:5.0.4":
  version: 5.0.4
  resolution: "@nomicfoundation/ethereumjs-rlp@npm:5.0.4"
  bin:
    rlp: bin/rlp.cjs
  checksum: ee2c2e5776c73801dc5ed636f4988b599b4563c2d0037da542ea57eb237c69dd1ac555f6bcb5e06f70515b6459779ba0d68252a6e105132b4659ab4bf62919b0
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-tx@npm:5.0.4":
  version: 5.0.4
  resolution: "@nomicfoundation/ethereumjs-tx@npm:5.0.4"
  dependencies:
    "@nomicfoundation/ethereumjs-common": 4.0.4
    "@nomicfoundation/ethereumjs-rlp": 5.0.4
    "@nomicfoundation/ethereumjs-util": 9.0.4
    ethereum-cryptography: 0.1.3
  peerDependencies:
    c-kzg: ^2.1.2
  peerDependenciesMeta:
    c-kzg:
      optional: true
  checksum: 0f1c87716682ccbcf4d92ffc6cf8ab557e658b90319d82be3219a091a736859f8803c73c98e4863682e3e86d264751c472d33ff6d3c3daf4e75b5f01d0af8fa3
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-util@npm:9.0.4":
  version: 9.0.4
  resolution: "@nomicfoundation/ethereumjs-util@npm:9.0.4"
  dependencies:
    "@nomicfoundation/ethereumjs-rlp": 5.0.4
    ethereum-cryptography: 0.1.3
  peerDependencies:
    c-kzg: ^2.1.2
  peerDependenciesMeta:
    c-kzg:
      optional: true
  checksum: 754439f72b11cad2d8986707ad020077dcc763c4055f73e2668a0b4cadb22aa4407faa9b3c587d9eb5b97ac337afbe037eb642bc1d5a16197284f83db3462cbe
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-chai-matchers@npm:^2.0.4":
  version: 2.0.8
  resolution: "@nomicfoundation/hardhat-chai-matchers@npm:2.0.8"
  dependencies:
    "@types/chai-as-promised": ^7.1.3
    chai-as-promised: ^7.1.1
    deep-eql: ^4.0.1
    ordinal: ^1.0.3
  peerDependencies:
    "@nomicfoundation/hardhat-ethers": ^3.0.0
    chai: ^4.2.0
    ethers: ^6.1.0
    hardhat: ^2.9.4
  checksum: bcf2efcf98e1e889e4566b3ff23099313c67a4c765367f702672890e0d3e6f38ad8de415ee6e9f65d038f6dcd879cc080ca0dda07109acc7d3fc249e8fdb79f5
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-ethers@npm:^3.0.8":
  version: 3.0.8
  resolution: "@nomicfoundation/hardhat-ethers@npm:3.0.8"
  dependencies:
    debug: ^4.1.1
    lodash.isequal: ^4.5.0
  peerDependencies:
    ethers: ^6.1.0
    hardhat: ^2.0.0
  checksum: 6ad6da6713fa25e653cef894ec10762fc3d728a50461a63c169eac248b5b1ea81bb3d42e8017601bbd231c9fee034336e1f2dc25375d5dcf9926ec4d4389034a
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-ignition-ethers@npm:0.15.0":
  version: 0.15.0
  resolution: "@nomicfoundation/hardhat-ignition-ethers@npm:0.15.0"
  peerDependencies:
    "@nomicfoundation/hardhat-ethers": ^3.0.4
    "@nomicfoundation/hardhat-ignition": ^0.15.0
    "@nomicfoundation/ignition-core": ^0.15.0
    ethers: ^6.7.0
    hardhat: ^2.18.0
  checksum: ff5ca46ff56b53bb4614a91ec0bae06437ad3902ca2944370130b41c0e16c99425a72e27e20d1aa842d61546625f5dc37cb177727cebaba39f654b4b108356eb
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-ignition@npm:^0.15.9":
  version: 0.15.9
  resolution: "@nomicfoundation/hardhat-ignition@npm:0.15.9"
  dependencies:
    "@nomicfoundation/ignition-core": ^0.15.9
    "@nomicfoundation/ignition-ui": ^0.15.9
    chalk: ^4.0.0
    debug: ^4.3.2
    fs-extra: ^10.0.0
    json5: ^2.2.3
    prompts: ^2.4.2
  peerDependencies:
    "@nomicfoundation/hardhat-verify": ^2.0.1
    hardhat: ^2.18.0
  checksum: f658b4cc7d82d0b2d10853262a3233228a1ad16f70fa39b1d9146717748840e38a157fb494e8e66c57e6fd09b9535b8693713f2fa89e4298e8567ab91d08a795
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-network-helpers@npm:^1.0.12":
  version: 1.0.12
  resolution: "@nomicfoundation/hardhat-network-helpers@npm:1.0.12"
  dependencies:
    ethereumjs-util: ^7.1.4
  peerDependencies:
    hardhat: ^2.9.5
  checksum: 7e1b91789dd4e73464b4eec919b1e67c6d482dd7534f4f7cae73fb5bdddd69f2a47143754b34385b098a1df0f4875cd4d2e1109fc3d847db76f4b0a9a44bd959
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-toolbox@npm:^5.0.0":
  version: 5.0.0
  resolution: "@nomicfoundation/hardhat-toolbox@npm:5.0.0"
  peerDependencies:
    "@nomicfoundation/hardhat-chai-matchers": ^2.0.0
    "@nomicfoundation/hardhat-ethers": ^3.0.0
    "@nomicfoundation/hardhat-ignition-ethers": ^0.15.0
    "@nomicfoundation/hardhat-network-helpers": ^1.0.0
    "@nomicfoundation/hardhat-verify": ^2.0.0
    "@typechain/ethers-v6": ^0.5.0
    "@typechain/hardhat": ^9.0.0
    "@types/chai": ^4.2.0
    "@types/mocha": ">=9.1.0"
    "@types/node": ">=18.0.0"
    chai: ^4.2.0
    ethers: ^6.4.0
    hardhat: ^2.11.0
    hardhat-gas-reporter: ^1.0.8
    solidity-coverage: ^0.8.1
    ts-node: ">=8.0.0"
    typechain: ^8.3.0
    typescript: ">=4.5.0"
  checksum: 18890eaf1cc130afb7dc83ea48cb6ef23c499eb5d28c3fbb36e706082383a320118ee6d4491ede64acf684d2f1ffa117cf84ad80d8ebde9fa52a443f8780a898
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-verify@npm:^2.0.12":
  version: 2.0.12
  resolution: "@nomicfoundation/hardhat-verify@npm:2.0.12"
  dependencies:
    "@ethersproject/abi": ^5.1.2
    "@ethersproject/address": ^5.0.2
    cbor: ^8.1.0
    debug: ^4.1.1
    lodash.clonedeep: ^4.5.0
    picocolors: ^1.1.0
    semver: ^6.3.0
    table: ^6.8.0
    undici: ^5.14.0
  peerDependencies:
    hardhat: ^2.0.4
  checksum: 510f9300e3dc92943dcbaab6eb26a71aa54eafd5579b4b911c1109fc5cca87a3db2dc53f7e5d56edcc729123c14786f235fcd8a513350ec96b491eb687d28409
  languageName: node
  linkType: hard

"@nomicfoundation/hardhat-web3-v4@npm:^1.0.0":
  version: 1.0.0
  resolution: "@nomicfoundation/hardhat-web3-v4@npm:1.0.0"
  peerDependencies:
    hardhat: ^2.0.0
    web3: ^4.0.1
  checksum: 870f363213f038e472fbdd60cb8dd14ec003db98b55c357d329807f4cab108480650825456857ac6b633ef43e98d4a496adef48a507a367b216e75b271382baf
  languageName: node
  linkType: hard

"@nomicfoundation/ignition-core@npm:^0.15.9":
  version: 0.15.9
  resolution: "@nomicfoundation/ignition-core@npm:0.15.9"
  dependencies:
    "@ethersproject/address": 5.6.1
    "@nomicfoundation/solidity-analyzer": ^0.1.1
    cbor: ^9.0.0
    debug: ^4.3.2
    ethers: ^6.7.0
    fs-extra: ^10.0.0
    immer: 10.0.2
    lodash: 4.17.21
    ndjson: 2.0.0
  checksum: 036a7bce250004376a2486f5861ea003b6ff9231fc0a89bb772dcad0b9efd60df57d88df322bcf122a9d31746b639956f5b4e6d81486cf2337b78bf178105bf2
  languageName: node
  linkType: hard

"@nomicfoundation/ignition-ui@npm:^0.15.9":
  version: 0.15.9
  resolution: "@nomicfoundation/ignition-ui@npm:0.15.9"
  checksum: 1ab2e2e3561159bc582e5922688a9bef874ab55b494d009960589f0a1b2daa8fe0b2fe4c2ea1270aaa74601428093c374d5321cdf40ae45318a26183ffce3074
  languageName: node
  linkType: hard

"@nomicfoundation/slang@npm:^0.18.3":
  version: 0.18.3
  resolution: "@nomicfoundation/slang@npm:0.18.3"
  dependencies:
    "@bytecodealliance/preview2-shim": 0.17.0
  checksum: 3b562ae0fb4ac292699069620e94e0b5338a84a574407a23d70d4f202dd40163b17dd341fe2f3810536bd1ac8e96f78ab6a35a1f0bc64d386d572b2511c791e7
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-darwin-arm64@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-darwin-arm64@npm:0.1.2"
  checksum: 5bf3cf3f88e39d7b684f0ca75621b794b62e2676eb63c6977e847acc9c827bdc132143cc84e46be2797b93edc522f2c6f85bf5501fd7b8c85b346fb27e4dd488
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-darwin-x64@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-darwin-x64@npm:0.1.2"
  checksum: 8061dc7749d97409ccde4a2e529316c29f83f2d07c78ffea87803777229e2a7d967bbb8bda564903ab5e9e89ad3b46cbcb060621209d1c6e4212c4b1b096c076
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-arm64-gnu@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-arm64-gnu@npm:0.1.2"
  checksum: 46111d18446ea5d157628c202d1ee1fc3444b32a0e3aa24337bbb407653606a79a3b199bf1e5fe5f74c5c78833cf243e492f20ab6a1503137e89f2236b3ecfe7
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-arm64-musl@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-arm64-musl@npm:0.1.2"
  checksum: 588e81e7b36cbe80b9d2c502dc2db4bf8706732bcea6906b79bac202eb441fa2f4b9f703c30d82a17ed2a4402eaf038057fb14fc1c16eac5ade103ff9b085cdc
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-x64-gnu@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-x64-gnu@npm:0.1.2"
  checksum: 26f8307bde4a2c7609d297f2af6a50cad87aa46e914326b09d5cb424b4f45f0f75e982f9fcb9ee3361a2f9b141fcc9c10a665ddbc9686e01b017c639fbfb500b
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-x64-musl@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-linux-x64-musl@npm:0.1.2"
  checksum: d3628bae4f04bcdb2f1dec1d6790cdf97812e7e5c0a426f4227acc97883fa3165017a800375237e36bc588f0fb4971b0936a372869a801a97f42336ee4e42feb
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-win32-x64-msvc@npm:0.1.2":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer-win32-x64-msvc@npm:0.1.2"
  checksum: 4a7d34d8419608cc343b6c028e07bd9ec72fd4ab82ccd36807ccf0fc8ad708b8d5baae9121532073ef08b2deb24d9c3a6f7b627c26f91f2a7de0cdb7024238f1
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer@npm:^0.1.0, @nomicfoundation/solidity-analyzer@npm:^0.1.1":
  version: 0.1.2
  resolution: "@nomicfoundation/solidity-analyzer@npm:0.1.2"
  dependencies:
    "@nomicfoundation/solidity-analyzer-darwin-arm64": 0.1.2
    "@nomicfoundation/solidity-analyzer-darwin-x64": 0.1.2
    "@nomicfoundation/solidity-analyzer-linux-arm64-gnu": 0.1.2
    "@nomicfoundation/solidity-analyzer-linux-arm64-musl": 0.1.2
    "@nomicfoundation/solidity-analyzer-linux-x64-gnu": 0.1.2
    "@nomicfoundation/solidity-analyzer-linux-x64-musl": 0.1.2
    "@nomicfoundation/solidity-analyzer-win32-x64-msvc": 0.1.2
  dependenciesMeta:
    "@nomicfoundation/solidity-analyzer-darwin-arm64":
      optional: true
    "@nomicfoundation/solidity-analyzer-darwin-x64":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-arm64-gnu":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-arm64-musl":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-x64-gnu":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-x64-musl":
      optional: true
    "@nomicfoundation/solidity-analyzer-win32-x64-msvc":
      optional: true
  checksum: 0de3a317658345b9012285665bb4c810a98b3668bcf32a118912fda00e5760fa2c77d0a92bce6b687dcc7b4bb34b0a83f8e6748bfa68660a2303d781ca728aef
  languageName: node
  linkType: hard

"@nomiclabs/hardhat-ethers@npm:^2.2.3":
  version: 2.2.3
  resolution: "@nomiclabs/hardhat-ethers@npm:2.2.3"
  peerDependencies:
    ethers: ^5.0.0
    hardhat: ^2.0.0
  checksum: 72321317e55eb510306e04c42353c5f7ceb42d086fc76cc740120da6e1635b7ad5bbf23a8d6b02bd590754adcf646618933111624085ab249b1ff3482e773226
  languageName: node
  linkType: hard

"@nomiclabs/hardhat-waffle@npm:^2.0.6":
  version: 2.0.6
  resolution: "@nomiclabs/hardhat-waffle@npm:2.0.6"
  peerDependencies:
    "@nomiclabs/hardhat-ethers": ^2.0.0
    "@types/sinon-chai": ^3.2.3
    ethereum-waffle: "*"
    ethers: ^5.0.0
    hardhat: ^2.0.0
  checksum: e43592b135739c7f077a9d0a38a479a5512000e58f91d684e6a0d4f0894f8f826821d0b637e2cd7b646669ba12300fcb5e180bcc2473f5cc67d55f44ab809770
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@offchainlabs/upgrade-executor@npm:1.1.0-beta.0":
  version: 1.1.0-beta.0
  resolution: "@offchainlabs/upgrade-executor@npm:1.1.0-beta.0"
  dependencies:
    "@openzeppelin/contracts": 4.7.3
    "@openzeppelin/contracts-upgradeable": 4.7.3
  checksum: b3c27bb2ff3f2ee0d7fbb955e2bc7f1dda29a7a409ea88348e3da627e91bb921b0bc8f8028caa7ecd087fe63f740dfda8fd7ead569eaeff5a282e83df1039bf6
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable@npm:4.5.2":
  version: 4.5.2
  resolution: "@openzeppelin/contracts-upgradeable@npm:4.5.2"
  checksum: bd6a12eb83d8e3194e0847ffb248a1063668e2bf723afd21066316090de9b02e95af6057086a95fa21b0d09a2ef0ea057459bb5f5645dfff298e59e8c3267692
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable@npm:4.7.3":
  version: 4.7.3
  resolution: "@openzeppelin/contracts-upgradeable@npm:4.7.3"
  checksum: c9ffb40cb847a975d440204fc6a811f43af960050242f707332b984d29bd16dc242ffa0935de61867aeb9e0357fadedb16b09b276deda5e9775582face831021
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable@npm:4.8.3":
  version: 4.8.3
  resolution: "@openzeppelin/contracts-upgradeable@npm:4.8.3"
  checksum: 022c99bac4828980e771ddf3426a58b5a6f27932a1b1ec93dd2ed3f11c89d8e407867b845bedd0660bdd11c683971d566737c10504db8a1f144f61daec8b5ed6
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable@npm:4.9.3":
  version: 4.9.3
  resolution: "@openzeppelin/contracts-upgradeable@npm:4.9.3"
  checksum: bda0240b1d44c913ec5a4e109c622f216c2bbd7b468d210822f75782a5f7fe0609d08bf03b78b253333625e99e507cf2f75212f1de3b274bd9fc64ae967aeec3
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable@npm:^5.2.0":
  version: 5.2.0
  resolution: "@openzeppelin/contracts-upgradeable@npm:5.2.0"
  peerDependencies:
    "@openzeppelin/contracts": 5.2.0
  checksum: 5736d40287899e72e240acdcddd72961f792a0c90a22a96f2a6705aa01286ab99ca96e800567d763d66cf90b40d814fa1f1ff48844dfa0676b96a85edf9854b3
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:4.5.0":
  version: 4.5.0
  resolution: "@openzeppelin/contracts@npm:4.5.0"
  checksum: 1c9c5dff041905771d2a83ac29c64dbf0b48603de43f74a34fb1358813d7c7bf259efe2f64e2112b410c5cca7a0b41aaedd882368be5f01a6d50a3ee0740f962
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:4.7.3":
  version: 4.7.3
  resolution: "@openzeppelin/contracts@npm:4.7.3"
  checksum: 18382fcacf7cfd652f5dd0e70c08f08ea74eaa8ff11e9f9850639ada70198ae01a3f9493d89a52d724f2db394e9616bf6258017804612ba273167cf657fbb073
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:4.8.3":
  version: 4.8.3
  resolution: "@openzeppelin/contracts@npm:4.8.3"
  checksum: aea130d38d46840c5cbe3adbaa9a7ac645e4bd66ad3f3baf2fa78588c408d1a686170b3408c9e2e5e05530fba22ecdc00d7efb6b27852a8b29f91accbc0af255
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:4.9.3":
  version: 4.9.3
  resolution: "@openzeppelin/contracts@npm:4.9.3"
  checksum: 4932063e733b35fa7669b9fe2053f69b062366c5c208b0c6cfa1ac451712100c78acff98120c3a4b88d94154c802be05d160d71f37e7d74cadbe150964458838
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:^5.2.0":
  version: 5.2.0
  resolution: "@openzeppelin/contracts@npm:5.2.0"
  checksum: badff8c3be46b099c331e90ba27f75e36f7af681279952cf0b123c7448bc53cd541ef447de8e6ebc38e7b29aed8f17dacf0756aac5891eed03fe96a0a4eba1a0
  languageName: node
  linkType: hard

"@openzeppelin/defender-sdk-base-client@npm:^2.1.0":
  version: 2.1.0
  resolution: "@openzeppelin/defender-sdk-base-client@npm:2.1.0"
  dependencies:
    "@aws-sdk/client-lambda": ^3.563.0
    amazon-cognito-identity-js: ^6.3.6
    async-retry: ^1.3.3
  checksum: 00704f3f7cf6bc171e611676f52a5db1c6f2778b73f445bbe17071bab0646d98255d8cdae1f5a8f5c330665e6c423a361be307f197f7cbfca363ae40e483f78b
  languageName: node
  linkType: hard

"@openzeppelin/defender-sdk-deploy-client@npm:^2.1.0":
  version: 2.1.0
  resolution: "@openzeppelin/defender-sdk-deploy-client@npm:2.1.0"
  dependencies:
    "@openzeppelin/defender-sdk-base-client": ^2.1.0
    axios: ^1.7.4
    lodash: ^4.17.21
  checksum: 2045b47bdd7cd071d6f4eb6f3ecf56c1f962e080fa095416ac5fd4eb906cbe5696ab0b6d3dccb3b6a331dcca22954527b1b44526195269c2408330f0062357e7
  languageName: node
  linkType: hard

"@openzeppelin/defender-sdk-network-client@npm:^2.1.0":
  version: 2.1.0
  resolution: "@openzeppelin/defender-sdk-network-client@npm:2.1.0"
  dependencies:
    "@openzeppelin/defender-sdk-base-client": ^2.1.0
    axios: ^1.7.4
    lodash: ^4.17.21
  checksum: f80b162a84f9c8ed06dc7db77a0bac678d8d197ddf14e33a8ea0c18c7b6c24750a978c6b3108363656abeb5cf8081d7f87677c82a5cb50b92f13e053af94b299
  languageName: node
  linkType: hard

"@openzeppelin/hardhat-upgrades@npm:^3.9.0":
  version: 3.9.0
  resolution: "@openzeppelin/hardhat-upgrades@npm:3.9.0"
  dependencies:
    "@openzeppelin/defender-sdk-base-client": ^2.1.0
    "@openzeppelin/defender-sdk-deploy-client": ^2.1.0
    "@openzeppelin/defender-sdk-network-client": ^2.1.0
    "@openzeppelin/upgrades-core": ^1.41.0
    chalk: ^4.1.0
    debug: ^4.1.1
    ethereumjs-util: ^7.1.5
    proper-lockfile: ^4.1.1
    undici: ^6.11.1
  peerDependencies:
    "@nomicfoundation/hardhat-ethers": ^3.0.0
    "@nomicfoundation/hardhat-verify": ^2.0.0
    ethers: ^6.6.0
    hardhat: ^2.0.2
  peerDependenciesMeta:
    "@nomicfoundation/hardhat-verify":
      optional: true
  bin:
    migrate-oz-cli-project: dist/scripts/migrate-oz-cli-project.js
  checksum: 3475daf67bd03dde63de86666f897a4fcdea0028c1759b70163429af5f4e83521d8de7f8c836aa08df6f0218112d31f541c5197827b04d95ef777406d2a752dc
  languageName: node
  linkType: hard

"@openzeppelin/upgrades-core@npm:^1.24.1, @openzeppelin/upgrades-core@npm:^1.41.0":
  version: 1.41.0
  resolution: "@openzeppelin/upgrades-core@npm:1.41.0"
  dependencies:
    "@nomicfoundation/slang": ^0.18.3
    cbor: ^9.0.0
    chalk: ^4.1.0
    compare-versions: ^6.0.0
    debug: ^4.1.1
    ethereumjs-util: ^7.0.3
    minimatch: ^9.0.5
    minimist: ^1.2.7
    proper-lockfile: ^4.1.1
    solidity-ast: ^0.4.51
  bin:
    openzeppelin-upgrades-core: dist/cli/cli.js
  checksum: 16548d76d84fa642a7ec1ddae02aafef08d2b1f592a9b7ddf182dbacfdebcd8c8ab60664de47db6ffbea797c6fb7759589791408898a1f67aa551da5ebc479ba
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@polkadot/networks@npm:13.3.1":
  version: 13.3.1
  resolution: "@polkadot/networks@npm:13.3.1"
  dependencies:
    "@polkadot/util": 13.3.1
    "@substrate/ss58-registry": ^1.51.0
    tslib: ^2.8.0
  checksum: 6497f190894bd4fd46c2edfeb2b74ffee17338352c5f25fb1f7ea0293f7ccb5fbb846c08b26d64956db9e3cf41ac7ee5a5c50938f6a64a021d6cfc25f79e5c19
  languageName: node
  linkType: hard

"@polkadot/util-crypto@npm:^13.3.1":
  version: 13.3.1
  resolution: "@polkadot/util-crypto@npm:13.3.1"
  dependencies:
    "@noble/curves": ^1.3.0
    "@noble/hashes": ^1.3.3
    "@polkadot/networks": 13.3.1
    "@polkadot/util": 13.3.1
    "@polkadot/wasm-crypto": ^7.4.1
    "@polkadot/wasm-util": ^7.4.1
    "@polkadot/x-bigint": 13.3.1
    "@polkadot/x-randomvalues": 13.3.1
    "@scure/base": ^1.1.7
    tslib: ^2.8.0
  peerDependencies:
    "@polkadot/util": 13.3.1
  checksum: 0a0d2c57296945578b84cc4040f7be9d906ebac1c6d25291844fed50349adb0df8f8f9ff5e8f6e7ff0006652ffda23ee3a458a6dfbbd087d2e8c36f0c20864e2
  languageName: node
  linkType: hard

"@polkadot/util@npm:13.3.1":
  version: 13.3.1
  resolution: "@polkadot/util@npm:13.3.1"
  dependencies:
    "@polkadot/x-bigint": 13.3.1
    "@polkadot/x-global": 13.3.1
    "@polkadot/x-textdecoder": 13.3.1
    "@polkadot/x-textencoder": 13.3.1
    "@types/bn.js": ^5.1.6
    bn.js: ^5.2.1
    tslib: ^2.8.0
  checksum: edb0e888046bb5dd5275afd7565d21ee11c09c811f3ef87db4a01deb36902f1537ccfcf727bb80d53c4381a8a6d997d093d4af0be9a4d305a5f904a97bf71996
  languageName: node
  linkType: hard

"@polkadot/wasm-bridge@npm:7.4.1":
  version: 7.4.1
  resolution: "@polkadot/wasm-bridge@npm:7.4.1"
  dependencies:
    "@polkadot/wasm-util": 7.4.1
    tslib: ^2.7.0
  peerDependencies:
    "@polkadot/util": "*"
    "@polkadot/x-randomvalues": "*"
  checksum: 2cb4389853764eccebbe37a36e583a240b06e20c726247173c3ff5d85e198544c17ebef302da2e40ccd67f4fdb81454ab01cfbfc2fb93b1b3553d5bcdf4fe1bc
  languageName: node
  linkType: hard

"@polkadot/wasm-crypto-asmjs@npm:7.4.1":
  version: 7.4.1
  resolution: "@polkadot/wasm-crypto-asmjs@npm:7.4.1"
  dependencies:
    tslib: ^2.7.0
  peerDependencies:
    "@polkadot/util": "*"
  checksum: 983c345b034723d1967349f446682f79c1ee02030895153fd4aa137cd00bbf8788ddfeb0825e2118ee5db2894707f4224d61eabe931c028d22d1f10e52a1acd8
  languageName: node
  linkType: hard

"@polkadot/wasm-crypto-init@npm:7.4.1":
  version: 7.4.1
  resolution: "@polkadot/wasm-crypto-init@npm:7.4.1"
  dependencies:
    "@polkadot/wasm-bridge": 7.4.1
    "@polkadot/wasm-crypto-asmjs": 7.4.1
    "@polkadot/wasm-crypto-wasm": 7.4.1
    "@polkadot/wasm-util": 7.4.1
    tslib: ^2.7.0
  peerDependencies:
    "@polkadot/util": "*"
    "@polkadot/x-randomvalues": "*"
  checksum: fc697dc76d99b9597750abe3739da28ed3731b199eb8efc522bab03bca4fb9b34ece091ebd9bd26509d75a9785078724417754ac45e1fec4ed541b805fc75025
  languageName: node
  linkType: hard

"@polkadot/wasm-crypto-wasm@npm:7.4.1":
  version: 7.4.1
  resolution: "@polkadot/wasm-crypto-wasm@npm:7.4.1"
  dependencies:
    "@polkadot/wasm-util": 7.4.1
    tslib: ^2.7.0
  peerDependencies:
    "@polkadot/util": "*"
  checksum: 303c53cdb5a9219f52827cb51bae8be3e897317280adea8a6507a5cbf3ad4b4bd62b5ca7ceba02f972dc0df1e36a4a169b9eaf863076a913c2a612e9c71742f4
  languageName: node
  linkType: hard

"@polkadot/wasm-crypto@npm:^7.4.1":
  version: 7.4.1
  resolution: "@polkadot/wasm-crypto@npm:7.4.1"
  dependencies:
    "@polkadot/wasm-bridge": 7.4.1
    "@polkadot/wasm-crypto-asmjs": 7.4.1
    "@polkadot/wasm-crypto-init": 7.4.1
    "@polkadot/wasm-crypto-wasm": 7.4.1
    "@polkadot/wasm-util": 7.4.1
    tslib: ^2.7.0
  peerDependencies:
    "@polkadot/util": "*"
    "@polkadot/x-randomvalues": "*"
  checksum: c3c155ad08a3be5b3de22743a3e8f3658082150138e770d4604e55256671021fb9d2f191fc228b0a7893a1af1cfce21daa11f7300a8b4cf1037de01aad583dcf
  languageName: node
  linkType: hard

"@polkadot/wasm-util@npm:7.4.1, @polkadot/wasm-util@npm:^7.4.1":
  version: 7.4.1
  resolution: "@polkadot/wasm-util@npm:7.4.1"
  dependencies:
    tslib: ^2.7.0
  peerDependencies:
    "@polkadot/util": "*"
  checksum: 16995482059ea7b3fa95ecb8bddd1465af64ca8b0b42b9942839fd0aa7bf556b7f4c914eb3bfe035d73ec5f1dc91f1b0b5d502bfb9d8b809d4399cd15b934e70
  languageName: node
  linkType: hard

"@polkadot/x-bigint@npm:13.3.1":
  version: 13.3.1
  resolution: "@polkadot/x-bigint@npm:13.3.1"
  dependencies:
    "@polkadot/x-global": 13.3.1
    tslib: ^2.8.0
  checksum: 8a281a7692fe50463a90b692298994b7d8c363da903a29b2202ee3711d0ec99478c7496d1a3d23f7ac2660a10497df315acd90c2352927208ea5a39e287750c0
  languageName: node
  linkType: hard

"@polkadot/x-global@npm:13.3.1":
  version: 13.3.1
  resolution: "@polkadot/x-global@npm:13.3.1"
  dependencies:
    tslib: ^2.8.0
  checksum: ec94d30e39854af70ecf8bbdf6e368e41ca2abc745a3bc26ef8af19fea5f62b94f75d4af45cc7eae93c40018ee17794872526b05d5f4c3d406ebe529174391a8
  languageName: node
  linkType: hard

"@polkadot/x-randomvalues@npm:13.3.1":
  version: 13.3.1
  resolution: "@polkadot/x-randomvalues@npm:13.3.1"
  dependencies:
    "@polkadot/x-global": 13.3.1
    tslib: ^2.8.0
  peerDependencies:
    "@polkadot/util": 13.3.1
    "@polkadot/wasm-util": "*"
  checksum: c4d8250e3e284cd01cb4e7ec52356e21f832c6430d5faa7a905a5a2ebd5c983a15aec7aecc481cd2d80097d7d71fa907e7ad7031cc1285fa548bc87914d7f5cd
  languageName: node
  linkType: hard

"@polkadot/x-textdecoder@npm:13.3.1":
  version: 13.3.1
  resolution: "@polkadot/x-textdecoder@npm:13.3.1"
  dependencies:
    "@polkadot/x-global": 13.3.1
    tslib: ^2.8.0
  checksum: 70970ba5a5ff2350f8a3a26d2debd28be513322eb14d79d0f9dbbe96e0d81e91873544ba0c33d7526a40dd02a43cd2c56931747153343cdda07206da5885fbfd
  languageName: node
  linkType: hard

"@polkadot/x-textencoder@npm:13.3.1":
  version: 13.3.1
  resolution: "@polkadot/x-textencoder@npm:13.3.1"
  dependencies:
    "@polkadot/x-global": 13.3.1
    tslib: ^2.8.0
  checksum: 02de4188e43b128d384d11a5105796c0777ef94520a762f5731000561653ec2df126b5037f3d74fe28c11eab9fea0fd9f7f0a1fb38310e1c388480a2e9ceb3e8
  languageName: node
  linkType: hard

"@resolver-engine/core@npm:^0.3.3":
  version: 0.3.3
  resolution: "@resolver-engine/core@npm:0.3.3"
  dependencies:
    debug: ^3.1.0
    is-url: ^1.2.4
    request: ^2.85.0
  checksum: e5ac586da2aeb7e384f6841821e528771fca533bf5cf38d7fd0851733bd9b70939e960459f2b841534ecdca6507c9aff71bd317f7481137d7b1d2e87ba15978a
  languageName: node
  linkType: hard

"@resolver-engine/fs@npm:^0.3.3":
  version: 0.3.3
  resolution: "@resolver-engine/fs@npm:0.3.3"
  dependencies:
    "@resolver-engine/core": ^0.3.3
    debug: ^3.1.0
  checksum: 734577b7864c3aceaaa80b4b74c252d92fb14a6f3c46dfc0a2d4658288dce1b38797578dd6a4ecbde88cbc4a366e8bdbc46451e282cb25dde8479548453c37a3
  languageName: node
  linkType: hard

"@resolver-engine/imports-fs@npm:^0.3.3":
  version: 0.3.3
  resolution: "@resolver-engine/imports-fs@npm:0.3.3"
  dependencies:
    "@resolver-engine/fs": ^0.3.3
    "@resolver-engine/imports": ^0.3.3
    debug: ^3.1.0
  checksum: d24778788959f8a201bda0a91527cd1703dfbbf3675fd16bd3891046e3f12378be73233bb9d4da19c7247488be38daeab2bdf800317f70553a16fb62208ba2c7
  languageName: node
  linkType: hard

"@resolver-engine/imports@npm:^0.3.3":
  version: 0.3.3
  resolution: "@resolver-engine/imports@npm:0.3.3"
  dependencies:
    "@resolver-engine/core": ^0.3.3
    debug: ^3.1.0
    hosted-git-info: ^2.6.0
    path-browserify: ^1.0.0
    url: ^0.11.0
  checksum: 690cf550fd0608e849fcb9c20a08479ce405173f8d0b09141a5bd140c4ae7c887ebcb0532c4ca64b5c1d3039fe77cc94172b7afb51c1a8fe7722475c429e6944
  languageName: node
  linkType: hard

"@scroll-tech/contracts@npm:0.1.0":
  version: 0.1.0
  resolution: "@scroll-tech/contracts@npm:0.1.0"
  checksum: 61aa3ca76043276dd38dfb2ed67c019fcd1bf42c8b5d33726e6addfa95cad96ba057988cc00572159c47fcdbbad6fbd1da4821c82f512f5a8704f7807820a04b
  languageName: node
  linkType: hard

"@scure/base@npm:^1.1.7":
  version: 1.2.4
  resolution: "@scure/base@npm:1.2.4"
  checksum: db554eb550a1bd17684af9282e1ad751050a13d4add0e83ad61cc496680d7d1c1c1120ca780e72935a293bb59721c20a006a53a5eec6f6b5bdcd702cf27c8cae
  languageName: node
  linkType: hard

"@scure/base@npm:~1.1.0, @scure/base@npm:~1.1.6":
  version: 1.1.9
  resolution: "@scure/base@npm:1.1.9"
  checksum: 120820a37dfe9dfe4cab2b7b7460552d08e67dee8057ed5354eb68d8e3440890ae983ce3bee957d2b45684950b454a2b6d71d5ee77c1fd3fddc022e2a510337f
  languageName: node
  linkType: hard

"@scure/base@npm:~1.2.2, @scure/base@npm:~1.2.4, @scure/base@npm:~1.2.5":
  version: 1.2.5
  resolution: "@scure/base@npm:1.2.5"
  checksum: 79f76781d4f55fa2ce36e4d6f950a76234a81f81c9f5f33794ee82077e5c8005e84a1491684a0643e77734e3dd1cd8367930d2a165a9c0af4d3c526ffe7407f8
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.1.5":
  version: 1.1.5
  resolution: "@scure/bip32@npm:1.1.5"
  dependencies:
    "@noble/hashes": ~1.2.0
    "@noble/secp256k1": ~1.7.0
    "@scure/base": ~1.1.0
  checksum: b08494ab0d2b1efee7226d1b5100db5157ebea22a78bb87126982a76a186cb3048413e8be0ba2622d00d048a20acbba527af730de86c132a77de616eb9907a3b
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.4.0":
  version: 1.4.0
  resolution: "@scure/bip32@npm:1.4.0"
  dependencies:
    "@noble/curves": ~1.4.0
    "@noble/hashes": ~1.4.0
    "@scure/base": ~1.1.6
  checksum: eff491651cbf2bea8784936de75af5fc020fc1bbb9bcb26b2cfeefbd1fb2440ebfaf30c0733ca11c0ae1e272a2ef4c3c34ba5c9fb3e1091c3285a4272045b0c6
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.6.2":
  version: 1.6.2
  resolution: "@scure/bip32@npm:1.6.2"
  dependencies:
    "@noble/curves": ~1.8.1
    "@noble/hashes": ~1.7.1
    "@scure/base": ~1.2.2
  checksum: e7586619f8a669e522267ce71a90b2d00c3a91da658f1f50e54072cf9f432ba26d2bb4d3d91a5d06932ab96612b8bd038bc31d885bbc048cebfb6509c4a790fc
  languageName: node
  linkType: hard

"@scure/bip32@npm:^1.5.0":
  version: 1.7.0
  resolution: "@scure/bip32@npm:1.7.0"
  dependencies:
    "@noble/curves": ~1.9.0
    "@noble/hashes": ~1.8.0
    "@scure/base": ~1.2.5
  checksum: c83adca5a74ec5c4ded8ba93900d0065e4767c4759cf24c2674923aef01d45ba56f171574e3519f2341be99f53a333f01b674eb6cfeb6fa8379607c6d1bc90b5
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.1.1":
  version: 1.1.1
  resolution: "@scure/bip39@npm:1.1.1"
  dependencies:
    "@noble/hashes": ~1.2.0
    "@scure/base": ~1.1.0
  checksum: fbb594c50696fa9c14e891d872f382e50a3f919b6c96c55ef2fb10c7102c546dafb8f099a62bd114c12a00525b595dcf7381846f383f0ddcedeaa6e210747d2f
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.3.0":
  version: 1.3.0
  resolution: "@scure/bip39@npm:1.3.0"
  dependencies:
    "@noble/hashes": ~1.4.0
    "@scure/base": ~1.1.6
  checksum: dbb0b27df753eb6c6380010b25cc9a9ea31f9cb08864fc51e69e5880ff7e2b8f85b72caea1f1f28af165e83b72c48dd38617e43fc632779d025b50ba32ea759e
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.5.4":
  version: 1.5.4
  resolution: "@scure/bip39@npm:1.5.4"
  dependencies:
    "@noble/hashes": ~1.7.1
    "@scure/base": ~1.2.4
  checksum: 744f302559ad05ee6ea4928572ac8f0b5443e8068fd53234c9c2e158814e910a043c54f0688d05546decadd2ff66e0d0c76355d10e103a28cb8f44efe140857a
  languageName: node
  linkType: hard

"@scure/bip39@npm:^1.4.0":
  version: 1.6.0
  resolution: "@scure/bip39@npm:1.6.0"
  dependencies:
    "@noble/hashes": ~1.8.0
    "@scure/base": ~1.2.5
  checksum: 96d46420780473d6c6c9700254a0eceec60302f61d7f9d7f29024e90c7acff3e8e40a5ee52dfaf104db539a10462e531996aaf9e69f082b8540b0a25870545fc
  languageName: node
  linkType: hard

"@sentry/core@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/core@npm:5.30.0"
  dependencies:
    "@sentry/hub": 5.30.0
    "@sentry/minimal": 5.30.0
    "@sentry/types": 5.30.0
    "@sentry/utils": 5.30.0
    tslib: ^1.9.3
  checksum: 8a2b22687e70d76fa4381bce215d770b6c08561c5ff5d6afe39c8c3c509c18ee7384ad0be3aee18d3a858a3c88e1d2821cf10eb5e05646376a33200903b56da2
  languageName: node
  linkType: hard

"@sentry/hub@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/hub@npm:5.30.0"
  dependencies:
    "@sentry/types": 5.30.0
    "@sentry/utils": 5.30.0
    tslib: ^1.9.3
  checksum: 09f778cc78765213f1e35a3ee6da3a8e02a706e8a7e5b7f84614707f4b665c7297b700a1849ab2ca1f02ede5884fd9ae893e58dc65f04f35ccdfee17e99ee93d
  languageName: node
  linkType: hard

"@sentry/minimal@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/minimal@npm:5.30.0"
  dependencies:
    "@sentry/hub": 5.30.0
    "@sentry/types": 5.30.0
    tslib: ^1.9.3
  checksum: 934650f6989ce51f425c7c4b4d4d9bfecface8162a36d21df8a241f780ab1716dd47b81e2170e4cc624797ed1eebe10f71e4876c1e25b787860daaef75ca7a0c
  languageName: node
  linkType: hard

"@sentry/node@npm:^5.18.1":
  version: 5.30.0
  resolution: "@sentry/node@npm:5.30.0"
  dependencies:
    "@sentry/core": 5.30.0
    "@sentry/hub": 5.30.0
    "@sentry/tracing": 5.30.0
    "@sentry/types": 5.30.0
    "@sentry/utils": 5.30.0
    cookie: ^0.4.1
    https-proxy-agent: ^5.0.0
    lru_map: ^0.3.3
    tslib: ^1.9.3
  checksum: 5f0367cc52f9d716c64ba727e2a5c8592364494c8fdadfb3df2d0ee9d7956b886fb3ec674370292d2a7b7e1d9a8e1b84c69c06e8a4a064be8d4687698df0090c
  languageName: node
  linkType: hard

"@sentry/tracing@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/tracing@npm:5.30.0"
  dependencies:
    "@sentry/hub": 5.30.0
    "@sentry/minimal": 5.30.0
    "@sentry/types": 5.30.0
    "@sentry/utils": 5.30.0
    tslib: ^1.9.3
  checksum: 720c07b111e8128e70a939ab4e9f9cfd13dc23303b27575afddabab08d08f9b94499017c76a9ffe253bf3ca40833e8f9262cf6dc546ba24da6eb74fedae5f92b
  languageName: node
  linkType: hard

"@sentry/types@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/types@npm:5.30.0"
  checksum: de7df777824c8e311f143c6fd7de220b24f25b5018312fe8f67d93bebf0f3cdd32bbca9f155846f5c31441d940eebe27c8338000321559a743264c7e41dda560
  languageName: node
  linkType: hard

"@sentry/utils@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/utils@npm:5.30.0"
  dependencies:
    "@sentry/types": 5.30.0
    tslib: ^1.9.3
  checksum: 27b259a136c664427641dd32ee3dc490553f3b5e92986accfa829d14063ebc69b191e92209ac9c40fbc367f74cfa17dc93b4c40981d666711fd57b4d51a82062
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/abort-controller@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 9f6ac65639ae5823e7ea83fcd05282fca105adecda8a40bd4280cacb87ef2af935cf18e649897369db53c1b82c81fcdea75240260ca0ce9795ee22d6afa4f067
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.0.0, @smithy/config-resolver@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/config-resolver@npm:4.0.1"
  dependencies:
    "@smithy/node-config-provider": ^4.0.1
    "@smithy/types": ^4.1.0
    "@smithy/util-config-provider": ^4.0.0
    "@smithy/util-middleware": ^4.0.1
    tslib: ^2.6.2
  checksum: 24035ea6766693668f0776f8eed3d0a81aecbabf925e48c20ef759e6a95b39cd3e1b04efd819860d46727fe094382803fe3f625a0fbfcd652196753b44b7864f
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.0.0, @smithy/core@npm:^3.1.0":
  version: 3.1.0
  resolution: "@smithy/core@npm:3.1.0"
  dependencies:
    "@smithy/middleware-serde": ^4.0.1
    "@smithy/protocol-http": ^5.0.1
    "@smithy/types": ^4.1.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-middleware": ^4.0.1
    "@smithy/util-stream": ^4.0.1
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 877f522d6eae322081c390b989e1def6ec1d55daf7d7ededf0853973d13d22523a3fca9bac868a0db47e321f6ac2c1c619ce094a742bae2698fc8e1940733537
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.0, @smithy/credential-provider-imds@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/credential-provider-imds@npm:4.0.1"
  dependencies:
    "@smithy/node-config-provider": ^4.0.1
    "@smithy/property-provider": ^4.0.1
    "@smithy/types": ^4.1.0
    "@smithy/url-parser": ^4.0.1
    tslib: ^2.6.2
  checksum: ec03248abf9b2e89f5a49539d2a069c3d034af35dc49a09d260dd58662ac0b639c6463d1eaa7d80253b8168c67ecb00de8c79376ed65433fc20f8e934a9017d9
  languageName: node
  linkType: hard

"@smithy/eventstream-codec@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/eventstream-codec@npm:4.0.1"
  dependencies:
    "@aws-crypto/crc32": 5.2.0
    "@smithy/types": ^4.1.0
    "@smithy/util-hex-encoding": ^4.0.0
    tslib: ^2.6.2
  checksum: a99733e446b7f4054ba62e0f06a6d88fc0b70324518e2d21f128c1a2c4b3c3a2bf18398ca7e4b2a59e54a62d7bedf451d65e8c9945ba4aa3db10c5df18c00fed
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-browser@npm:^4.0.0":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-browser@npm:4.0.1"
  dependencies:
    "@smithy/eventstream-serde-universal": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: cfa141c76002be5e3fc0ff80826b793f052b5dac4e5806e8548f29d18b77523d37cf597724f070a53227337f77e7da9cde505ba7f44297561d681b96e4ab762b
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-config-resolver@npm:^4.0.0":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-config-resolver@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 37c49d5ffa6b9c2f1f5b83b9c7f1de431ecef92a710e3dc4d91af5b978c662153d86ed558504424363c0c53682137ffa077ada9103f72eadc5d40ef4613f0241
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-node@npm:^4.0.0":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-node@npm:4.0.1"
  dependencies:
    "@smithy/eventstream-serde-universal": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: c24de78ac6bd3f4609a7e652301ff44fa0e5a499e2c10505068f17a8fa7387a5b6717601053c9c3804b94760df1da4799e5cd68900f97bab60160c075d93506a
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-universal@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/eventstream-serde-universal@npm:4.0.1"
  dependencies:
    "@smithy/eventstream-codec": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 1fc609a5d341a43c427f233a64afa722b458f9b1dc7db9bab898f62d6805edcbe4fba866620a8b83001240e90254d9057636a6cf4eca80ada58f9dd2e0ca864b
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.0.0, @smithy/fetch-http-handler@npm:^5.0.1":
  version: 5.0.1
  resolution: "@smithy/fetch-http-handler@npm:5.0.1"
  dependencies:
    "@smithy/protocol-http": ^5.0.1
    "@smithy/querystring-builder": ^4.0.1
    "@smithy/types": ^4.1.0
    "@smithy/util-base64": ^4.0.0
    tslib: ^2.6.2
  checksum: d8e160e4a57e1fb7b7805fcafda81fb7d7511904b48d2e25229d18bd15598c64cdd12bd39c0dee9fc9cc31a76952fae1d400c6a80e9015cfd6e22c2f930a6212
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.0":
  version: 4.0.1
  resolution: "@smithy/hash-node@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: c68d222d4c39e97e90965cc669b6d30628b07ae136f49981ff551bccd5c104161fd2a322ebea85514c925e1d3525e413c05513e303a76e7af6c3e0cdb55960d0
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.0":
  version: 4.0.1
  resolution: "@smithy/invalid-dependency@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 541e89a18cb5ce8db063ea74ea8831a11bdf42ac58412ae6aad350d4a128b6e9d3b0b5b31cac2597e5e52a0da4a2a3cf202946bb6649d398a84876a89c332bd1
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: ^2.6.2
  checksum: cd12c2e27884fec89ca8966d33c9dc34d3234efe89b33a9b309c61ebcde463e6f15f6a02d31d4fddbfd6e5904743524ca5b95021b517b98fe10957c2da0cd5fc
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 8226fc1eca7aacd7f887f3a5ec2f15a3cafa72aa1c42d3fc759c66600481381d18ec7285a8195f24b9c4fe0ce9a565c133b2021d86a8077aebce3f86b3716802
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.0":
  version: 4.0.1
  resolution: "@smithy/middleware-content-length@npm:4.0.1"
  dependencies:
    "@smithy/protocol-http": ^5.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 61109cfee368b8b20d39efcc050c0a30c4a4355dc4fb1c8521b1ec258c35c454bda9a6489571b01eb14c48e030642fd674d28e6c8083e6e4272b2b24cee0e61e
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.0.0, @smithy/middleware-endpoint@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/middleware-endpoint@npm:4.0.1"
  dependencies:
    "@smithy/core": ^3.1.0
    "@smithy/middleware-serde": ^4.0.1
    "@smithy/node-config-provider": ^4.0.1
    "@smithy/shared-ini-file-loader": ^4.0.1
    "@smithy/types": ^4.1.0
    "@smithy/url-parser": ^4.0.1
    "@smithy/util-middleware": ^4.0.1
    tslib: ^2.6.2
  checksum: 203749fda4a7524b4bce67326d3ffde5bb4eda023a10badcaf5b1243fd3022045aa004cc4749aaa2edd3282690ecd842e2cacc92adf8833084e1007596214ea3
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.0.0":
  version: 4.0.2
  resolution: "@smithy/middleware-retry@npm:4.0.2"
  dependencies:
    "@smithy/node-config-provider": ^4.0.1
    "@smithy/protocol-http": ^5.0.1
    "@smithy/service-error-classification": ^4.0.1
    "@smithy/smithy-client": ^4.1.1
    "@smithy/types": ^4.1.0
    "@smithy/util-middleware": ^4.0.1
    "@smithy/util-retry": ^4.0.1
    tslib: ^2.6.2
    uuid: ^9.0.1
  checksum: 0e23393ac2f10e5f7566a0af7b140875a1973bbab2b02417ff095717effd18244df38da49dc93de270d75098353c1dcaddff0a4892482fd69ac76198a9f8deee
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.0, @smithy/middleware-serde@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/middleware-serde@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: fc462b4555272759ccaff21460d2373d3783e211d8d7eca9ed4b0a6dde214b00de21a16ffa38e384b58acc1b2b9c97bf3ed7261f9c06cd87ad6006460dd03135
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.0, @smithy/middleware-stack@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/middleware-stack@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 21f61adf5071c6c32356c9f6b2423fffc0ba0cfdedae37b5162659e156bec122e03f67a5dac5fbd224f9bbb15a6793fd332cf1a02ea17eda0c4fb7e4ca22ce95
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.0.0, @smithy/node-config-provider@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/node-config-provider@npm:4.0.1"
  dependencies:
    "@smithy/property-provider": ^4.0.1
    "@smithy/shared-ini-file-loader": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: e997b3732a686e1dd9c5544a97fb18519acb45d522700045301391eee4a7b305a31ed68dd3a407fe754bebdfd4b759d8128a4bc80cdcd490113934ef8c3aaaa7
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.0.0, @smithy/node-http-handler@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/node-http-handler@npm:4.0.1"
  dependencies:
    "@smithy/abort-controller": ^4.0.1
    "@smithy/protocol-http": ^5.0.1
    "@smithy/querystring-builder": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: d32ee8e9792340c1fa5d037e55e50c8ddf0e9b481a5bc83d1423765f66eb9f699c1510834b331ccb3b4b0d7f2f3c4eec5b724800c5132730954b3d930f059730
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.0, @smithy/property-provider@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/property-provider@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: c03bd23a9e707af6201e49d1d7c67d370b630eb39ab60eaebd628bda725105d3ed67392078d6ae73a22be35f7dcec9771fafd2a88c48b532ca717b68fc3c9a33
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.0.0, @smithy/protocol-http@npm:^5.0.1":
  version: 5.0.1
  resolution: "@smithy/protocol-http@npm:5.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 3978f544aa4bf36bcf2484126f5208f7035b210ca0088f2721edd11dbe7bbddeacb6b9e7ca493437dc7b5fdd0d9d85992f2c6e31846744690f205f852a981a3b
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/querystring-builder@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    "@smithy/util-uri-escape": ^4.0.0
    tslib: ^2.6.2
  checksum: 8c8486a1c5a8f7cb05db4fdbe213bd02a9b323121da885ff234763d63730aa269ce779adc4dea74715fbf53a7ff4f487d9d51dda33ddb14533ad42166f10b0cb
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/querystring-parser@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 0ce6963937aa44882aeaf44b6aff68ca08faa927bd93da7adf354dd83b48beaef4246672504d8fc10d91be07e2f78c2b670bb82a46638da573183a69fa393278
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/service-error-classification@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
  checksum: 331c06d7a07cd2f9303cc396e1f9b1d44c785ccb27f4f8f02177b9f496667ffa4df40ae38d2ed1b557cd9c75b5cacb9b00106462dc62094253f8619a7d370343
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.0, @smithy/shared-ini-file-loader@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 54a399800dc32368ad99c5da4fd5eae62de4f9ddd249144b6516493bc42625e83c21ccd7c61d667c88d6000a3f5b42db452c10b870740cc9bec9e6c776607a9e
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.0.0":
  version: 5.0.1
  resolution: "@smithy/signature-v4@npm:5.0.1"
  dependencies:
    "@smithy/is-array-buffer": ^4.0.0
    "@smithy/protocol-http": ^5.0.1
    "@smithy/types": ^4.1.0
    "@smithy/util-hex-encoding": ^4.0.0
    "@smithy/util-middleware": ^4.0.1
    "@smithy/util-uri-escape": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: fb6613ce08e2008e3da447eeaafdfdcbd8a428c9d4aaf3220eab77cb33832596885f77966acbee3f753e113ce728f440ca31747908d81d0ecbcf1822c5c7bd28
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.0.0, @smithy/smithy-client@npm:^4.1.1":
  version: 4.1.1
  resolution: "@smithy/smithy-client@npm:4.1.1"
  dependencies:
    "@smithy/core": ^3.1.0
    "@smithy/middleware-endpoint": ^4.0.1
    "@smithy/middleware-stack": ^4.0.1
    "@smithy/protocol-http": ^5.0.1
    "@smithy/types": ^4.1.0
    "@smithy/util-stream": ^4.0.1
    tslib: ^2.6.2
  checksum: c27a4e8ff9926948bf3ed6e917dff70f99859bed4a2829ae67c7d1a69d92ab1dac797db257aac4eb5e811733633003bb58097db1322cc404f4dca2c088d0ca42
  languageName: node
  linkType: hard

"@smithy/types@npm:^3.7.2":
  version: 3.7.2
  resolution: "@smithy/types@npm:3.7.2"
  dependencies:
    tslib: ^2.6.2
  checksum: f3ae277d2f81eeb67c12e651b2e999225cda115dff8fab257a77fbe3739d32c515dbd19218590dd2c33d203259b00c80c9fc6027f8484c50725390a8fc0f1e3e
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.0.0, @smithy/types@npm:^4.1.0":
  version: 4.1.0
  resolution: "@smithy/types@npm:4.1.0"
  dependencies:
    tslib: ^2.6.2
  checksum: ff7dcb7a72a2f5e984df95342ec7276cc3249e57de76d5013bf69314a4dbd081e193c5f849e8e5c3f54be222d861272a90ab15b437678e31958eb2c76f55c689
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.0, @smithy/url-parser@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/url-parser@npm:4.0.1"
  dependencies:
    "@smithy/querystring-parser": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 3ec0ebf024a333d20cfe463c246196a188abcd3460014cf535979540e873c5b9f7a13214e221aed31b50dd1f28b24b5eafbb6ef5ae1998987f81622c4ccd156b
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 7fb3430d6e1cbb4bcc61458587bb0746458f0ec8e8cd008224ca984ff65c3c3307b3a528d040cef4c1fc7d1bd4111f6de8f4f1595845422f14ac7d100b3871b1
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 72381e12de7cccbb722c60e3f3ae0f8bce7fc9a9e8064c7968ac733698a5a30bea098a3c365095c519491fe64e2e949c22f74d4f1e0d910090d6389b41c416eb
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 12d8de9c526647f51f56804044f5847f0c7c7afee30fa368d2b7bd4b4de8fe2438a925aab51965fe8a4b2f08f68e8630cc3c54a449beae6646d99cae900ed106
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": ^2.2.0
    tslib: ^2.6.2
  checksum: 424c5b7368ae5880a8f2732e298d17879a19ca925f24ca45e1c6c005f717bb15b76eb28174d308d81631ad457ea0088aab0fd3255dd42f45a535c81944ad64d3
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": ^4.0.0
    tslib: ^2.6.2
  checksum: 8124e28d3e34b5335c08398a9081cc56a232d23e08172d488669f91a167d0871d36aba9dd3e4b70175a52f1bd70e2bf708d4c989a19512a4374d2cf67650a15e
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 91bd9e0bec4c4a37c3fc286e72f3387be9272b090111edaee992d9e9619370f3f2ad88ce771ef42dbfe40a44500163b633914486e662526591f5f737d5e4ff5a
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.0":
  version: 4.0.2
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.2"
  dependencies:
    "@smithy/property-provider": ^4.0.1
    "@smithy/smithy-client": ^4.1.1
    "@smithy/types": ^4.1.0
    bowser: ^2.11.0
    tslib: ^2.6.2
  checksum: 00ae6988e412861eefed36955fd4777dc09b2dc12826de89dadac265543ecf83b42b453ffae6a1689d59f00e5ace4d8270d5371a4e2243e53b50f5854fde0c40
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.0":
  version: 4.0.2
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.2"
  dependencies:
    "@smithy/config-resolver": ^4.0.1
    "@smithy/credential-provider-imds": ^4.0.1
    "@smithy/node-config-provider": ^4.0.1
    "@smithy/property-provider": ^4.0.1
    "@smithy/smithy-client": ^4.1.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 8c9f7b9db0b202324207a3aa39f76ae5dabb70a062beba8aaf87a396ae701f00fbb5564444133e4f43da90a47abe415f5c411f6e28dd550eb0de0d7191fe393e
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.0":
  version: 3.0.1
  resolution: "@smithy/util-endpoints@npm:3.0.1"
  dependencies:
    "@smithy/node-config-provider": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 2d351e297353fb624ba564b46ecf324376bc8fe34529ab4551e1d640c3b0317613a620c28977819db2c2d240791ff354d1d996fda119c0c4885a11507fb86af6
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: b932fa0e5cd2ba2598ad55ce46722bbbd15109809badaa3e4402fe4dd6f31f62b9fb49d2616e38d660363dc92a5898391f9c8f3b18507c36109e908400785e2a
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.0, @smithy/util-middleware@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/util-middleware@npm:4.0.1"
  dependencies:
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 1402e0abd9bfeb0d8b0033ad1b572984df1469dccf9f562353ec0133691826cdd85aa180616267819f80d8bb56c57f5a3a2ae92033f52cd8249230a6e670343b
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.0, @smithy/util-retry@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/util-retry@npm:4.0.1"
  dependencies:
    "@smithy/service-error-classification": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 29f8afd444f4b692ebd8cb2d0f6045ac0d5ca3834c0b6bbfdf1f6c1faec17c7bdc9734413ba93c55a672d373900aaf08e3c9f2023b3ec9b60c057afb8bcb4966
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.0.0, @smithy/util-stream@npm:^4.0.1":
  version: 4.0.1
  resolution: "@smithy/util-stream@npm:4.0.1"
  dependencies:
    "@smithy/fetch-http-handler": ^5.0.1
    "@smithy/node-http-handler": ^4.0.1
    "@smithy/types": ^4.1.0
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-hex-encoding": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 53a783d197b6711fca0a13f9a9d2979c6b82fcb83cbcb1e6689693197a71ffacece7867270c91417e2731d0435b6aa89a1ecf4ca9ae55439230a5012026b361f
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 7ea350545971f8a009d56e085c34c949c9045862cfab233ee7adc16e111a076a814bb5d9279b2b85ee382e0ed204a1c673ac32e3e28f1073b62a2c53a5dd6d19
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": ^2.2.0
    tslib: ^2.6.2
  checksum: 00e55d4b4e37d48be0eef3599082402b933c52a1407fed7e8e8ad76d94d81a0b30b8bfaf2047c59d9c3af31e5f20e7a8c959cb7ae270f894255e05a2229964f0
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": ^4.0.0
    tslib: ^2.6.2
  checksum: 08811c5a18c341782b3b65acc4640a9f559aeba61c889dbdc56e5153a3b7f395e613bfb1ade25cf15311d6237f291e1fce8af197c6313065e0cb084fd2148c64
  languageName: node
  linkType: hard

"@smithy/util-waiter@npm:^4.0.0":
  version: 4.0.2
  resolution: "@smithy/util-waiter@npm:4.0.2"
  dependencies:
    "@smithy/abort-controller": ^4.0.1
    "@smithy/types": ^4.1.0
    tslib: ^2.6.2
  checksum: 8e5cbf0ea3d93e3bc834b2db8e158c2a84a1c36f5163d4b9b925f6444c5be60c23803b6afb323de2d32f16f08f3e03e8e423bc7e8b531bbf9ff6e23f42554fe9
  languageName: node
  linkType: hard

"@solidity-parser/parser@npm:^0.19.0":
  version: 0.19.0
  resolution: "@solidity-parser/parser@npm:0.19.0"
  checksum: b1c556eeb83ac99f066ea4b0eb0bee45321a667f76dbafef95f8bc6adf32d1f8f52f752fb47620c61d1a264d3acb7534d75a8daa6d21099f55bc52b0af13ad83
  languageName: node
  linkType: hard

"@substrate/ss58-registry@npm:^1.51.0":
  version: 1.51.0
  resolution: "@substrate/ss58-registry@npm:1.51.0"
  checksum: bfcba035e14648801f74802c76b195c22a86875cca89a577e21f5edd3e800578486f964a5117bad4b272f21695f8557fe713c3031c0c81269b76259470eb5a74
  languageName: node
  linkType: hard

"@trufflesuite/bigint-buffer@npm:1.1.10":
  version: 1.1.10
  resolution: "@trufflesuite/bigint-buffer@npm:1.1.10"
  dependencies:
    node-gyp: latest
    node-gyp-build: 4.4.0
  checksum: e1dc5e4fbf348a55e660c0055267021eb04cbbdb7f6b0ee983ad32cd4aae1200be448a2ca3963c7d19c7c936d42f66c1ff8b5e4e2332cb1a9e3f870ff818dce4
  languageName: node
  linkType: hard

"@trufflesuite/bigint-buffer@npm:1.1.9":
  version: 1.1.9
  resolution: "@trufflesuite/bigint-buffer@npm:1.1.9"
  dependencies:
    node-gyp: latest
    node-gyp-build: 4.3.0
  checksum: 627dcff2cae7afe31432646232518363869e89b300f90f88ca68d903d0bdc79119975a5bc338223c03c1a4484cfac6d9cf4413ef20933a69eee48dd925519165
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 51fe47d55fe1b80ec35e6e5ed30a13665fd3a531945350aa74a14a1e82875fb60b350c2f2a5e72a64831b1b6bc02acb6760c30b3738b54954ec2dea82db7a267
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 5ce29a41b13e7897a58b8e2df11269c5395999e588b9a467386f99d1d26f6c77d1af2719e407621412520ea30517d718d5192a32403b8dfcc163bf33e40a338a
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 19275fe80c4c8d0ad0abed6a96dbf00642e88b220b090418609c4376e1cef81bf16237bf170ad1b341452feddb8115d8dd2e5acdfdea1b27422071163dc9ba9d
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 202319785901f942a6e1e476b872d421baec20cf09f4b266a1854060efbf78cde16a4d256e8bc949d31e6cd9a90f1e8ef8fb06af96a65e98338a2b6b0de0a0ff
  languageName: node
  linkType: hard

"@typechain/ethers-v5@npm:^10.0.0":
  version: 10.2.1
  resolution: "@typechain/ethers-v5@npm:10.2.1"
  dependencies:
    lodash: ^4.17.15
    ts-essentials: ^7.0.1
  peerDependencies:
    "@ethersproject/abi": ^5.0.0
    "@ethersproject/providers": ^5.0.0
    ethers: ^5.1.3
    typechain: ^8.1.1
    typescript: ">=4.3.0"
  checksum: 852da4b1ff368ef87251111a5d50077de3d0fc12c519529269a74223740f8bda89297e67a5eb6c1f5b04ee23119566d6cbccf58264d32a83132be0f328a58d22
  languageName: node
  linkType: hard

"@typechain/ethers-v6@npm:^0.5.1":
  version: 0.5.1
  resolution: "@typechain/ethers-v6@npm:0.5.1"
  dependencies:
    lodash: ^4.17.15
    ts-essentials: ^7.0.1
  peerDependencies:
    ethers: 6.x
    typechain: ^8.3.2
    typescript: ">=4.7.0"
  checksum: 44e7970ce95eeb1a02019f8a53bbe30dcb87664e1df15fe436ae48eea1bf91ca72b5b230eac1bdf9cbe9b55bc488b54c6273e6f77155eaeff885fb34cfcd1108
  languageName: node
  linkType: hard

"@typechain/hardhat@npm:^9.1.0":
  version: 9.1.0
  resolution: "@typechain/hardhat@npm:9.1.0"
  dependencies:
    fs-extra: ^9.1.0
  peerDependencies:
    "@typechain/ethers-v6": ^0.5.1
    ethers: ^6.1.0
    hardhat: ^2.9.9
    typechain: ^8.3.2
  checksum: a05998ce89bb4a297f233f4489e4af2a3ad0abcedd75392096745a378d7c5bab9650fcd5dd778c9deaf790bf16e3a849b3ed91a6ae916173df6dfc9e574efcbc
  languageName: node
  linkType: hard

"@types/abstract-leveldown@npm:*":
  version: 7.2.5
  resolution: "@types/abstract-leveldown@npm:7.2.5"
  checksum: 3a99b13c81a53a62b42bea9cff326880de3146b4eeff528b039be69a268515b3120a6c12142e96646fcb0a03c463f298998581e86d9ddb29fbea3612f40edb2b
  languageName: node
  linkType: hard

"@types/bn.js@npm:^4.11.3":
  version: 4.11.6
  resolution: "@types/bn.js@npm:4.11.6"
  dependencies:
    "@types/node": "*"
  checksum: 7f66f2c7b7b9303b3205a57184261974b114495736b77853af5b18d857c0b33e82ce7146911e86e87a87837de8acae28986716fd381ac7c301fd6e8d8b6c811f
  languageName: node
  linkType: hard

"@types/bn.js@npm:^5.1.0, @types/bn.js@npm:^5.1.6":
  version: 5.1.6
  resolution: "@types/bn.js@npm:5.1.6"
  dependencies:
    "@types/node": "*"
  checksum: 887411126d40e3d28aef2df8075cda2832db2b0e926bb4046039bbb026f2e3cfbcf1a3ce90bd935be0fcc039f8009e32026dfbb84a11c1f5d051cd7f8194ba23
  languageName: node
  linkType: hard

"@types/chai-as-promised@npm:^7.1.3":
  version: 7.1.8
  resolution: "@types/chai-as-promised@npm:7.1.8"
  dependencies:
    "@types/chai": "*"
  checksum: f0e5eab451b91bc1e289ed89519faf6591932e8a28d2ec9bbe95826eb73d28fe43713633e0c18706f3baa560a7d97e7c7c20dc53ce639e5d75bac46b2a50bf21
  languageName: node
  linkType: hard

"@types/chai@npm:*":
  version: 5.0.1
  resolution: "@types/chai@npm:5.0.1"
  dependencies:
    "@types/deep-eql": "*"
  checksum: 53d813cbca3755c025381ad4ac8b51b17897df90316350247f9527bdba3adb48b3b1315308fbd717d9013d8e60375c0ab4bd004dc72330133486ff5db4cb0b2c
  languageName: node
  linkType: hard

"@types/chai@npm:^4.2.0":
  version: 4.3.20
  resolution: "@types/chai@npm:4.3.20"
  checksum: 7c5b0c9148f1a844a8d16cb1e16c64f2e7749cab2b8284155b9e494a6b34054846e22fb2b38df6b290f9bf57e6beebb2e121940c5896bc086ad7bab7ed429f06
  languageName: node
  linkType: hard

"@types/deep-eql@npm:*":
  version: 4.0.2
  resolution: "@types/deep-eql@npm:4.0.2"
  checksum: 249a27b0bb22f6aa28461db56afa21ec044fa0e303221a62dff81831b20c8530502175f1a49060f7099e7be06181078548ac47c668de79ff9880241968d43d0c
  languageName: node
  linkType: hard

"@types/eslint@npm:^8":
  version: 8.56.12
  resolution: "@types/eslint@npm:8.56.12"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: 0f7710ee02a256c499514251f527f84de964bb29487db840408e4cde79283124a38935597636d2265756c34dd1d902e1b00ae78930d4a0b55111909cb7b80d84
  languageName: node
  linkType: hard

"@types/estree@npm:*":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: 8825d6e729e16445d9a1dd2fb1db2edc5ed400799064cd4d028150701031af012ba30d6d03fe9df40f4d7a437d0de6d2b256020152b7b09bde9f2e420afdffd9
  languageName: node
  linkType: hard

"@types/glob@npm:^7.1.1":
  version: 7.2.0
  resolution: "@types/glob@npm:7.2.0"
  dependencies:
    "@types/minimatch": "*"
    "@types/node": "*"
  checksum: 6ae717fedfdfdad25f3d5a568323926c64f52ef35897bcac8aca8e19bc50c0bd84630bbd063e5d52078b2137d8e7d3c26eabebd1a2f03ff350fff8a91e79fc19
  languageName: node
  linkType: hard

"@types/json-schema@npm:*":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/level-errors@npm:*":
  version: 3.0.2
  resolution: "@types/level-errors@npm:3.0.2"
  checksum: 3d9b801f6499f795b60ac723c1b3f93ca105f20ed26966eeb606c804b10c65984c3233fb99914644d75a3223f80f220eca74fda316640a85a5b3d7572cd86925
  languageName: node
  linkType: hard

"@types/levelup@npm:^4.3.0":
  version: 4.3.3
  resolution: "@types/levelup@npm:4.3.3"
  dependencies:
    "@types/abstract-leveldown": "*"
    "@types/level-errors": "*"
    "@types/node": "*"
  checksum: 04969bb805035960b8d6650e8f76893be7ba70267bb7012f6f00d67a0cf096ada552355629791b3f5925e9cdb6912d3fe08892c33c3c583e8fd02099b573bdd7
  languageName: node
  linkType: hard

"@types/lru-cache@npm:5.1.1, @types/lru-cache@npm:^5.1.0":
  version: 5.1.1
  resolution: "@types/lru-cache@npm:5.1.1"
  checksum: e1d6c0085f61b16ec5b3073ec76ad1be4844ea036561c3f145fc19f71f084b58a6eb600b14128aa95809d057d28f1d147c910186ae51219f58366ffd2ff2e118
  languageName: node
  linkType: hard

"@types/minimatch@npm:*":
  version: 5.1.2
  resolution: "@types/minimatch@npm:5.1.2"
  checksum: 0391a282860c7cb6fe262c12b99564732401bdaa5e395bee9ca323c312c1a0f45efbf34dce974682036e857db59a5c9b1da522f3d6055aeead7097264c8705a8
  languageName: node
  linkType: hard

"@types/mkdirp@npm:^0.5.2":
  version: 0.5.2
  resolution: "@types/mkdirp@npm:0.5.2"
  dependencies:
    "@types/node": "*"
  checksum: 21e6681ee18cee6314dbe0f57ada48981912b76de8266f438ba2573770d60aaa8dd376baad3f20e2346696a7cca84b0aadd1737222341553a0091831a46e6ad1
  languageName: node
  linkType: hard

"@types/mocha@npm:>=9.1.0":
  version: 10.0.10
  resolution: "@types/mocha@npm:10.0.10"
  checksum: 17a56add60a8cc8362d3c62cb6798be3f89f4b6ccd5b9abd12b46e31ff299be21ff2faebf5993de7e0099559f58ca5a3b49a505d302dfa5d65c5a4edfc089195
  languageName: node
  linkType: hard

"@types/node-fetch@npm:^2.6.1":
  version: 2.6.12
  resolution: "@types/node-fetch@npm:2.6.12"
  dependencies:
    "@types/node": "*"
    form-data: ^4.0.0
  checksum: 9647e68f9a125a090220c38d77b3c8e669c488658ae7506f1b4f9568214beba087624b1705bba1dc76649a65281ce3fd5b400e15266cbef8088027fb88777557
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:^22.10.2":
  version: 22.10.2
  resolution: "@types/node@npm:22.10.2"
  dependencies:
    undici-types: ~6.20.0
  checksum: b22401e6e7d1484e437d802c72f5560e18100b1257b9ad0574d6fe05bebe4dbcb620ea68627d1f1406775070d29ace8b6b51f57e7b1c7b8bafafe6da7f29c843
  languageName: node
  linkType: hard

"@types/node@npm:11.11.6":
  version: 11.11.6
  resolution: "@types/node@npm:11.11.6"
  checksum: 075f1c011cf568e49701419acbcb55c24906b3bb5a34d9412a3b88f228a7a78401a5ad4d3e1cd6855c99aaea5ef96e37fc86ca097e50f06da92cf822befc1fff
  languageName: node
  linkType: hard

"@types/node@npm:22.7.5":
  version: 22.7.5
  resolution: "@types/node@npm:22.7.5"
  dependencies:
    undici-types: ~6.19.2
  checksum: 1a8bbb504efaffcef7b8491074a428e5c0b5425b0c0ffb13e7262cb8462c275e8cc5eaf90a38d8fbf52a1eeda7c01ab3b940673c43fc2414140779c973e40ec6
  languageName: node
  linkType: hard

"@types/node@npm:^12.7.1":
  version: 12.20.55
  resolution: "@types/node@npm:12.20.55"
  checksum: e4f86785f4092706e0d3b0edff8dca5a13b45627e4b36700acd8dfe6ad53db71928c8dee914d4276c7fd3b6ccd829aa919811c9eb708a2c8e4c6eb3701178c37
  languageName: node
  linkType: hard

"@types/pbkdf2@npm:^3.0.0":
  version: 3.1.2
  resolution: "@types/pbkdf2@npm:3.1.2"
  dependencies:
    "@types/node": "*"
  checksum: bebe1e596cbbe5f7d2726a58859e61986c5a42459048e29cb7f2d4d764be6bbb0844572fd5d70ca8955a8a17e8b4ed80984fc4903e165d9efb8807a3fbb051aa
  languageName: node
  linkType: hard

"@types/prettier@npm:^2.1.1":
  version: 2.7.3
  resolution: "@types/prettier@npm:2.7.3"
  checksum: 705384209cea6d1433ff6c187c80dcc0b95d99d5c5ce21a46a9a58060c527973506822e428789d842761e0280d25e3359300f017fbe77b9755bc772ab3dc2f83
  languageName: node
  linkType: hard

"@types/secp256k1@npm:^4.0.1":
  version: 4.0.6
  resolution: "@types/secp256k1@npm:4.0.6"
  dependencies:
    "@types/node": "*"
  checksum: 984494caf49a4ce99fda2b9ea1840eb47af946b8c2737314108949bcc0c06b4880e871296bd49ed6ea4c8423e3a302ad79fec43abfc987330e7eb98f0c4e8ba4
  languageName: node
  linkType: hard

"@types/seedrandom@npm:3.0.1":
  version: 3.0.1
  resolution: "@types/seedrandom@npm:3.0.1"
  checksum: d9755452f224a4f5072a1d8738da6c9de3039fc59a2a449b1f658e51087be7b48ada49bcabc8b0f16633c095f55598c32fcd072c448858422a2f6a0566569e4c
  languageName: node
  linkType: hard

"@types/ws@npm:8.5.3":
  version: 8.5.3
  resolution: "@types/ws@npm:8.5.3"
  dependencies:
    "@types/node": "*"
  checksum: 0ce46f850d41383fcdc2149bcacc86d7232fa7a233f903d2246dff86e31701a02f8566f40af5f8b56d1834779255c04ec6ec78660fe0f9b2a69cf3d71937e4ae
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: ef236c27f9432983e91432d974243e6c4cdae227cb673740320eff32d04d853eed59c92ca6f1142a335cfdc0e17cccafa62e95886a8154ca8891cc2dec4ee6fc
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.33":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: ee013f257472ab643cb0584cf3e1ff9b0c44bca1c9ba662395300a7f1a6c55fa9d41bd40ddff42d99f5d95febb3907c9ff600fbcb92dadbec22c6a76de7e1236
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.2.1
  resolution: "@ungap/structured-clone@npm:1.2.1"
  checksum: 1e3b9fef293118861f0b2159b3695fc7f3793c0707095888ebb3ac7183f78c390e68f04cd4b4cf9ac979ae0da454505e08b3aae887cdd639609a3fe529e19e59
  languageName: node
  linkType: hard

"@yarnpkg/lockfile@npm:^1.1.0":
  version: 1.1.0
  resolution: "@yarnpkg/lockfile@npm:1.1.0"
  checksum: 05b881b4866a3546861fee756e6d3812776ea47fa6eb7098f983d6d0eefa02e12b66c3fff931574120f196286a7ad4879ce02743c8bb2be36c6a576c7852083a
  languageName: node
  linkType: hard

"@zksync/contracts@git+https://github.com/matter-labs/era-contracts.git#446d391d34bdb48255d5f8fef8a8248925fc98b9":
  version: 0.1.0
  resolution: "@zksync/contracts@https://github.com/matter-labs/era-contracts.git#commit=446d391d34bdb48255d5f8fef8a8248925fc98b9"
  checksum: c1abb6217fe0f63432c0da589a70d75d83216700c54daaa861cdebc6c7e13c34dfb0c1891fc51c3a71801b57b3b7da9b519769cc455d37f9bc83681be6a222db
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"abbrev@npm:1.0.x":
  version: 1.0.9
  resolution: "abbrev@npm:1.0.9"
  checksum: 46460c897b4ce62cd9b1bd4a853cc46e771a1f1d929f5443f3945a976f8be5388891bf9e5f8a9862baa29587349e16c48596b6a621404d46d3b184fe9bd9fb26
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 0e994ad2aa6575f94670d8a2149afe94465de9cedaaaac364e7fb43a40c3691c980ff74899f682f4ca58fa96b4cbd7421a015d3a6defe43a442117d7821a2f36
  languageName: node
  linkType: hard

"abitype@npm:0.7.1":
  version: 0.7.1
  resolution: "abitype@npm:0.7.1"
  peerDependencies:
    typescript: ">=4.9.4"
    zod: ^3 >=3.19.1
  peerDependenciesMeta:
    zod:
      optional: true
  checksum: de0d7082d28a4835b3d8dc4d8c75e9222c95a1f9eed13d6b2381403b46f46b68ea7a281e8ba6628d259a98c54ea466ebc206eec21db6205fa1641c7393854f5e
  languageName: node
  linkType: hard

"abitype@npm:1.0.8, abitype@npm:^1.0.6":
  version: 1.0.8
  resolution: "abitype@npm:1.0.8"
  peerDependencies:
    typescript: ">=5.0.4"
    zod: ^3 >=3.22.0
  peerDependenciesMeta:
    typescript:
      optional: true
    zod:
      optional: true
  checksum: 104bc2f6820ced8d2cb61521916f7f22c0981a846216f5b6144f69461265f7da137a4ae108bf4b84cd8743f2dd1e9fdadffc0f95371528e15a59e0a369e08438
  languageName: node
  linkType: hard

"abstract-leveldown@npm:^6.2.1":
  version: 6.3.0
  resolution: "abstract-leveldown@npm:6.3.0"
  dependencies:
    buffer: ^5.5.0
    immediate: ^3.2.3
    level-concat-iterator: ~2.0.0
    level-supports: ~1.0.0
    xtend: ~4.0.0
  checksum: 121a8509d8c6a540e656c2a69e5b8d853d4df71072011afefc868b98076991bb00120550e90643de9dc18889c675f62413409eeb4c8c204663124c7d215e4ec3
  languageName: node
  linkType: hard

"abstract-leveldown@npm:^7.2.0":
  version: 7.2.0
  resolution: "abstract-leveldown@npm:7.2.0"
  dependencies:
    buffer: ^6.0.3
    catering: ^2.0.0
    is-buffer: ^2.0.5
    level-concat-iterator: ^3.0.0
    level-supports: ^2.0.1
    queue-microtask: ^1.2.3
  checksum: d558111f2d123da95ac80b8ba3b9b0a5bc8cd87296e64b05dca693f5f4839aa0e2fc97bad56a101766f499824e2962611750f8a76bbac4a5db35801968fbbe02
  languageName: node
  linkType: hard

"abstract-leveldown@npm:~6.2.1":
  version: 6.2.3
  resolution: "abstract-leveldown@npm:6.2.3"
  dependencies:
    buffer: ^5.5.0
    immediate: ^3.2.3
    level-concat-iterator: ~2.0.0
    level-supports: ~1.0.0
    xtend: ~4.0.0
  checksum: 00202b2eb7955dd7bc04f3e44d225e60160cedb8f96fe6ae0e6dca9c356d57071f001ece8ae1d53f48095c4c036d92b3440f2bc7666730610ddea030f9fbde4a
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: ^8.11.0
  checksum: 4ff03f42323e7cf90f1683e08606b0f460e1e6ac263d2730e3df91c7665b6f64e696db6ea27ee4bed18c2599569be61f28a8399fa170c611161a348c402ca19c
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.4.1, acorn@npm:^8.9.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 8755074ba55fff94e84e81c72f1013c2d9c78e973c31231c8ae505a5f966859baf654bddd75046bffd73ce816b149298977fff5077a3033dedba0ae2aad152d4
  languageName: node
  linkType: hard

"adm-zip@npm:^0.4.16":
  version: 0.4.16
  resolution: "adm-zip@npm:0.4.16"
  checksum: 5ea46664d8b3b073fffeb7f934705fea288708745e708cffc1dd732ce3d2672cecd476b243f9d051892fd12952db2b6bd061975e1ff40057246f6d0cb6534a50
  languageName: node
  linkType: hard

"aes-js@npm:4.0.0-beta.5":
  version: 4.0.0-beta.5
  resolution: "aes-js@npm:4.0.0-beta.5"
  checksum: cc2ea969d77df939c32057f7e361b6530aa6cb93cb10617a17a45cd164e6d761002f031ff6330af3e67e58b1f0a3a8fd0b63a720afd591a653b02f649470e15b
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.3, ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.1":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: ^3.1.3
    fast-uri: ^3.0.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
  checksum: 1797bf242cfffbaf3b870d13565bd1716b73f214bb7ada9a497063aada210200da36e3ed40237285f3255acc4feeae91b1fb183625331bad27da95973f7253d9
  languageName: node
  linkType: hard

"amazon-cognito-identity-js@npm:^6.3.6":
  version: 6.3.14
  resolution: "amazon-cognito-identity-js@npm:6.3.14"
  dependencies:
    "@aws-crypto/sha256-js": 1.2.2
    buffer: 4.9.2
    fast-base64-decode: ^1.0.0
    isomorphic-unfetch: ^3.0.0
    js-cookie: ^2.2.1
  checksum: 593f4dfc8c008de22b23592fa511a9b0a138cb48714de60b95a5b94ca02e742e39775ae7c919de0047890e18ed52057df858614b7e953e0272e384e590362124
  languageName: node
  linkType: hard

"amdefine@npm:>=0.0.4":
  version: 1.0.1
  resolution: "amdefine@npm:1.0.1"
  checksum: 9d4e15b94641643a9385b2841b4cb2bcf4e8e2f741ea4bd475c93ad7bab261ad4ed827a32e9c549b38b98759c4526c173ae4e6dde8caeb75ee5cebedc9863762
  languageName: node
  linkType: hard

"ansi-align@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-align@npm:3.0.1"
  dependencies:
    string-width: ^4.1.0
  checksum: 6abfa08f2141d231c257162b15292467081fa49a208593e055c866aa0455b57f3a86b5a678c190c618faa79b4c59e254493099cb700dd9cf2293c6be2c8f5d8d
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.1, ansi-colors@npm:^4.1.3":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: a9c2ec842038a1fabc7db9ece7d3177e2fe1c5dc6f0c51ecfbf5f39911427b89c00b5dc6b8bd95f82a26e9b16aaae2e83d45f060e98070ce4d1333038edceb0e
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 544af8dd3f60546d3e4aff084d451b96961d2267d668670199692f8d054f0415d86fc5497d0e641e91546f0aa920e7c29e5250e99fc89f5552a34b5d93b77f43
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"array-back@npm:^3.0.1, array-back@npm:^3.1.0":
  version: 3.1.0
  resolution: "array-back@npm:3.1.0"
  checksum: 7205004fcd0f9edd926db921af901b083094608d5b265738d0290092f9822f73accb468e677db74c7c94ef432d39e5ed75a7b1786701e182efb25bbba9734209
  languageName: node
  linkType: hard

"array-back@npm:^4.0.1, array-back@npm:^4.0.2":
  version: 4.0.2
  resolution: "array-back@npm:4.0.2"
  checksum: f30603270771eeb54e5aad5f54604c62b3577a18b6db212a7272b2b6c32049121b49431f656654790ed1469411e45f387e7627c0de8fd0515995cc40df9b9294
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"asn1@npm:~0.2.3":
  version: 0.2.6
  resolution: "asn1@npm:0.2.6"
  dependencies:
    safer-buffer: ~2.1.0
  checksum: 39f2ae343b03c15ad4f238ba561e626602a3de8d94ae536c46a4a93e69578826305366dc09fbb9b56aec39b4982a463682f259c38e59f6fa380cd72cd61e493d
  languageName: node
  linkType: hard

"assert-plus@npm:1.0.0, assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: 19b4340cb8f0e6a981c07225eacac0e9d52c2644c080198765d63398f0075f83bbc0c8e95474d54224e297555ad0d631c1dcd058adb1ddc2437b41a6b424ac64
  languageName: node
  linkType: hard

"assertion-error@npm:^1.1.0":
  version: 1.1.0
  resolution: "assertion-error@npm:1.1.0"
  checksum: fd9429d3a3d4fd61782eb3962ae76b6d08aa7383123fca0596020013b3ebd6647891a85b05ce821c47d1471ed1271f00b0545cf6a4326cf2fc91efcc3b0fbecf
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"async-eventemitter@npm:^0.2.4":
  version: 0.2.4
  resolution: "async-eventemitter@npm:0.2.4"
  dependencies:
    async: ^2.4.0
  checksum: b9e77e0f58ebd7188c50c23d613d1263e0ab501f5e677e02b57cc97d7032beaf60aafa189887e7105569c791e212df4af00b608be1e9a4c425911d577124911e
  languageName: node
  linkType: hard

"async-retry@npm:^1.3.3":
  version: 1.3.3
  resolution: "async-retry@npm:1.3.3"
  dependencies:
    retry: 0.13.1
  checksum: 38a7152ff7265a9321ea214b9c69e8224ab1febbdec98efbbde6e562f17ff68405569b796b1c5271f354aef8783665d29953f051f68c1fc45306e61aec82fdc4
  languageName: node
  linkType: hard

"async@npm:1.x":
  version: 1.5.2
  resolution: "async@npm:1.5.2"
  checksum: fe5d6214d8f15bd51eee5ae8ec5079b228b86d2d595f47b16369dec2e11b3ff75a567bb5f70d12d79006665fbbb7ee0a7ec0e388524eefd454ecbe651c124ebd
  languageName: node
  linkType: hard

"async@npm:^2.4.0":
  version: 2.6.4
  resolution: "async@npm:2.6.4"
  dependencies:
    lodash: ^4.17.14
  checksum: a52083fb32e1ebe1d63e5c5624038bb30be68ff07a6c8d7dfe35e47c93fc144bd8652cbec869e0ac07d57dde387aa5f1386be3559cdee799cb1f789678d88e19
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 463e2f8e43384f1afb54bc68485c436d7622acec08b6fad269b421cb1d29cebb5af751426793d0961ed243146fe4dc983402f6d5a51b720b277818dbf6f2e49e
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"aws-sign2@npm:~0.7.0":
  version: 0.7.0
  resolution: "aws-sign2@npm:0.7.0"
  checksum: b148b0bb0778098ad8cf7e5fc619768bcb51236707ca1d3e5b49e41b171166d8be9fdc2ea2ae43d7decf02989d0aaa3a9c4caa6f320af95d684de9b548a71525
  languageName: node
  linkType: hard

"aws4@npm:^1.8.0":
  version: 1.13.2
  resolution: "aws4@npm:1.13.2"
  checksum: 9ac924e4a91c088b4928ea86b68d8c4558b0e6289ccabaae0e3e96a611bd75277c2eab6e3965821028768700516f612b929a5ce822f33a8771f74ba2a8cedb9c
  languageName: node
  linkType: hard

"axios@npm:^1.6.7, axios@npm:^1.7.4":
  version: 1.7.9
  resolution: "axios@npm:1.7.9"
  dependencies:
    follow-redirects: ^1.15.6
    form-data: ^4.0.0
    proxy-from-env: ^1.1.0
  checksum: cb8ce291818effda09240cb60f114d5625909b345e10f389a945320e06acf0bc949d0f8422d25720f5dd421362abee302c99f5e97edec4c156c8939814b23d19
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base-x@npm:^3.0.2":
  version: 3.0.10
  resolution: "base-x@npm:3.0.10"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 52307739559e81d9980889de2359cb4f816cc0eb9a463028fa3ab239ab913d9044a1b47b4520f98e68453df32a457b8ba58b8d0ee7e757fc3fb971f3fa7a1482
  languageName: node
  linkType: hard

"base64-js@npm:^1.0.2, base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bcrypt-pbkdf@npm:^1.0.0":
  version: 1.0.2
  resolution: "bcrypt-pbkdf@npm:1.0.2"
  dependencies:
    tweetnacl: ^0.14.3
  checksum: 4edfc9fe7d07019609ccf797a2af28351736e9d012c8402a07120c4453a3b789a15f2ee1530dc49eee8f7eb9379331a8dd4b3766042b9e502f74a68e7f662291
  languageName: node
  linkType: hard

"bech32@npm:1.1.4":
  version: 1.1.4
  resolution: "bech32@npm:1.1.4"
  checksum: 0e98db619191548390d6f09ff68b0253ba7ae6a55db93dfdbb070ba234c1fd3308c0606fbcc95fad50437227b10011e2698b89f0181f6e7f845c499bd14d0f4b
  languageName: node
  linkType: hard

"better-path-resolve@npm:1.0.0":
  version: 1.0.0
  resolution: "better-path-resolve@npm:1.0.0"
  dependencies:
    is-windows: ^1.0.0
  checksum: 5392dbe04e7fe68b944eb37961d9dfa147aaac3ee9ee3f6e13d42e2c9fbe949e68d16e896c14ee9016fa5f8e6e53ec7fd8b5f01b50a32067a7d94ac9cfb9a050
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.0.0":
  version: 9.1.2
  resolution: "bignumber.js@npm:9.1.2"
  checksum: 582c03af77ec9cb0ebd682a373ee6c66475db94a4325f92299621d544aa4bd45cb45fd60001610e94aef8ae98a0905fa538241d9638d4422d57abbeeac6fadaf
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"bip39@npm:3.0.4":
  version: 3.0.4
  resolution: "bip39@npm:3.0.4"
  dependencies:
    "@types/node": 11.11.6
    create-hash: ^1.1.0
    pbkdf2: ^3.0.9
    randombytes: ^2.0.1
  checksum: 79ce1600a03d1ba5053bdd4e6323f9463ec340764c7e52918b6c6b9dca81221940f2d9a65656447f108f9bc2c8d9ae8df319cca83bbd1dad63f53ef2768d9bae
  languageName: node
  linkType: hard

"blakejs@npm:^1.1.0":
  version: 1.2.1
  resolution: "blakejs@npm:1.2.1"
  checksum: d699ba116cfa21d0b01d12014a03e484dd76d483133e6dc9eb415aa70a119f08beb3bcefb8c71840106a00b542cba77383f8be60cd1f0d4589cb8afb922eefbe
  languageName: node
  linkType: hard

"bn.js@npm:4.11.6":
  version: 4.11.6
  resolution: "bn.js@npm:4.11.6"
  checksum: db23047bf06fdf9cf74401c8e76bca9f55313c81df382247d2c753868b368562e69171716b81b7038ada8860af18346fd4bcd1cf9d4963f923fe8e54e61cb58a
  languageName: node
  linkType: hard

"bn.js@npm:^4.0.0, bn.js@npm:^4.11.0, bn.js@npm:^4.11.1, bn.js@npm:^4.11.8, bn.js@npm:^4.11.9":
  version: 4.12.1
  resolution: "bn.js@npm:4.12.1"
  checksum: f7f84a909bd07bdcc6777cccbf280b629540792e6965fb1dd1aeafba96e944f197ca10cbec2692f51e0a906ff31da1eb4317f3d1cd659d6f68b8bcd211f7ecbc
  languageName: node
  linkType: hard

"bn.js@npm:^5.1.2, bn.js@npm:^5.2.0, bn.js@npm:^5.2.1":
  version: 5.2.1
  resolution: "bn.js@npm:5.2.1"
  checksum: 3dd8c8d38055fedfa95c1d5fc3c99f8dd547b36287b37768db0abab3c239711f88ff58d18d155dd8ad902b0b0cee973747b7ae20ea12a09473272b0201c9edd3
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 29c3f01f22e703fa6644fc3b684307442df4240b6e10f6cfe1b61c6ca5721073189ca97cdeedb376081148c8518e33b1d818a57f781d70b0b70e1f31fb48814f
  languageName: node
  linkType: hard

"boxen@npm:^5.1.2":
  version: 5.1.2
  resolution: "boxen@npm:5.1.2"
  dependencies:
    ansi-align: ^3.0.0
    camelcase: ^6.2.0
    chalk: ^4.1.0
    cli-boxes: ^2.2.1
    string-width: ^4.2.2
    type-fest: ^0.20.2
    widest-line: ^3.1.0
    wrap-ansi: ^7.0.0
  checksum: 82d03e42a72576ff235123f17b7c505372fe05c83f75f61e7d4fa4bcb393897ec95ce766fecb8f26b915f0f7a7227d66e5ec7cef43f5b2bd9d3aeed47ec55877
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"brorand@npm:^1.0.1, brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 8a05c9f3c4b46572dec6ef71012b1946db6cae8c7bb60ccd4b7dd5a84655db49fe043ecc6272e7ef1f69dc53d6730b9e2a3a03a8310509a3d797a618cbee52be
  languageName: node
  linkType: hard

"brotli-wasm@npm:^2.0.1":
  version: 2.0.1
  resolution: "brotli-wasm@npm:2.0.1"
  checksum: 3a0506c66ad3a27512deebee3a9c9a0c59cd1dc7de0c1934c37f0a7b8772de8aa22093fb1fb466c8fdd1cd80f99e5a7c814ff1235350853fb1cd4129d99609f5
  languageName: node
  linkType: hard

"browser-stdout@npm:^1.3.1":
  version: 1.3.1
  resolution: "browser-stdout@npm:1.3.1"
  checksum: b717b19b25952dd6af483e368f9bcd6b14b87740c3d226c2977a65e84666ffd67000bddea7d911f111a9b6ddc822b234de42d52ab6507bce4119a4cc003ef7b3
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.2.0":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: ^1.0.3
    cipher-base: ^1.0.0
    create-hash: ^1.1.0
    evp_bytestokey: ^1.0.3
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  checksum: 4a17c3eb55a2aa61c934c286f34921933086bf6d67f02d4adb09fcc6f2fc93977b47d9d884c25619144fccd47b3b3a399e1ad8b3ff5a346be47270114bcf7104
  languageName: node
  linkType: hard

"bs58@npm:^4.0.0":
  version: 4.0.1
  resolution: "bs58@npm:4.0.1"
  dependencies:
    base-x: ^3.0.2
  checksum: b3c5365bb9e0c561e1a82f1a2d809a1a692059fae016be233a6127ad2f50a6b986467c3a50669ce4c18929dcccb297c5909314dd347a25a68c21b68eb3e95ac2
  languageName: node
  linkType: hard

"bs58check@npm:^2.1.2":
  version: 2.1.2
  resolution: "bs58check@npm:2.1.2"
  dependencies:
    bs58: ^4.0.0
    create-hash: ^1.1.0
    safe-buffer: ^5.1.2
  checksum: 43bdf08a5dd04581b78f040bc4169480e17008da482ffe2a6507327bbc4fc5c28de0501f7faf22901cfe57fbca79cbb202ca529003fedb4cb8dccd265b38e54d
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: 10c520df29d62fa6e785e2800e586a20fc4f6dfad84bcdbd12e1e8a83856de1cb75c7ebd7abe6d036bbfab738a6cf18a3ae9c8e5a2e2eb3167ca7399ce65373a
  languageName: node
  linkType: hard

"buffer-xor@npm:^2.0.1":
  version: 2.0.2
  resolution: "buffer-xor@npm:2.0.2"
  dependencies:
    safe-buffer: ^5.1.1
  checksum: 78226fcae9f4a0b4adec69dffc049f26f6bab240dfdd1b3f6fe07c4eb6b90da202ea5c363f98af676156ee39450a06405fddd9e8965f68a5327edcc89dcbe5d0
  languageName: node
  linkType: hard

"buffer@npm:4.9.2":
  version: 4.9.2
  resolution: "buffer@npm:4.9.2"
  dependencies:
    base64-js: ^1.0.2
    ieee754: ^1.1.4
    isarray: ^1.0.0
  checksum: 8801bc1ba08539f3be70eee307a8b9db3d40f6afbfd3cf623ab7ef41dffff1d0a31de0addbe1e66e0ca5f7193eeb667bfb1ecad3647f8f1b0750de07c13295c3
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0, buffer@npm:^5.6.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.2.1
  checksum: 5ad23293d9a731e4318e420025800b42bf0d264004c0286c8cc010af7a270c7a0f6522e84f54b9ad65cbd6db20b8badbfd8d2ebf4f80fa03dab093b89e68c3f9
  languageName: node
  linkType: hard

"bufferutil@npm:4.0.5":
  version: 4.0.5
  resolution: "bufferutil@npm:4.0.5"
  dependencies:
    node-gyp: latest
    node-gyp-build: ^4.3.0
  checksum: 37d5bef7cb38d29f9377b8891ff8a57f53ae6057313d77a8aa2a7417df37a72f16987100796cb2f1e1862f3eb80057705f3c052615ec076a0dcc7aa6c83b68c9
  languageName: node
  linkType: hard

"bufio@npm:^1.0.7":
  version: 1.2.2
  resolution: "bufio@npm:1.2.2"
  checksum: 8768f558bdb0cf905377efec3641a670d89068722c7fcb1ae0c606e3f172acfd78e2d4cf89e20ed621b9416241b9dae6fa4ede32c1426e3824c9d4d1001dbb0f
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: 3c55343261bb387c58a4762d15ad9d42053659a62681ec5eb50690c6b52a4a666302a01d557133ce6533e8bd04530ee3b209f23dd06c9577a1925556f8fcccdf
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    get-intrinsic: ^1.2.6
  checksum: a93bbe0f2d0a2d6c144a4349ccd0593d5d0d5d9309b69101710644af8964286420062f2cc3114dca120b9bc8cc07507952d4b1b3ea7672e0d7f6f1675efedb32
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^6.0.0, camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caseless@npm:~0.12.0":
  version: 0.12.0
  resolution: "caseless@npm:0.12.0"
  checksum: b43bd4c440aa1e8ee6baefee8063b4850fd0d7b378f6aabc796c9ec8cb26d27fb30b46885350777d9bd079c5256c0e1329ad0dc7c2817e0bb466810ebb353751
  languageName: node
  linkType: hard

"catering@npm:^2.0.0, catering@npm:^2.1.0":
  version: 2.1.1
  resolution: "catering@npm:2.1.1"
  checksum: 205daefa69c935b0c19f3d8f2e0a520dd69aebe9bda55902958003f7c9cff8f967dfb90071b421bd6eb618576f657a89d2bc0986872c9bc04bbd66655e9d4bd6
  languageName: node
  linkType: hard

"cbor@npm:^8.1.0":
  version: 8.1.0
  resolution: "cbor@npm:8.1.0"
  dependencies:
    nofilter: ^3.1.0
  checksum: a90338435dc7b45cc01461af979e3bb6ddd4f2a08584c437586039cd5f2235014c06e49d664295debbfb3514d87b2f06728092ab6aa6175e2e85e9cd7dc0c1fd
  languageName: node
  linkType: hard

"cbor@npm:^9.0.0":
  version: 9.0.2
  resolution: "cbor@npm:9.0.2"
  dependencies:
    nofilter: ^3.1.0
  checksum: 925edae7bf964be5a26dba1b7ba6311ac12b6a66234dc958958997a0576cdc740632dc19852a5b84d8a75101936bea1fe122dc22539d6e11f4539c731853ba2e
  languageName: node
  linkType: hard

"chai-as-promised@npm:^7.1.1":
  version: 7.1.2
  resolution: "chai-as-promised@npm:7.1.2"
  dependencies:
    check-error: ^1.0.2
  peerDependencies:
    chai: ">= 2.1.2 < 6"
  checksum: 671ee980054eb23a523875c1d22929a2ac05d89b5428e1fd12800f54fc69baf41014667b87e2368e2355ee2a3140d3e3d7d5a1f8638b07cfefd7fe38a149e3f6
  languageName: node
  linkType: hard

"chai@npm:^4.2.0, chai@npm:^4.3.4":
  version: 4.5.0
  resolution: "chai@npm:4.5.0"
  dependencies:
    assertion-error: ^1.1.0
    check-error: ^1.0.3
    deep-eql: ^4.1.3
    get-func-name: ^2.0.2
    loupe: ^2.3.6
    pathval: ^1.1.1
    type-detect: ^4.1.0
  checksum: 70e5a8418a39e577e66a441cc0ce4f71fd551a650a71de30dd4e3e31e75ed1f5aa7119cf4baf4a2cb5e85c0c6befdb4d8a05811fad8738c1a6f3aa6a23803821
  languageName: node
  linkType: hard

"chalk@npm:4.1.2, chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 6fd5da1f5d18ff5712c1e0aed41da200d7c51c28f11b36ee3c7b483f3696dabc08927fc6b227735eb8f0e1215c9a8abd8154637f3eff8cada5959df7f58b024d
  languageName: node
  linkType: hard

"charenc@npm:>= 0.0.1":
  version: 0.0.2
  resolution: "charenc@npm:0.0.2"
  checksum: 81dcadbe57e861d527faf6dd3855dc857395a1c4d6781f4847288ab23cffb7b3ee80d57c15bba7252ffe3e5e8019db767757ee7975663ad2ca0939bb8fcaf2e5
  languageName: node
  linkType: hard

"check-error@npm:^1.0.2, check-error@npm:^1.0.3":
  version: 1.0.3
  resolution: "check-error@npm:1.0.3"
  dependencies:
    get-func-name: ^2.0.2
  checksum: e2131025cf059b21080f4813e55b3c480419256914601750b0fee3bd9b2b8315b531e551ef12560419b8b6d92a3636511322752b1ce905703239e7cc451b6399
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: ^4.0.1
  checksum: a8765e452bbafd04f3f2fad79f04222dd65f43161488bb6014a41099e6ca18d166af613d59a90771908c1c823efa3f46ba36b86ac50b701c20c1b9908c5fe36e
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"ci-info@npm:^3.7.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 6b19dc9b2966d1f8c2041a838217299718f15d6c4b63ae36e4674edd2bee48f780e94761286a56aa59eb305a85fbea4ddffb7630ec063e7ec7e7e5ad42549a87
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.6
  resolution: "cipher-base@npm:1.0.6"
  dependencies:
    inherits: ^2.0.4
    safe-buffer: ^5.2.1
  checksum: 64a1738a8583163cf096bc85321a69ef3075bb0873f34cf89dc705e62b9eee058dd6b2e5c672f774ede0b6bdbe56fe7b710e0d38c4f08a2f355d8ab828f05c6f
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-boxes@npm:^2.2.1":
  version: 2.2.1
  resolution: "cli-boxes@npm:2.2.1"
  checksum: be79f8ec23a558b49e01311b39a1ea01243ecee30539c880cf14bf518a12e223ef40c57ead0cb44f509bffdffc5c129c746cd50d863ab879385370112af4f585
  languageName: node
  linkType: hard

"cli-table3@npm:^0.6.0, cli-table3@npm:^0.6.3":
  version: 0.6.5
  resolution: "cli-table3@npm:0.6.5"
  dependencies:
    "@colors/colors": 1.5.0
    string-width: ^4.2.0
  dependenciesMeta:
    "@colors/colors":
      optional: true
  checksum: ab7afbf4f8597f1c631f3ee6bb3481d0bfeac8a3b81cffb5a578f145df5c88003b6cfff46046a7acae86596fdd03db382bfa67f20973b6b57425505abc47e42c
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^7.0.0
  checksum: ce2e8f578a4813806788ac399b9e866297740eecd4ad1823c27fd344d78b22c5f8597d548adbcc46f0573e43e21e751f39446c5a5e804a12aace402b7a315d7f
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6, combined-stream@npm:^1.0.8, combined-stream@npm:~1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"command-exists@npm:^1.2.8":
  version: 1.2.9
  resolution: "command-exists@npm:1.2.9"
  checksum: 729ae3d88a2058c93c58840f30341b7f82688a573019535d198b57a4d8cb0135ced0ad7f52b591e5b28a90feb2c675080ce916e56254a0f7c15cb2395277cac3
  languageName: node
  linkType: hard

"command-line-args@npm:^5.1.1":
  version: 5.2.1
  resolution: "command-line-args@npm:5.2.1"
  dependencies:
    array-back: ^3.1.0
    find-replace: ^3.0.0
    lodash.camelcase: ^4.3.0
    typical: ^4.0.0
  checksum: e759519087be3cf2e86af8b9a97d3058b4910cd11ee852495be881a067b72891f6a32718fb685ee6d41531ab76b2b7bfb6602f79f882cd4b7587ff1e827982c7
  languageName: node
  linkType: hard

"command-line-usage@npm:^6.1.0":
  version: 6.1.3
  resolution: "command-line-usage@npm:6.1.3"
  dependencies:
    array-back: ^4.0.2
    chalk: ^2.4.2
    table-layout: ^1.0.2
    typical: ^5.2.0
  checksum: 8261d4e5536eb0bcddee0ec5e89c05bb2abd18e5760785c8078ede5020bc1c612cbe28eb6586f5ed4a3660689748e5aaad4a72f21566f4ef39393694e2fa1a0b
  languageName: node
  linkType: hard

"commander@npm:^8.1.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 0f82321821fc27b83bd409510bb9deeebcfa799ff0bf5d102128b500b7af22872c0c92cb6a0ebc5a4cf19c6b550fba9cedfa7329d18c6442a625f851377bacf0
  languageName: node
  linkType: hard

"compare-versions@npm:^6.0.0":
  version: 6.1.1
  resolution: "compare-versions@npm:6.1.1"
  checksum: 73fe6c4f52d22efe28f0a3be10df2afd704e10b3593360cd963e86b33a7a263c263b41a1361b69c30a0fe68bfa70fef90860c1cf2ef41502629d4402890fcd57
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"cookie@npm:^0.4.1":
  version: 0.4.2
  resolution: "cookie@npm:0.4.2"
  checksum: a00833c998bedf8e787b4c342defe5fa419abd96b32f4464f718b91022586b8f1bafbddd499288e75c037642493c83083da426c6a9080d309e3bd90fd11baa9b
  languageName: node
  linkType: hard

"core-js-pure@npm:^3.0.1":
  version: 3.39.0
  resolution: "core-js-pure@npm:3.39.0"
  checksum: cdcb1eec4eb9308fcf5cfe18a95322c388b05c11e66b5b0ac296a08f8f2106b458ecfe8aca0155d62ed2c5e150485b68073937e7b0a563fbc287563c4475a7c1
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: 7a4c925b497a2c91421e25bf76d6d8190f0b2359a9200dbeed136e63b2931d6294d3b1893eda378883ed363cd950f44a12a401384c609839ea616befb7927dab
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0, crc-32@npm:^1.2.2":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: ad2d0ad0cbd465b75dcaeeff0600f8195b686816ab5f3ba4c6e052a07f728c3e70df2e3ca9fd3d4484dc4ba70586e161ca5a2334ec8bf5a41bf022a6103ff243
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.1.2, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: ^1.0.1
    inherits: ^2.0.1
    md5.js: ^1.3.4
    ripemd160: ^2.0.1
    sha.js: ^2.4.0
  checksum: 02a6ae3bb9cd4afee3fabd846c1d8426a0e6b495560a977ba46120c473cb283be6aa1cace76b5f927cf4e499c6146fb798253e48e83d522feba807d6b722eaa9
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.4, create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: ^1.0.3
    create-hash: ^1.1.0
    inherits: ^2.0.1
    ripemd160: ^2.0.0
    safe-buffer: ^5.0.1
    sha.js: ^2.4.8
  checksum: ba12bb2257b585a0396108c72830e85f882ab659c3320c83584b1037f8ab72415095167ced80dc4ce8e446a8ecc4b2acf36d87befe0707d73b26cf9dc77440ed
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: a9a1503d4390d8b59ad86f4607de7870b39cad43d929813599a23714831e81c520bddf61bcdd1f8e30f05fd3a2b71ae8538e946eb2786dc65c2bbc520f692eff
  languageName: node
  linkType: hard

"cross-fetch@npm:^4.0.0":
  version: 4.1.0
  resolution: "cross-fetch@npm:4.1.0"
  dependencies:
    node-fetch: ^2.7.0
  checksum: c02fa85d59f83e50dbd769ee472c9cc984060c403ee5ec8654659f61a525c1a655eef1c7a35e365c1a107b4e72d76e786718b673d1cb3c97f61d4644cb0a9f9d
  languageName: node
  linkType: hard

"cross-spawn@npm:^6.0.5":
  version: 6.0.6
  resolution: "cross-spawn@npm:6.0.6"
  dependencies:
    nice-try: ^1.0.4
    path-key: ^2.0.1
    semver: ^5.5.0
    shebang-command: ^1.2.0
    which: ^1.2.9
  checksum: a6e2e5b04a0e0f806c1df45f92cd079b65f95fbe5a7650ee1ab60318c33a6c156a8a2f8b6898f57764f7363ec599a0625e9855dfa78d52d2d73dbd32eb11c25e
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.5":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"crypt@npm:>= 0.0.1":
  version: 0.0.2
  resolution: "crypt@npm:0.0.2"
  checksum: baf4c7bbe05df656ec230018af8cf7dbe8c14b36b98726939cef008d473f6fe7a4fad906cfea4062c93af516f1550a3f43ceb4d6615329612c6511378ed9fe34
  languageName: node
  linkType: hard

"dashdash@npm:^1.12.0":
  version: 1.14.1
  resolution: "dashdash@npm:1.14.1"
  dependencies:
    assert-plus: ^1.0.0
  checksum: 3634c249570f7f34e3d34f866c93f866c5b417f0dd616275decae08147dcdf8fccfaa5947380ccfb0473998ea3a8057c0b4cd90c875740ee685d0624b2983598
  languageName: node
  linkType: hard

"dataloader@npm:^1.4.0":
  version: 1.4.0
  resolution: "dataloader@npm:1.4.0"
  checksum: e2c93d43afde68980efc0cd9ff48e9851116e27a9687f863e02b56d46f7e7868cc762cd6dcbaf4197e1ca850a03651510c165c2ae24b8e9843fd894002ad0e20
  languageName: node
  linkType: hard

"death@npm:^1.1.0":
  version: 1.1.0
  resolution: "death@npm:1.1.0"
  checksum: 8010ba9a320752f9580eb474985ed214572c0595cf83e92859e3c5a014a01fc8e8f2f2908b80b5f8bca9cb3f94adb546cf55810df6b80e282452e355cdce5aaa
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4, debug@npm:^4.3.5":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: fb42df878dd0e22816fc56e1fdca9da73caa85212fbe40c868b1295a6878f9101ae684f4eeef516c13acfc700f5ea07f1136954f43d4cd2d477a811144136479
  languageName: node
  linkType: hard

"debug@npm:^2.2.0":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:^3.1.0":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decamelize@npm:^4.0.0":
  version: 4.0.0
  resolution: "decamelize@npm:4.0.0"
  checksum: b7d09b82652c39eead4d6678bb578e3bebd848add894b76d0f6b395bc45b2d692fb88d977e7cfb93c4ed6c119b05a1347cef261174916c2e75c0a8ca57da1809
  languageName: node
  linkType: hard

"deep-eql@npm:^4.0.1, deep-eql@npm:^4.1.3":
  version: 4.1.4
  resolution: "deep-eql@npm:4.1.4"
  dependencies:
    type-detect: ^4.0.0
  checksum: 01c3ca78ff40d79003621b157054871411f94228ceb9b2cab78da913c606631c46e8aa79efc4aa0faf3ace3092acd5221255aab3ef0e8e7b438834f0ca9a16c7
  languageName: node
  linkType: hard

"deep-extend@npm:~0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3, deep-is@npm:~0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deferred-leveldown@npm:~5.3.0":
  version: 5.3.0
  resolution: "deferred-leveldown@npm:5.3.0"
  dependencies:
    abstract-leveldown: ~6.2.1
    inherits: ^2.0.3
  checksum: 5631e153528bb9de1aa60d59a5065d1a519374c5e4c1d486f2190dba4008dcf5c2ee8dd7f2f81396fc4d5a6bb6e7d0055e3dfe68afe00da02adaa3bf329addf7
  languageName: node
  linkType: hard

"define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"detect-indent@npm:^6.0.0":
  version: 6.1.0
  resolution: "detect-indent@npm:6.1.0"
  checksum: ab953a73c72dbd4e8fc68e4ed4bfd92c97eb6c43734af3900add963fd3a9316f3bc0578b018b24198d4c31a358571eff5f0656e81a1f3b9ad5c547d58b2d093d
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: f2c09b0ce4e6b301c221addd83bf3f454c0bc00caa3dd837cf6c127d6edf7223aa2bbe3b688feea110b7f262adbfc845b757c44c8a9f8c0c5b15d8fa9ce9d20d
  languageName: node
  linkType: hard

"diff@npm:^5.2.0":
  version: 5.2.0
  resolution: "diff@npm:5.2.0"
  checksum: 12b63ca9c36c72bafa3effa77121f0581b4015df18bc16bac1f8e263597735649f1a173c26f7eba17fb4162b073fee61788abe49610e6c70a2641fe1895443fd
  languageName: node
  linkType: hard

"difflib@npm:^0.2.4":
  version: 0.2.4
  resolution: "difflib@npm:0.2.4"
  dependencies:
    heap: ">= 0.2.0"
  checksum: 4f4237b026263ce7471b77d9019b901c2f358a7da89401a80a84a8c3cdc1643a8e70b7495ccbe686cb4d95492eaf5dac119cd9ecbffe5f06bfc175fbe5c20a27
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dotenv@npm:^8.1.0":
  version: 8.6.0
  resolution: "dotenv@npm:8.6.0"
  checksum: 38e902c80b0666ab59e9310a3d24ed237029a7ce34d976796349765ac96b8d769f6df19090f1f471b77a25ca391971efde8a1ea63bb83111bd8bec8e5cc9b2cd
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ecc-jsbn@npm:~0.1.1":
  version: 0.1.2
  resolution: "ecc-jsbn@npm:0.1.2"
  dependencies:
    jsbn: ~0.1.0
    safer-buffer: ^2.1.0
  checksum: 22fef4b6203e5f31d425f5b711eb389e4c6c2723402e389af394f8411b76a488fa414d309d866e2b577ce3e8462d344205545c88a8143cc21752a5172818888a
  languageName: node
  linkType: hard

"elliptic@npm:6.5.4":
  version: 6.5.4
  resolution: "elliptic@npm:6.5.4"
  dependencies:
    bn.js: ^4.11.9
    brorand: ^1.1.0
    hash.js: ^1.0.0
    hmac-drbg: ^1.0.1
    inherits: ^2.0.4
    minimalistic-assert: ^1.0.1
    minimalistic-crypto-utils: ^1.0.1
  checksum: d56d21fd04e97869f7ffcc92e18903b9f67f2d4637a23c860492fbbff5a3155fd9ca0184ce0c865dd6eb2487d234ce9551335c021c376cd2d3b7cb749c7d10f4
  languageName: node
  linkType: hard

"elliptic@npm:^6.5.2, elliptic@npm:^6.5.4, elliptic@npm:^6.5.7":
  version: 6.6.1
  resolution: "elliptic@npm:6.6.1"
  dependencies:
    bn.js: ^4.11.9
    brorand: ^1.1.0
    hash.js: ^1.0.0
    hmac-drbg: ^1.0.1
    inherits: ^2.0.4
    minimalistic-assert: ^1.0.1
    minimalistic-crypto-utils: ^1.0.1
  checksum: 27b14a52f68bbbc0720da259f712cb73e953f6d2047958cd02fb0d0ade2e83849dc39fb4af630889c67df8817e24237428cf59c4f4c07700f755b401149a7375
  languageName: node
  linkType: hard

"emittery@npm:0.10.0":
  version: 0.10.0
  resolution: "emittery@npm:0.10.0"
  checksum: 2616a802df51e3f412b9b33f1b43161f7bc96037142cada6ecdbf35ddef1368e30d4f8e47fddc10b0753ccf91d3483b20ebca535b4b1e47526440e13150e2bc7
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encoding-down@npm:^6.3.0":
  version: 6.3.0
  resolution: "encoding-down@npm:6.3.0"
  dependencies:
    abstract-leveldown: ^6.2.1
    inherits: ^2.0.3
    level-codec: ^9.0.0
    level-errors: ^2.0.0
  checksum: 74043e6d9061a470614ff61d708c849259ab32932a428fd5ddfb0878719804f56a52f59b31cccd95fddc2e636c0fd22dc3e02481fb98d5bf1bdbbbc44ca09bdc
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enquirer@npm:^2.3.0, enquirer@npm:^2.4.1":
  version: 2.4.1
  resolution: "enquirer@npm:2.4.1"
  dependencies:
    ansi-colors: ^4.1.1
    strip-ansi: ^6.0.1
  checksum: f080f11a74209647dbf347a7c6a83c8a47ae1ebf1e75073a808bc1088eb780aa54075bfecd1bcdb3e3c724520edb8e6ee05da031529436b421b71066fcc48cb5
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"errno@npm:~0.1.1":
  version: 0.1.8
  resolution: "errno@npm:0.1.8"
  dependencies:
    prr: ~1.0.1
  bin:
    errno: cli.js
  checksum: 1271f7b9fbb3bcbec76ffde932485d1e3561856d21d847ec613a9722ee924cdd4e523a62dc71a44174d91e898fe21fdc8d5b50823f4b5e0ce8c35c8271e6ef4a
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-object-atoms@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
  checksum: 26f0ff78ab93b63394e8403c353842b2272836968de4eafe97656adfb8a7c84b9099bf0fe96ed58f4a4cddc860f6e34c77f91649a58a5daa4a9c40b902744e3c
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escodegen@npm:1.8.x":
  version: 1.8.1
  resolution: "escodegen@npm:1.8.1"
  dependencies:
    esprima: ^2.7.1
    estraverse: ^1.9.1
    esutils: ^2.0.2
    optionator: ^0.8.1
    source-map: ~0.2.0
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: ./bin/escodegen.js
    esgenerate: ./bin/esgenerate.js
  checksum: 99f5579dbc309d8f95f8051cce2f85620c073ff1d4f7b58197addee7e81aeb5281dadfbd446a0885b8fb8c0c47ce5c2cdb5f97dbfddccb5126cca5eb9af73992
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: ec97dbf5fb04b94e8f4c5a91a7f0a6dd3c55e46bfc7bbcd0e3138c3a76977570e02ed89a1810c778dcd72072ff0e9621ba1379b4babe53921d71e2e4486fda3e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint@npm:^8":
  version: 8.57.1
  resolution: "eslint@npm:8.57.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.6.1
    "@eslint/eslintrc": ^2.1.4
    "@eslint/js": 8.57.1
    "@humanwhocodes/config-array": ^0.13.0
    "@humanwhocodes/module-importer": ^1.0.1
    "@nodelib/fs.walk": ^1.2.8
    "@ungap/structured-clone": ^1.2.0
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.3.2
    doctrine: ^3.0.0
    escape-string-regexp: ^4.0.0
    eslint-scope: ^7.2.2
    eslint-visitor-keys: ^3.4.3
    espree: ^9.6.1
    esquery: ^1.4.2
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    globals: ^13.19.0
    graphemer: ^1.4.0
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    is-path-inside: ^3.0.3
    js-yaml: ^4.1.0
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
    strip-ansi: ^6.0.1
    text-table: ^0.2.0
  bin:
    eslint: bin/eslint.js
  checksum: e2489bb7f86dd2011967759a09164e65744ef7688c310bc990612fc26953f34cc391872807486b15c06833bdff737726a23e9b4cdba5de144c311377dc41d91b
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: eb8c149c7a2a77b3f33a5af80c10875c3abd65450f60b8af6db1bfcfa8f101e21c1e56a561c6dc13b848e18148d43469e7cd208506238554fb5395a9ea5a1ab9
  languageName: node
  linkType: hard

"esprima@npm:2.7.x, esprima@npm:^2.7.1":
  version: 2.7.3
  resolution: "esprima@npm:2.7.3"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 55584508dca0551885e62c3369bc4a783bd948b43e2f034f05c2a37f3ca398db99f072ab228234e9cab09af8dc8c65d6ca7de3a975f2a296b34d1a3aba7e89f1
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^1.9.1":
  version: 1.9.3
  resolution: "estraverse@npm:1.9.3"
  checksum: 78fa96317500e7783d48297dbd4c7f8735ddeb970be2981b485639ffa77578d05b8f781332622e436f2e9e533f32923c62c2e6463291e577ceeaf2776ac5e4b5
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"ethereum-bloom-filters@npm:^1.0.6":
  version: 1.2.0
  resolution: "ethereum-bloom-filters@npm:1.2.0"
  dependencies:
    "@noble/hashes": ^1.4.0
  checksum: 3a4d11495a5845483b78eca6455a915835d691df09a8c5754785c6bdfb5d18382d7e65b066a1c092493c1d87850c6a77243136996a231baec82f22c727e15258
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:0.1.3, ethereum-cryptography@npm:^0.1.3":
  version: 0.1.3
  resolution: "ethereum-cryptography@npm:0.1.3"
  dependencies:
    "@types/pbkdf2": ^3.0.0
    "@types/secp256k1": ^4.0.1
    blakejs: ^1.1.0
    browserify-aes: ^1.2.0
    bs58check: ^2.1.2
    create-hash: ^1.2.0
    create-hmac: ^1.1.7
    hash.js: ^1.1.7
    keccak: ^3.0.0
    pbkdf2: ^3.0.17
    randombytes: ^2.1.0
    safe-buffer: ^5.1.2
    scrypt-js: ^3.0.0
    secp256k1: ^4.0.1
    setimmediate: ^1.0.5
  checksum: 54bae7a4a96bd81398cdc35c91cfcc74339f71a95ed1b5b694663782e69e8e3afd21357de3b8bac9ff4877fd6f043601e200a7ad9133d94be6fd7d898ee0a449
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^1.0.3":
  version: 1.2.0
  resolution: "ethereum-cryptography@npm:1.2.0"
  dependencies:
    "@noble/hashes": 1.2.0
    "@noble/secp256k1": 1.7.1
    "@scure/bip32": 1.1.5
    "@scure/bip39": 1.1.1
  checksum: 97e8e8253cb9f5a9271bd0201c37609c451c890eb85883b9c564f14743c3d7c673287406c93bf5604307593ee298ad9a03983388b85c11ca61461b9fc1a4f2c7
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^2.0.0, ethereum-cryptography@npm:^2.1.2, ethereum-cryptography@npm:^2.1.3":
  version: 2.2.1
  resolution: "ethereum-cryptography@npm:2.2.1"
  dependencies:
    "@noble/curves": 1.4.2
    "@noble/hashes": 1.4.0
    "@scure/bip32": 1.4.0
    "@scure/bip39": 1.3.0
  checksum: 1466e4c417b315a6ac67f95088b769fafac8902b495aada3c6375d827e5a7882f9e0eea5f5451600d2250283d9198b8a3d4d996e374e07a80a324e29136f25c6
  languageName: node
  linkType: hard

"ethereum-waffle@npm:^4.0.10":
  version: 4.0.10
  resolution: "ethereum-waffle@npm:4.0.10"
  dependencies:
    "@ethereum-waffle/chai": 4.0.10
    "@ethereum-waffle/compiler": 4.0.3
    "@ethereum-waffle/mock-contract": 4.0.4
    "@ethereum-waffle/provider": 4.0.5
    solc: 0.8.15
    typechain: ^8.0.0
  peerDependencies:
    ethers: "*"
  bin:
    waffle: bin/waffle
  checksum: 680df4f5cf61f2f64b740d7724323e0872b1b1462e7ee2f1de6a1c9732155b28c4ac25c669ba557f72e1bb20204f81696a1fd543aece03654d71a9d9ebe1fc53
  languageName: node
  linkType: hard

"ethereumjs-abi@npm:0.6.8, ethereumjs-abi@npm:^0.6.8":
  version: 0.6.8
  resolution: "ethereumjs-abi@npm:0.6.8"
  dependencies:
    bn.js: ^4.11.8
    ethereumjs-util: ^6.0.0
  checksum: cede2a8ae7c7e04eeaec079c2f925601a25b2ef75cf9230e7c5da63b4ea27883b35447365a47e35c1e831af520973a2252af89022c292c18a09a4607821a366b
  languageName: node
  linkType: hard

"ethereumjs-util@npm:7.1.3":
  version: 7.1.3
  resolution: "ethereumjs-util@npm:7.1.3"
  dependencies:
    "@types/bn.js": ^5.1.0
    bn.js: ^5.1.2
    create-hash: ^1.1.2
    ethereum-cryptography: ^0.1.3
    rlp: ^2.2.4
  checksum: 6de7a32af05c7265c96163ecd15ad97327afab9deb36092ef26250616657a8c0b5df8e698328247c8193e7b87c643c967f64f0b3cff2b2937cafa870ff5fcb41
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^6.0.0, ethereumjs-util@npm:^6.2.1":
  version: 6.2.1
  resolution: "ethereumjs-util@npm:6.2.1"
  dependencies:
    "@types/bn.js": ^4.11.3
    bn.js: ^4.11.0
    create-hash: ^1.1.2
    elliptic: ^6.5.2
    ethereum-cryptography: ^0.1.3
    ethjs-util: 0.1.6
    rlp: ^2.2.3
  checksum: e3cb4a2c034a2529281fdfc21a2126fe032fdc3038863f5720352daa65ddcc50fc8c67dbedf381a882dc3802e05d979287126d7ecf781504bde1fd8218693bde
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^7.0.3, ethereumjs-util@npm:^7.1.1, ethereumjs-util@npm:^7.1.3, ethereumjs-util@npm:^7.1.4, ethereumjs-util@npm:^7.1.5":
  version: 7.1.5
  resolution: "ethereumjs-util@npm:7.1.5"
  dependencies:
    "@types/bn.js": ^5.1.0
    bn.js: ^5.1.2
    create-hash: ^1.1.2
    ethereum-cryptography: ^0.1.3
    rlp: ^2.2.4
  checksum: 27a3c79d6e06b2df34b80d478ce465b371c8458b58f5afc14d91c8564c13363ad336e6e83f57eb0bd719fde94d10ee5697ceef78b5aa932087150c5287b286d1
  languageName: node
  linkType: hard

"ethers@npm:^6.13.5":
  version: 6.13.5
  resolution: "ethers@npm:6.13.5"
  dependencies:
    "@adraffy/ens-normalize": 1.10.1
    "@noble/curves": 1.2.0
    "@noble/hashes": 1.3.2
    "@types/node": 22.7.5
    aes-js: 4.0.0-beta.5
    tslib: 2.7.0
    ws: 8.17.1
  checksum: 25700f75c3854fb5043b72748c7a4198efd15d50b4e66d575e6287aab707e855d9aa5ba342fe3d4a4c7943c84a46bcf3702b0f1da1307a82c40e1d08e86078ba
  languageName: node
  linkType: hard

"ethers@npm:^6.7.0":
  version: 6.13.4
  resolution: "ethers@npm:6.13.4"
  dependencies:
    "@adraffy/ens-normalize": 1.10.1
    "@noble/curves": 1.2.0
    "@noble/hashes": 1.3.2
    "@types/node": 22.7.5
    aes-js: 4.0.0-beta.5
    tslib: 2.7.0
    ws: 8.17.1
  checksum: a64ad0f05ed7f79bf3092cd54ac11c3ed4a0a3fe8ee00a81053b5b4a34d84728c12fa5aa9bf3e2cc5efabbf1a0a37f62cd3a1852cf780a1ab619421fa03c2713
  languageName: node
  linkType: hard

"ethjs-unit@npm:0.1.6":
  version: 0.1.6
  resolution: "ethjs-unit@npm:0.1.6"
  dependencies:
    bn.js: 4.11.6
    number-to-bn: 1.7.0
  checksum: df6b4752ff7461a59a20219f4b1684c631ea601241c39660e3f6c6bd63c950189723841c22b3c6c0ebeb3c9fc99e0e803e3c613101206132603705fcbcf4def5
  languageName: node
  linkType: hard

"ethjs-util@npm:0.1.6, ethjs-util@npm:^0.1.6":
  version: 0.1.6
  resolution: "ethjs-util@npm:0.1.6"
  dependencies:
    is-hex-prefixed: 1.0.0
    strip-hex-prefix: 1.0.0
  checksum: 1f42959e78ec6f49889c49c8a98639e06f52a15966387dd39faf2930db48663d026efb7db2702dcffe7f2a99c4a0144b7ce784efdbf733f4077aae95de76d65f
  languageName: node
  linkType: hard

"eventemitter3@npm:5.0.1, eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 543d6c858ab699303c3c32e0f0f47fc64d360bf73c3daf0ac0b5079710e340d6fe9f15487f94e66c629f5f82cd1a8678d692f3dbb6f6fcd1190e1b97fcad36f8
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: ^1.3.4
    node-gyp: latest
    safe-buffer: ^5.1.1
  checksum: ad4e1577f1a6b721c7800dcc7c733fe01f6c310732bb5bf2240245c2a5b45a38518b91d8be2c610611623160b9d1c0e91f1ce96d639f8b53e8894625cf20fa45
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"extend@npm:~3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: a50a8309ca65ea5d426382ff09f33586527882cf532931cb08ca786ea3146c0553310bda688710ff61d7668eba9f96b923fe1420cdf56a2c3eaf30fcab87b515
  languageName: node
  linkType: hard

"extendable-error@npm:^0.1.5":
  version: 0.1.7
  resolution: "extendable-error@npm:0.1.7"
  checksum: 80478be7429a1675d2085f701239796bab3230ed6f2fb1b138fbabec24bea6516b7c5ceb6e9c209efcc9c089948d93715703845653535f8e8a49655066a9255e
  languageName: node
  linkType: hard

"external-editor@npm:^3.1.0":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: ^0.7.0
    iconv-lite: ^0.4.24
    tmp: ^0.0.33
  checksum: 1c2a616a73f1b3435ce04030261bed0e22d4737e14b090bb48e58865da92529c9f2b05b893de650738d55e692d071819b45e1669259b2b354bc3154d27a698c7
  languageName: node
  linkType: hard

"extsprintf@npm:1.3.0":
  version: 1.3.0
  resolution: "extsprintf@npm:1.3.0"
  checksum: cee7a4a1e34cffeeec18559109de92c27517e5641991ec6bab849aa64e3081022903dd53084f2080d0d2530803aa5ee84f1e9de642c365452f9e67be8f958ce2
  languageName: node
  linkType: hard

"extsprintf@npm:^1.2.0":
  version: 1.4.1
  resolution: "extsprintf@npm:1.4.1"
  checksum: a2f29b241914a8d2bad64363de684821b6b1609d06ae68d5b539e4de6b28659715b5bea94a7265201603713b7027d35399d10b0548f09071c5513e65e8323d33
  languageName: node
  linkType: hard

"fast-base64-decode@npm:^1.0.0":
  version: 1.0.0
  resolution: "fast-base64-decode@npm:1.0.0"
  checksum: 4c59eb1775a7f132333f296c5082476fdcc8f58d023c42ed6d378d2e2da4c328c7a71562f271181a725dd17cdaa8f2805346cc330cdbad3b8e4b9751508bd0a3
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.0.3, fast-glob@npm:^3.2.9":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: 900e4979f4dbc3313840078419245621259f349950411ca2fa445a2f9a1a6d98c3b5e7e0660c5ccd563aa61abe133a21765c6c0dec8e57da1ba71d8000b05ec1
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6, fast-levenshtein@npm:~2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.3
  resolution: "fast-uri@npm:3.0.3"
  checksum: c52e6c86465f5c240e84a4485fb001088cc743d261a4b54b0050ce4758b1648bdbe53da1328ef9620149dca1435e3de64184f226d7c0a3656cb5837b3491e149
  languageName: node
  linkType: hard

"fast-xml-parser@npm:4.4.1":
  version: 4.4.1
  resolution: "fast-xml-parser@npm:4.4.1"
  dependencies:
    strnum: ^1.0.5
  bin:
    fxparser: src/cli/cli.js
  checksum: f440c01cd141b98789ae777503bcb6727393296094cc82924ae9f88a5b971baa4eec7e65306c7e07746534caa661fc83694ff437d9012dc84dee39dfbfaab947
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.18.0
  resolution: "fastq@npm:1.18.0"
  dependencies:
    reusify: ^1.0.4
  checksum: fb8d94318c2e5545a1913c1647b35e8b7825caaba888a98ef9887085e57f5a82104aefbb05f26c81d4e220f02b2ea6f2c999132186d8c77e6c681d91870191ba
  languageName: node
  linkType: hard

"fdir@npm:^6.4.2":
  version: 6.4.2
  resolution: "fdir@npm:6.4.2"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 517ad31c495f1c0778238eef574a7818788efaaf2ce1969ffa18c70793e2951a9763dfa2e6720b8fcef615e602a3cbb47f9b8aea9da0b02147579ab36043f22f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"find-replace@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-replace@npm:3.0.0"
  dependencies:
    array-back: ^3.0.1
  checksum: 6b04bcfd79027f5b84aa1dfe100e3295da989bdac4b4de6b277f4d063e78f5c9e92ebc8a1fec6dd3b448c924ba404ee051cc759e14a3ee3e825fa1361025df08
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"find-yarn-workspace-root@npm:^2.0.0":
  version: 2.0.0
  resolution: "find-yarn-workspace-root@npm:2.0.0"
  dependencies:
    micromatch: ^4.0.2
  checksum: fa5ca8f9d08fe7a54ce7c0a5931ff9b7e36f9ee7b9475fb13752bcea80ec6b5f180fa5102d60b376d5526ce924ea3fc6b19301262efa0a5d248dd710f3644242
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.3
    rimraf: ^3.0.2
  checksum: e7e0f59801e288b54bee5cb9681e9ee21ee28ef309f886b312c9d08415b79fc0f24ac842f84356ce80f47d6a53de62197ce0e6e148dc42d5db005992e2a756ec
  languageName: node
  linkType: hard

"flat@npm:^5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.2
  resolution: "flatted@npm:3.3.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.12.1, follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 859e2bacc7a54506f2bf9aacb10d165df78c8c1b0ceb8023f966621b233717dab56e8d08baadc3ad3b9db58af290413d585c999694b7c146aaf2616340c3d2a6
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 1989698488f725b05b26bc9afc8a08f08ec41807cd7b92ad85d96004ddf8243fd3e79486b8348c64a3011ae5cc2c9f0936af989e1f28339805d8bc178a75b451
  languageName: node
  linkType: hard

"forever-agent@npm:~0.6.1":
  version: 0.6.1
  resolution: "forever-agent@npm:0.6.1"
  checksum: 766ae6e220f5fe23676bb4c6a99387cec5b7b62ceb99e10923376e27bfea72f3c3aeec2ba5f45f3f7ba65d6616965aa7c20b15002b6860833bb6e394dea546a8
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.1
  resolution: "form-data@npm:4.0.1"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    mime-types: ^2.1.12
  checksum: ccee458cd5baf234d6b57f349fe9cc5f9a2ea8fd1af5ecda501a18fd1572a6dd3bf08a49f00568afd995b6a65af34cb8dec083cf9d582c4e621836499498dd84
  languageName: node
  linkType: hard

"form-data@npm:~2.3.2":
  version: 2.3.3
  resolution: "form-data@npm:2.3.3"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.6
    mime-types: ^2.1.12
  checksum: 10c1780fa13dbe1ff3100114c2ce1f9307f8be10b14bf16e103815356ff567b6be39d70fc4a40f8990b9660012dc24b0f5e1dde1b6426166eb23a445ba068ca3
  languageName: node
  linkType: hard

"fp-ts@npm:1.19.3":
  version: 1.19.3
  resolution: "fp-ts@npm:1.19.3"
  checksum: eb0d4766ad561e9c5c01bfdd3d0ae589af135556921c733d26cf5289aad9f400110defdd93e6ac1d71f626697bb44d9d95ed2879c53dfd868f7cac3cf5c5553c
  languageName: node
  linkType: hard

"fp-ts@npm:^1.0.0":
  version: 1.19.5
  resolution: "fp-ts@npm:1.19.5"
  checksum: 67d2d9c3855d211ca2592b1ef805f98b618157e7681791a776d9d0f7f3e52fcca2122ebf5bc215908c9099fad69756d40e37210cf46cb4075dae1b61efe69e40
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs-extra@npm:^7.0.0, fs-extra@npm:^7.0.1":
  version: 7.0.1
  resolution: "fs-extra@npm:7.0.1"
  dependencies:
    graceful-fs: ^4.1.2
    jsonfile: ^4.0.0
    universalify: ^0.1.0
  checksum: 141b9dccb23b66a66cefdd81f4cda959ff89282b1d721b98cea19ba08db3dcbe6f862f28841f3cf24bb299e0b7e6c42303908f65093cb7e201708e86ea5a8dcf
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^4.0.0
    universalify: ^0.1.0
  checksum: bf44f0e6cea59d5ce071bba4c43ca76d216f89e402dc6285c128abc0902e9b8525135aa808adad72c9d5d218e9f4bcc63962815529ff2f684ad532172a284880
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0, fs-extra@npm:^9.1.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: ^1.0.0
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: ba71ba32e0faa74ab931b7a0031d1523c66a73e225de7426e275e238e312d07313d2da2d33e34a52aa406c8763ade5712eb3ec9ba4d9edce652bcacdc29e6b20
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"fula-chain@workspace:.":
  version: 0.0.0-use.local
  resolution: "fula-chain@workspace:."
  dependencies:
    "@chainlink/contracts": ^1.3.0
    "@nomicfoundation/hardhat-chai-matchers": ^2.0.4
    "@nomicfoundation/hardhat-ethers": ^3.0.8
    "@nomicfoundation/hardhat-ignition": ^0.15.9
    "@nomicfoundation/hardhat-ignition-ethers": 0.15.0
    "@nomicfoundation/hardhat-network-helpers": ^1.0.12
    "@nomicfoundation/hardhat-toolbox": ^5.0.0
    "@nomicfoundation/hardhat-verify": ^2.0.12
    "@nomicfoundation/hardhat-web3-v4": ^1.0.0
    "@nomiclabs/hardhat-ethers": ^2.2.3
    "@nomiclabs/hardhat-waffle": ^2.0.6
    "@openzeppelin/contracts": ^5.2.0
    "@openzeppelin/contracts-upgradeable": ^5.2.0
    "@openzeppelin/hardhat-upgrades": ^3.9.0
    "@openzeppelin/upgrades-core": ^1.41.0
    "@polkadot/util-crypto": ^13.3.1
    "@typechain/ethers-v6": ^0.5.1
    "@typechain/hardhat": ^9.1.0
    "@types/chai": ^4.2.0
    "@types/eslint": ^8
    "@types/mocha": ">=9.1.0"
    "@types/node": ^22.10.2
    "@types/yargs": ^17.0.33
    chai: ^4.2.0
    eslint: ^8
    ethereum-waffle: ^4.0.10
    ethers: ^6.13.5
    hardhat: ^2.22.18
    hardhat-contract-sizer: ^2.10.0
    hardhat-gas-reporter: ^2.2.3
    solidity-coverage: ^0.8.7
    ts-node: ">=8.0.0"
    tslib: ^2.8.1
    typechain: ^8.3.2
    typescript: ">=4.5.0"
    web3: ^4.5.0
  languageName: unknown
  linkType: soft

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"functional-red-black-tree@npm:^1.0.1, functional-red-black-tree@npm:~1.0.1":
  version: 1.0.1
  resolution: "functional-red-black-tree@npm:1.0.1"
  checksum: ca6c170f37640e2d94297da8bb4bf27a1d12bea3e00e6a3e007fd7aa32e37e000f5772acf941b4e4f3cf1c95c3752033d0c509af157ad8f526e7f00723b9eb9f
  languageName: node
  linkType: hard

"ganache@npm:7.4.3":
  version: 7.4.3
  resolution: "ganache@npm:7.4.3"
  dependencies:
    "@trufflesuite/bigint-buffer": 1.1.10
    "@types/bn.js": ^5.1.0
    "@types/lru-cache": 5.1.1
    "@types/seedrandom": 3.0.1
    bufferutil: 4.0.5
    emittery: 0.10.0
    keccak: 3.0.2
    leveldown: 6.1.0
    secp256k1: 4.0.3
    utf-8-validate: 5.0.7
  dependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  bin:
    ganache: dist/node/cli.js
    ganache-cli: dist/node/cli.js
  checksum: 170dde8c2ecd88e7f02a92bf582149e6497a2fdfb05abe9ab63a5b7cf33c061d71390f9719ef3d716dad4149299f8a87c71612ab4b40d1c57c55d7a185314630
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-func-name@npm:^2.0.1, get-func-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "get-func-name@npm:2.0.2"
  checksum: 3f62f4c23647de9d46e6f76d2b3eafe58933a9b3830c60669e4180d6c601ce1b4aa310ba8366143f55e52b139f992087a9f0647274e8745621fa2af7e0acf13b
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6":
  version: 1.2.6
  resolution: "get-intrinsic@npm:1.2.6"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    dunder-proto: ^1.0.0
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    function-bind: ^1.1.2
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.0.0
  checksum: a7592a0b7f023a2e83c0121fa9449ca83780e370a5feeebe8452119474d148016e43b455049134ae7a683b9b11b93d3f65eac199a0ad452ab740d5f0c299de47
  languageName: node
  linkType: hard

"getpass@npm:^0.1.1":
  version: 0.1.7
  resolution: "getpass@npm:0.1.7"
  dependencies:
    assert-plus: ^1.0.0
  checksum: ab18d55661db264e3eac6012c2d3daeafaab7a501c035ae0ccb193c3c23e9849c6e29b6ac762b9c2adae460266f925d55a3a2a3a3c8b94be2f222df94d70c046
  languageName: node
  linkType: hard

"ghost-testrpc@npm:^0.0.2":
  version: 0.0.2
  resolution: "ghost-testrpc@npm:0.0.2"
  dependencies:
    chalk: ^2.4.2
    node-emoji: ^1.10.0
  bin:
    testrpc-sc: ./index.js
  checksum: 3f86326d32f5e96c9356381837edde7dd0f23dcb7223aa73e02816256b84703cb76ce922987054a05b65963326088e99a4aa142d4b467ddda7c28547ed915d6d
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:7.1.7":
  version: 7.1.7
  resolution: "glob@npm:7.1.7"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: b61f48973bbdcf5159997b0874a2165db572b368b931135832599875919c237fc05c12984e38fe828e69aa8a921eb0e8a4997266211c517c9cfaae8a93988bb8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^5.0.15":
  version: 5.0.15
  resolution: "glob@npm:5.0.15"
  dependencies:
    inflight: ^1.0.4
    inherits: 2
    minimatch: 2 || 3
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: f9742448303460672607e569457f1b57e486a79a985e269b69465834d2075b243378225f65dc54c09fcd4b75e4fb34442aec88f33f8c65fa4abccc8ee2dc2f5d
  languageName: node
  linkType: hard

"glob@npm:^7.0.0, glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"glob@npm:^8.1.0":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^5.0.1
    once: ^1.3.0
  checksum: 92fbea3221a7d12075f26f0227abac435de868dd0736a17170663783296d0dd8d3d532a5672b4488a439bf5d7fb85cdd07c11185d6cd39184f0385cbdfb86a47
  languageName: node
  linkType: hard

"global-modules@npm:^2.0.0":
  version: 2.0.0
  resolution: "global-modules@npm:2.0.0"
  dependencies:
    global-prefix: ^3.0.0
  checksum: d6197f25856c878c2fb5f038899f2dca7cbb2f7b7cf8999660c0104972d5cfa5c68b5a0a77fa8206bb536c3903a4615665acb9709b4d80846e1bb47eaef65430
  languageName: node
  linkType: hard

"global-prefix@npm:^3.0.0":
  version: 3.0.0
  resolution: "global-prefix@npm:3.0.0"
  dependencies:
    ini: ^1.3.5
    kind-of: ^6.0.2
    which: ^1.3.1
  checksum: 8a82fc1d6f22c45484a4e34656cc91bf021a03e03213b0035098d605bfc612d7141f1e14a21097e8a0413b4884afd5b260df0b6a25605ce9d722e11f1df2881d
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: 56066ef058f6867c04ff203b8a44c15b038346a62efbc3060052a1016be9f56f4cf0b2cd45b74b22b81e521a889fc7786c73691b0549c2f3a6e825b3d394f43c
  languageName: node
  linkType: hard

"globby@npm:^10.0.1":
  version: 10.0.2
  resolution: "globby@npm:10.0.2"
  dependencies:
    "@types/glob": ^7.1.1
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.0.3
    glob: ^7.1.3
    ignore: ^5.1.1
    merge2: ^1.2.3
    slash: ^3.0.0
  checksum: 167cd067f2cdc030db2ec43232a1e835fa06217577d545709dbf29fd21631b30ff8258705172069c855dc4d5766c3b2690834e35b936fbff01ad0329fb95a26f
  languageName: node
  linkType: hard

"globby@npm:^11.0.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.11, graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.5, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"handlebars@npm:^4.0.1":
  version: 4.7.8
  resolution: "handlebars@npm:4.7.8"
  dependencies:
    minimist: ^1.2.5
    neo-async: ^2.6.2
    source-map: ^0.6.1
    uglify-js: ^3.1.4
    wordwrap: ^1.0.0
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 00e68bb5c183fd7b8b63322e6234b5ac8fbb960d712cb3f25587d559c2951d9642df83c04a1172c918c41bcfc81bfbd7a7718bbce93b893e0135fc99edea93ff
  languageName: node
  linkType: hard

"har-schema@npm:^2.0.0":
  version: 2.0.0
  resolution: "har-schema@npm:2.0.0"
  checksum: d8946348f333fb09e2bf24cc4c67eabb47c8e1d1aa1c14184c7ffec1140a49ec8aa78aa93677ae452d71d5fc0fdeec20f0c8c1237291fc2bcb3f502a5d204f9b
  languageName: node
  linkType: hard

"har-validator@npm:~5.1.3":
  version: 5.1.5
  resolution: "har-validator@npm:5.1.5"
  dependencies:
    ajv: ^6.12.3
    har-schema: ^2.0.0
  checksum: b998a7269ca560d7f219eedc53e2c664cd87d487e428ae854a6af4573fc94f182fe9d2e3b92ab968249baec7ebaf9ead69cf975c931dc2ab282ec182ee988280
  languageName: node
  linkType: hard

"hardhat-contract-sizer@npm:^2.10.0":
  version: 2.10.0
  resolution: "hardhat-contract-sizer@npm:2.10.0"
  dependencies:
    chalk: ^4.0.0
    cli-table3: ^0.6.0
    strip-ansi: ^6.0.0
  peerDependencies:
    hardhat: ^2.0.0
  checksum: 870e7cad5d96ad7288b64da0faec7962a9a18e1eaaa02ed474e4f9285cd4b1a0fc6f66326e6a7476f7063fdf99aee57f227084519b1fb3723700a2d65fc65cfa
  languageName: node
  linkType: hard

"hardhat-gas-reporter@npm:^2.2.3":
  version: 2.2.3
  resolution: "hardhat-gas-reporter@npm:2.2.3"
  dependencies:
    "@ethersproject/abi": ^5.7.0
    "@ethersproject/bytes": ^5.7.0
    "@ethersproject/units": ^5.7.0
    "@solidity-parser/parser": ^0.19.0
    axios: ^1.6.7
    brotli-wasm: ^2.0.1
    chalk: 4.1.2
    cli-table3: ^0.6.3
    ethereum-cryptography: ^2.1.3
    glob: ^10.3.10
    jsonschema: ^1.4.1
    lodash: ^4.17.21
    markdown-table: 2.0.0
    sha1: ^1.1.1
    viem: ^2.27.0
  peerDependencies:
    hardhat: ^2.16.0
  checksum: fcbc73ad000b2fdd18eb430f2ecaa5e74e6d7a7795a5c9631999bf2f826c20702f96793af92d6b14916e76729be19086b84b5fb8327db1c54b542ec4f48dfb61
  languageName: node
  linkType: hard

"hardhat@npm:^2.22.18":
  version: 2.22.18
  resolution: "hardhat@npm:2.22.18"
  dependencies:
    "@ethersproject/abi": ^5.1.2
    "@metamask/eth-sig-util": ^4.0.0
    "@nomicfoundation/edr": ^0.7.0
    "@nomicfoundation/ethereumjs-common": 4.0.4
    "@nomicfoundation/ethereumjs-tx": 5.0.4
    "@nomicfoundation/ethereumjs-util": 9.0.4
    "@nomicfoundation/solidity-analyzer": ^0.1.0
    "@sentry/node": ^5.18.1
    "@types/bn.js": ^5.1.0
    "@types/lru-cache": ^5.1.0
    adm-zip: ^0.4.16
    aggregate-error: ^3.0.0
    ansi-escapes: ^4.3.0
    boxen: ^5.1.2
    chokidar: ^4.0.0
    ci-info: ^2.0.0
    debug: ^4.1.1
    enquirer: ^2.3.0
    env-paths: ^2.2.0
    ethereum-cryptography: ^1.0.3
    ethereumjs-abi: ^0.6.8
    find-up: ^5.0.0
    fp-ts: 1.19.3
    fs-extra: ^7.0.1
    immutable: ^4.0.0-rc.12
    io-ts: 1.10.4
    json-stream-stringify: ^3.1.4
    keccak: ^3.0.2
    lodash: ^4.17.11
    mnemonist: ^0.38.0
    mocha: ^10.0.0
    p-map: ^4.0.0
    picocolors: ^1.1.0
    raw-body: ^2.4.1
    resolve: 1.17.0
    semver: ^6.3.0
    solc: 0.8.26
    source-map-support: ^0.5.13
    stacktrace-parser: ^0.1.10
    tinyglobby: ^0.2.6
    tsort: 0.0.1
    undici: ^5.14.0
    uuid: ^8.3.2
    ws: ^7.4.6
  peerDependencies:
    ts-node: "*"
    typescript: "*"
  peerDependenciesMeta:
    ts-node:
      optional: true
    typescript:
      optional: true
  bin:
    hardhat: internal/cli/bootstrap.js
  checksum: e350e80a96846a465e1ca0c92a30a83e5a04225b8def19c9703d049f4a05ac69ff12150f93bf647e3ce3f82e2264558c6a2cec1b8e8a8494b97ffbf241f54077
  languageName: node
  linkType: hard

"has-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-flag@npm:1.0.0"
  checksum: ce3f8ae978e70f16e4bbe17d3f0f6d6c0a3dd3b62a23f97c91d0fda9ed8e305e13baf95cc5bee4463b9f25ac9f5255de113165c5fb285e01b8065b2ac079b301
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0, has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0":
  version: 3.1.0
  resolution: "hash-base@npm:3.1.0"
  dependencies:
    inherits: ^2.0.4
    readable-stream: ^3.6.0
    safe-buffer: ^5.2.0
  checksum: 26b7e97ac3de13cb23fc3145e7e3450b0530274a9562144fc2bf5c1e2983afd0e09ed7cc3b20974ba66039fad316db463da80eb452e7373e780cbee9a0d2f2dc
  languageName: node
  linkType: hard

"hash.js@npm:1.1.7, hash.js@npm:^1.0.0, hash.js@npm:^1.0.3, hash.js@npm:^1.1.7":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: ^2.0.3
    minimalistic-assert: ^1.0.1
  checksum: e350096e659c62422b85fa508e4b3669017311aa4c49b74f19f8e1bc7f3a54a584fdfd45326d4964d6011f2b2d882e38bea775a96046f2a61b7779a979629d8f
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"heap@npm:>= 0.2.0":
  version: 0.2.7
  resolution: "heap@npm:0.2.7"
  checksum: b0f3963a799e02173f994c452921a777f2b895b710119df999736bfed7477235c2860c423d9aea18a9f3b3d065cb1114d605c208cfcb8d0ac550f97ec5d28cb0
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: ^1.0.3
    minimalistic-assert: ^1.0.0
    minimalistic-crypto-utils: ^1.0.1
  checksum: bd30b6a68d7f22d63f10e1888aee497d7c2c5c0bb469e66bbdac99f143904d1dfe95f8131f95b3e86c86dd239963c9d972fcbe147e7cffa00e55d18585c43fe0
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.6.0":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: c955394bdab888a1e9bb10eb33029e0f7ce5a2ac7b3f158099dc8c486c99e73809dca609f5694b223920ca2174db33d32b12f9a2a47141dc59607c29da5a62dd
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"http-signature@npm:~1.2.0":
  version: 1.2.0
  resolution: "http-signature@npm:1.2.0"
  dependencies:
    assert-plus: ^1.0.0
    jsprim: ^1.2.2
    sshpk: ^1.7.0
  checksum: 3324598712266a9683585bb84a75dec4fd550567d5e0dd4a0fff6ff3f74348793404d3eeac4918fa0902c810eeee1a86419e4a2e92a164132dfe6b26743fb47c
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"human-id@npm:^1.0.2":
  version: 1.0.2
  resolution: "human-id@npm:1.0.2"
  checksum: 95ee57ffae849f008e2ef3fe6e437be8c999861b4256f18c3b194c8928670a8a149e0576917105d5fd77e5edbb621c5a4736fade20bb7bf130113c1ebc95cb74
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24, iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13, ieee754@npm:^1.1.4, ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.1.1, ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"immediate@npm:^3.2.3":
  version: 3.3.0
  resolution: "immediate@npm:3.3.0"
  checksum: 634b4305101e2452eba6c07d485bf3e415995e533c94b9c3ffbc37026fa1be34def6e4f2276b0dc2162a3f91628564a4bfb26280278b89d3ee54624e854d2f5f
  languageName: node
  linkType: hard

"immediate@npm:~3.2.3":
  version: 3.2.3
  resolution: "immediate@npm:3.2.3"
  checksum: 9867dc70794f3aa246a90afe8a0166607590b687e8c572839ff2342292ac2da4b1cdfd396d38f7b9e72625d817d601e73c33c2874e9c0b8e0f1d6658b3c03496
  languageName: node
  linkType: hard

"immer@npm:10.0.2":
  version: 10.0.2
  resolution: "immer@npm:10.0.2"
  checksum: 525a3b14210d02ae420c3b9f6ca14f7e9bcf625611d1356e773e7739f14c7c8de50dac442e6c7de3a6e24a782f7b792b6b8666bc0b3f00269d21a95f8f68ca84
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0-rc.12":
  version: 4.3.7
  resolution: "immutable@npm:4.3.7"
  checksum: 1c50eb053bb300796551604afff554066f041aa8e15926cf98f6d11d9736b62ad12531c06515dd96375258653878b4736f8051cd20b640f5f976d09fa640e3ec
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:^1.3.5":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"interpret@npm:^1.0.0":
  version: 1.4.0
  resolution: "interpret@npm:1.4.0"
  checksum: 2e5f51268b5941e4a17e4ef0575bc91ed0ab5f8515e3cf77486f7c14d13f3010df9c0959f37063dcc96e78d12dc6b0bb1b9e111cdfe69771f4656d2993d36155
  languageName: node
  linkType: hard

"io-ts@npm:1.10.4":
  version: 1.10.4
  resolution: "io-ts@npm:1.10.4"
  dependencies:
    fp-ts: ^1.0.0
  checksum: 619134006778f7ca42693716ade7fc1a383079e7848bbeabc67a0e4ac9139cda6b2a88a052d539ab7d554033ee2ffe4dab5cb96b958c83fee2dff73d23f03e88
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: aae9307fedfe2e5be14aebd0f48a9eeedf6b8c8f5a0b66257b965146d1e94abdc3f08e3dce3b1d908e1fa23c70039a88810ee1d753905758b9b6eebbab0bafeb
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.5":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 764c9ad8b523a9f5a32af29bdf772b08eb48c04d2ad0a7240916ac2688c983bf5f8504bf25b35e66240edeb9d9085461f9b5dae1f3d2861c6b06a65fe983de42
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-ci@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-ci@npm:2.0.0"
  dependencies:
    ci-info: ^2.0.0
  bin:
    is-ci: bin.js
  checksum: 77b869057510f3efa439bbb36e9be429d53b3f51abd4776eeea79ab3b221337fe1753d1e50058a9e2c650d38246108beffb15ccfd443929d77748d8c0cc90144
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: ^2.0.2
  checksum: 6ec5b3c42d9cbf1ac23f164b16b8a140c3cec338bf8f884c076ca89950c7cc04c33e78f02b8cae7ff4751f3247e3174b2330f1fe4de194c7210deb8b1ea316a7
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.7":
  version: 1.0.10
  resolution: "is-generator-function@npm:1.0.10"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d54644e7dbaccef15ceb1e5d91d680eb5068c9ee9f9eb0a9e04173eb5542c9b51b5ab52c5537f5703e48d5fddfd376817c1ca07a84a407b7115b769d4bdde72b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-hex-prefixed@npm:1.0.0":
  version: 1.0.0
  resolution: "is-hex-prefixed@npm:1.0.0"
  checksum: 5ac58e6e528fb029cc43140f6eeb380fad23d0041cc23154b87f7c9a1b728bcf05909974e47248fd0b7fcc11ba33cf7e58d64804883056fabd23e2b898be41de
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: cec9100678b0a9fe0248a81743041ed990c2d4c99f893d935545cfbc42876cbe86d207f3b895700c690ad2fa520e568c44afc1605044b535a7820c1d40e38daa
  languageName: node
  linkType: hard

"is-subdir@npm:^1.1.1":
  version: 1.2.0
  resolution: "is-subdir@npm:1.2.0"
  dependencies:
    better-path-resolve: 1.0.0
  checksum: 31029a383972bff4cc4f1bd1463fd04dde017e0a04ae3a6f6e08124a90c6c4656312d593101b0f38805fa3f3c8f6bc4583524bbf72c50784fa5ca0d3e5a76279
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.3":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: ^1.1.16
  checksum: ea7cfc46c282f805d19a9ab2084fd4542fed99219ee9dbfbc26284728bd713a51eac66daa74eca00ae0a43b61322920ba334793607dc39907465913e921e0892
  languageName: node
  linkType: hard

"is-typedarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 3508c6cd0a9ee2e0df2fa2e9baabcdc89e911c7bd5cf64604586697212feec525aa21050e48affb5ffc3df20f0f5d2e2cf79b08caa64e1ccc9578e251763aef7
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-url@npm:^1.2.4":
  version: 1.2.4
  resolution: "is-url@npm:1.2.4"
  checksum: 100e74b3b1feab87a43ef7653736e88d997eb7bd32e71fd3ebc413e58c1cbe56269699c776aaea84244b0567f2a7d68dfaa512a062293ed2f9fdecb394148432
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.0":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: 438b7e52656fe3b9b293b180defb4e448088e7023a523ec21a91a80b9ff8cdb3377ddb5b6e60f7c7de4fa8b63ab56e121b6705fe081b3cf1b828b0a380009ad7
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:^1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isomorphic-unfetch@npm:^3.0.0":
  version: 3.1.0
  resolution: "isomorphic-unfetch@npm:3.1.0"
  dependencies:
    node-fetch: ^2.6.1
    unfetch: ^4.2.0
  checksum: 82b92fe4ec2823a81ab0fc0d11bd94d710e6f9a940d56b3cba31896d4345ec9ffc7949f4ff31ebcae84f6b95f7ebf3474c4c7452b834eb4078ea3f2c37e459c5
  languageName: node
  linkType: hard

"isomorphic-ws@npm:^5.0.0":
  version: 5.0.0
  resolution: "isomorphic-ws@npm:5.0.0"
  peerDependencies:
    ws: "*"
  checksum: e20eb2aee09ba96247465fda40c6d22c1153394c0144fa34fe6609f341af4c8c564f60ea3ba762335a7a9c306809349f9b863c8beedf2beea09b299834ad5398
  languageName: node
  linkType: hard

"isows@npm:1.0.6":
  version: 1.0.6
  resolution: "isows@npm:1.0.6"
  peerDependencies:
    ws: "*"
  checksum: ab9e85b50bcc3d70aa5ec875aa2746c5daf9321cb376ed4e5434d3c2643c5d62b1f466d93a05cd2ad0ead5297224922748c31707cb4fbd68f5d05d0479dce99c
  languageName: node
  linkType: hard

"isstream@npm:~0.1.2":
  version: 0.1.2
  resolution: "isstream@npm:0.1.2"
  checksum: 1eb2fe63a729f7bdd8a559ab552c69055f4f48eb5c2f03724430587c6f450783c8f1cd936c1c952d0a927925180fcc892ebd5b174236cf1065d4bd5bdb37e963
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"js-cookie@npm:^2.2.1":
  version: 2.2.1
  resolution: "js-cookie@npm:2.2.1"
  checksum: 9b1fb980a1c5e624fd4b28ea4867bb30c71e04c4484bb3a42766344c533faa684de9498e443425479ec68609e96e27b60614bfe354877c449c631529b6d932f2
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0, js-sha3@npm:^0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: 75df77c1fc266973f06cce8309ce010e9e9f07ec35ab12022ed29b7f0d9c8757f5a73e1b35aa24840dced0dea7059085aa143d817aea9e188e2a80d569d9adce
  languageName: node
  linkType: hard

"js-yaml@npm:3.x, js-yaml@npm:^3.13.1, js-yaml@npm:^3.6.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsbn@npm:~0.1.0":
  version: 0.1.1
  resolution: "jsbn@npm:0.1.1"
  checksum: e5ff29c1b8d965017ef3f9c219dacd6e40ad355c664e277d31246c90545a02e6047018c16c60a00f36d561b3647215c41894f5d869ada6908a2e0ce4200c88f2
  languageName: node
  linkType: hard

"json-bigint@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-bigint@npm:1.0.0"
  dependencies:
    bignumber.js: ^9.0.0
  checksum: c67bb93ccb3c291e60eb4b62931403e378906aab113ec1c2a8dd0f9a7f065ad6fd9713d627b732abefae2e244ac9ce1721c7a3142b2979532f12b258634ce6f6
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-schema@npm:0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 66389434c3469e698da0df2e7ac5a3281bcff75e797a5c127db7c5b56270e01ae13d9afa3c03344f76e32e81678337a8c912bdbb75101c62e487dc3778461d72
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json-stream-stringify@npm:^3.1.4":
  version: 3.1.6
  resolution: "json-stream-stringify@npm:3.1.6"
  checksum: ce873e09fe18461960b7536f63e2f913a2cb242819513856ed1af58989d41846976e7177cb1fe3c835220023aa01e534d56b6d5c3290a5b23793a6f4cb93785e
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1, json-stringify-safe@npm:~5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 48ec0adad5280b8a96bb93f4563aa1667fd7a36334f79149abd42446d0989f2ddc58274b479f4819f1f00617957e6344c886c55d05a4e15ebb4ab931e4a6a8ee
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.6
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 6447d6224f0d31623eef9b51185af03ac328a7553efcee30fa423d98a9e276ca08db87d71e17f2310b0263fd3ffa6c2a90a6308367f661dc21580f9469897c9e
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"jsonschema@npm:^1.2.4":
  version: 1.4.1
  resolution: "jsonschema@npm:1.4.1"
  checksum: 1ef02a6cd9bc32241ec86bbf1300bdbc3b5f2d8df6eb795517cf7d1cd9909e7beba1e54fdf73990fd66be98a182bda9add9607296b0cb00b1348212988e424b2
  languageName: node
  linkType: hard

"jsonschema@npm:^1.4.1":
  version: 1.5.0
  resolution: "jsonschema@npm:1.5.0"
  checksum: 170b9c375967bc135f4d029fedc31f5686f2c3bb07e7472cebddbb907b5369bf75a1a50287d6af9c31f61c76fe0b7786e78044c188aaddd329b77d856475e6db
  languageName: node
  linkType: hard

"jsprim@npm:^1.2.2":
  version: 1.4.2
  resolution: "jsprim@npm:1.4.2"
  dependencies:
    assert-plus: 1.0.0
    extsprintf: 1.3.0
    json-schema: 0.4.0
    verror: 1.10.0
  checksum: 2ad1b9fdcccae8b3d580fa6ced25de930eaa1ad154db21bbf8478a4d30bbbec7925b5f5ff29b933fba9412b16a17bd484a8da4fdb3663b5e27af95dd693bab2a
  languageName: node
  linkType: hard

"keccak@npm:3.0.1":
  version: 3.0.1
  resolution: "keccak@npm:3.0.1"
  dependencies:
    node-addon-api: ^2.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
  checksum: 1de1b62fbb3e035ee186232b11f154bd5c2c12a2d910bc8ec313dab412b6f39ddc51d3a105618dd8de752875da0ead21abb0eb1d4e7d7b17771a4acbb7159390
  languageName: node
  linkType: hard

"keccak@npm:3.0.2":
  version: 3.0.2
  resolution: "keccak@npm:3.0.2"
  dependencies:
    node-addon-api: ^2.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
    readable-stream: ^3.6.0
  checksum: 39a7d6128b8ee4cb7dcd186fc7e20c6087cc39f573a0f81b147c323f688f1f7c2b34f62c4ae189fe9b81c6730b2d1228d8a399cdc1f3d8a4c8f030cdc4f20272
  languageName: node
  linkType: hard

"keccak@npm:^3.0.0, keccak@npm:^3.0.2":
  version: 3.0.4
  resolution: "keccak@npm:3.0.4"
  dependencies:
    node-addon-api: ^2.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
    readable-stream: ^3.6.0
  checksum: 2bf27b97b2f24225b1b44027de62be547f5c7326d87d249605665abd0c8c599d774671c35504c62c9b922cae02758504c6f76a73a84234d23af8a2211afaaa11
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"klaw-sync@npm:^6.0.0":
  version: 6.0.0
  resolution: "klaw-sync@npm:6.0.0"
  dependencies:
    graceful-fs: ^4.1.11
  checksum: 0da397f8961313c3ef8f79fb63af9002cde5a8fb2aeb1a37351feff0dd6006129c790400c3f5c3b4e757bedcabb13d21ec0a5eaef5a593d59515d4f2c291e475
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: df82cd1e172f957bae9c536286265a5cdbd5eeca487cb0a3b2a7b41ef959fc61f8e7c0e9aeea9c114ccf2c166b6a8dd45a46fd619c1c569d210ecd2765ad5169
  languageName: node
  linkType: hard

"level-codec@npm:^9.0.0":
  version: 9.0.2
  resolution: "level-codec@npm:9.0.2"
  dependencies:
    buffer: ^5.6.0
  checksum: 289003d51b8afcdd24c4d318606abf2bae81975e4b527d7349abfdbacc8fef26711f2f24e2d20da0e1dce0bb216a856c9433ccb9ca25fa78a96aed9f51e506ed
  languageName: node
  linkType: hard

"level-concat-iterator@npm:^3.0.0":
  version: 3.1.0
  resolution: "level-concat-iterator@npm:3.1.0"
  dependencies:
    catering: ^2.1.0
  checksum: a15bc4c5fbbb30c1efa7fad06b72feaac84d90990b356b461593c198a833336f31f6daff8f40c3908fabd14cfd8856d1c5ecae9e1cb0575037b65fa607e760e9
  languageName: node
  linkType: hard

"level-concat-iterator@npm:~2.0.0":
  version: 2.0.1
  resolution: "level-concat-iterator@npm:2.0.1"
  checksum: 562583ef1292215f8e749c402510cb61c4d6fccf4541082b3d21dfa5ecde9fcccfe52bdcb5cfff9d2384e7ce5891f44df9439a6ddb39b0ffe31015600b4a828a
  languageName: node
  linkType: hard

"level-errors@npm:^2.0.0, level-errors@npm:~2.0.0":
  version: 2.0.1
  resolution: "level-errors@npm:2.0.1"
  dependencies:
    errno: ~0.1.1
  checksum: aca5d7670e2a40609db8d7743fce289bb5202c0bc13e4a78f81f36a6642e9abc0110f48087d3d3c2c04f023d70d4ee6f2db0e20c63d29b3fda323a67bfff6526
  languageName: node
  linkType: hard

"level-iterator-stream@npm:~4.0.0":
  version: 4.0.2
  resolution: "level-iterator-stream@npm:4.0.2"
  dependencies:
    inherits: ^2.0.4
    readable-stream: ^3.4.0
    xtend: ^4.0.2
  checksum: 239e2c7e62bffb485ed696bcd3b98de7a2bc455d13be4fce175ae3544fe9cda81c2ed93d3e88b61380ae6d28cce02511862d77b86fb2ba5b5cf00471f3c1eccc
  languageName: node
  linkType: hard

"level-mem@npm:^5.0.1":
  version: 5.0.1
  resolution: "level-mem@npm:5.0.1"
  dependencies:
    level-packager: ^5.0.3
    memdown: ^5.0.0
  checksum: 37a38163b0c7cc55f64385fdff78438669f953bc08dc751739e2f1edd401472a89001a73a95cc8b81f38f989e46279797c11eb82e702690ea9a171e02bf31e84
  languageName: node
  linkType: hard

"level-packager@npm:^5.0.3":
  version: 5.1.1
  resolution: "level-packager@npm:5.1.1"
  dependencies:
    encoding-down: ^6.3.0
    levelup: ^4.3.2
  checksum: befe2aa54f2010a6ecf7ddce392c8dee225e1839205080a2704d75e560e28b01191b345494696196777b70d376e3eaae4c9e7c330cc70d3000839f5b18dd78f2
  languageName: node
  linkType: hard

"level-supports@npm:^2.0.1":
  version: 2.1.0
  resolution: "level-supports@npm:2.1.0"
  checksum: f7b16aea7ddd13326ee4fbc2c1099bcaf8a74dc95346af9ebedea4e02518c6f7a438e829b79b7890d67489b59f615a9428369a0a065021797aa7cb6b6bd84d75
  languageName: node
  linkType: hard

"level-supports@npm:~1.0.0":
  version: 1.0.1
  resolution: "level-supports@npm:1.0.1"
  dependencies:
    xtend: ^4.0.2
  checksum: 5d6bdb88cf00c3d9adcde970db06a548c72c5a94bf42c72f998b58341a105bfe2ea30d313ce1e84396b98cc9ddbc0a9bd94574955a86e929f73c986e10fc0df0
  languageName: node
  linkType: hard

"level-ws@npm:^2.0.0":
  version: 2.0.0
  resolution: "level-ws@npm:2.0.0"
  dependencies:
    inherits: ^2.0.3
    readable-stream: ^3.1.0
    xtend: ^4.0.1
  checksum: 4e5cbf090a07367373f693c98ad5b4797e7e694ea801ce5cd4103e06837ec883bdce9588ac11e0b9963ca144b96c95c6401c9e43583028ba1e4f847e81ec9ad6
  languageName: node
  linkType: hard

"leveldown@npm:6.1.0":
  version: 6.1.0
  resolution: "leveldown@npm:6.1.0"
  dependencies:
    abstract-leveldown: ^7.2.0
    napi-macros: ~2.0.0
    node-gyp: latest
    node-gyp-build: ^4.3.0
  checksum: e984b61e9fbe057cfd5c81ac0afe5d7e35d695ff130a95991e0ecb66390e4c4ff6aa3980a65b6c53edaba80527a47790bb26e3cfbd52a054957b3546d9941fe4
  languageName: node
  linkType: hard

"levelup@npm:^4.3.2":
  version: 4.4.0
  resolution: "levelup@npm:4.4.0"
  dependencies:
    deferred-leveldown: ~5.3.0
    level-errors: ~2.0.0
    level-iterator-stream: ~4.0.0
    level-supports: ~1.0.0
    xtend: ~4.0.0
  checksum: 5a09e34c78cd7c23f9f6cb73563f1ebe8121ffc5f9f5f232242529d4fbdd40e8d1ffb337d2defa0b842334e0dbd4028fbfe7a072eebfe2c4d07174f0aa4aabca
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"levn@npm:~0.3.0":
  version: 0.3.0
  resolution: "levn@npm:0.3.0"
  dependencies:
    prelude-ls: ~1.1.2
    type-check: ~0.3.2
  checksum: 0d084a524231a8246bb10fec48cdbb35282099f6954838604f3c7fc66f2e16fa66fd9cc2f3f20a541a113c4dafdf181e822c887c8a319c9195444e6c64ac395e
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: cb9227612f71b83e42de93eccf1232feeb25e705bdb19ba26c04f91e885bfd3dd5c517c4a97137658190581d3493ea3973072ca010aab7e301046d90740393d1
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 92c46f094b064e876a23c97f57f81fbffd5d760bf2d8a1c61d85db6d1e488c66b0384c943abee4f6af7debf5ad4e4282e74ff83177c9e63d8ff081a4837c3489
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: da27515dc5230eb1140ba65ff8de3613649620e8656b19a6270afe4866b7bd461d9ba2ac8a48dcc57f7adac4ee80e1de9f965d89d4d81a0ad52bb3eec2609644
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.startcase@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.startcase@npm:4.4.0"
  checksum: c03a4a784aca653845fe09d0ef67c902b6e49288dc45f542a4ab345a9c406a6dc194c774423fa313ee7b06283950301c1221dd2a1d8ecb2dac8dfbb9ed5606b5
  languageName: node
  linkType: hard

"lodash.truncate@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.truncate@npm:4.4.2"
  checksum: b463d8a382cfb5f0e71c504dcb6f807a7bd379ff1ea216669aa42c52fc28c54e404bfbd96791aa09e6df0de2c1d7b8f1b7f4b1a61f324d38fe98bc535aeee4f5
  languageName: node
  linkType: hard

"lodash@npm:4.17.21, lodash@npm:^4.17.11, lodash@npm:^4.17.14, lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"loupe@npm:^2.3.6":
  version: 2.3.7
  resolution: "loupe@npm:2.3.7"
  dependencies:
    get-func-name: ^2.0.1
  checksum: 96c058ec7167598e238bb7fb9def2f9339215e97d6685d9c1e3e4bdb33d14600e11fe7a812cf0c003dfb73ca2df374f146280b2287cae9e8d989e9d7a69a203b
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru_map@npm:^0.3.3":
  version: 0.3.3
  resolution: "lru_map@npm:0.3.3"
  checksum: ca9dd43c65ed7a4f117c548028101c5b6855e10923ea9d1f635af53ad20c5868ff428c364d454a7b57fe391b89c704982275410c3c5099cca5aeee00d76e169a
  languageName: node
  linkType: hard

"ltgt@npm:~2.2.0":
  version: 2.2.1
  resolution: "ltgt@npm:2.2.1"
  checksum: 7e3874296f7538bc8087b428ac4208008d7b76916354b34a08818ca7c83958c1df10ec427eeeaad895f6b81e41e24745b18d30f89abcc21d228b94f6961d50a2
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: b86e5e0e25f7f777b77fabd8e2cbf15737972869d852a22b7e73c17623928fccb826d8e46b9951501d3f20e51ad74ba8c59ed584f610526a48f8ccf88aaec402
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"markdown-table@npm:2.0.0":
  version: 2.0.0
  resolution: "markdown-table@npm:2.0.0"
  dependencies:
    repeat-string: ^1.0.0
  checksum: 9bb634a9300016cbb41216c1eab44c74b6b7083ac07872e296f900a29449cf0e260ece03fa10c3e9784ab94c61664d1d147da0315f95e1336e2bdcc025615c90
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.0.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"mcl-wasm@npm:^0.7.1":
  version: 0.7.9
  resolution: "mcl-wasm@npm:0.7.9"
  checksum: 6b6ed5084156b98b2db70b223e1ba2c01953970b48a2e0c4ea3eeb9296610e6b3bfb2a2cce9e92e2d7ad61778b5f5a630e705e663835e915ba188c174a0a37fa
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: ^3.0.0
    inherits: ^2.0.1
    safe-buffer: ^5.1.2
  checksum: 098494d885684bcc4f92294b18ba61b7bd353c23147fbc4688c75b45cb8590f5a95fd4584d742415dcc52487f7a1ef6ea611cfa1543b0dc4492fe026357f3f0c
  languageName: node
  linkType: hard

"memdown@npm:^5.0.0":
  version: 5.1.0
  resolution: "memdown@npm:5.1.0"
  dependencies:
    abstract-leveldown: ~6.2.1
    functional-red-black-tree: ~1.0.1
    immediate: ~3.2.3
    inherits: ~2.0.1
    ltgt: ~2.2.0
    safe-buffer: ~5.2.0
  checksum: 23e4414034e975eae1edd6864874bbe77501d41814fc27e8ead946c3379cb1cbea303d724083d08a6a269af9bf5d55073f1f767dfa7ad6e70465769f87e29794
  languageName: node
  linkType: hard

"memorystream@npm:^0.3.1":
  version: 0.3.1
  resolution: "memorystream@npm:0.3.1"
  checksum: f18b42440d24d09516d01466c06adf797df7873f0d40aa7db02e5fb9ed83074e5e65412d0720901d7069363465f82dc4f8bcb44f0cde271567a61426ce6ca2e9
  languageName: node
  linkType: hard

"merge2@npm:^1.2.3, merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"merkle-patricia-tree@npm:^4.2.2, merkle-patricia-tree@npm:^4.2.4":
  version: 4.2.4
  resolution: "merkle-patricia-tree@npm:4.2.4"
  dependencies:
    "@types/levelup": ^4.3.0
    ethereumjs-util: ^7.1.4
    level-mem: ^5.0.1
    level-ws: ^2.0.0
    readable-stream: ^3.6.0
    semaphore-async-await: ^1.5.1
  checksum: acedc7eea7bb14b97da01e8e023406ed55742f8e82bdd28d1ed821e3bd0cfed9e92f18c7cb300aee0d38f319c960026fd4d4e601f61e2a8665b73c0786d9f799
  languageName: node
  linkType: hard

"micro-ftch@npm:^0.3.1":
  version: 0.3.1
  resolution: "micro-ftch@npm:0.3.1"
  checksum: 0e496547253a36e98a83fb00c628c53c3fb540fa5aaeaf718438873785afd193244988c09d219bb1802984ff227d04938d9571ef90fe82b48bd282262586aaff
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"miller-rabin@npm:^4.0.0":
  version: 4.0.1
  resolution: "miller-rabin@npm:4.0.1"
  dependencies:
    bn.js: ^4.0.0
    brorand: ^1.0.1
  bin:
    miller-rabin: bin/miller-rabin
  checksum: 00cd1ab838ac49b03f236cc32a14d29d7d28637a53096bf5c6246a032a37749c9bd9ce7360cbf55b41b89b7d649824949ff12bc8eee29ac77c6b38eada619ece
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.19":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 6e8a0422b30039406efd4c440829ea8f988845db02a3299f372fceba56ffa94994a9c0f2fd70c17f9969eedfbd72f34b5070ead9656a34d3f71c0bd72583a0ed
  languageName: node
  linkType: hard

"minimatch@npm:2 || 3, minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1, minimatch@npm:^5.1.6":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 7564208ef81d7065a370f788d337cd80a689e981042cb9a1d0e6580b6c6a8c9279eba80010516e258835a988363f99f54a6f711a315089b8b42694f5da9d0d77
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4, minimatch@npm:^9.0.5":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.5, minimist@npm:^1.2.6, minimist@npm:^1.2.7":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 7d59a31011ab9e4d1af6562dd4c4440e425b2baf4c5edbdd2e22fb25a88629e1cdceca39953ff209da504a46021df520f18fd9a519f36efae4750ff724ddadea
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: ^7.0.4
    rimraf: ^5.0.5
  checksum: da0a53899252380475240c587e52c824f8998d9720982ba5c4693c68e89230718884a209858c156c6e08d51aad35700a3589987e540593c36f6713fe30cd7338
  languageName: node
  linkType: hard

"mkdirp@npm:0.5.x, mkdirp@npm:^0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: ^1.2.6
  bin:
    mkdirp: bin/cmd.js
  checksum: 0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"mnemonist@npm:^0.38.0":
  version: 0.38.5
  resolution: "mnemonist@npm:0.38.5"
  dependencies:
    obliterator: ^2.0.0
  checksum: 66080afc1616866beb164e230c432964d6eed467cf37ad00e9c10161b8267928124ca8f1d0ecfea86c85568acfa62d54faaf646a86968d1135189a0fdfdd6b78
  languageName: node
  linkType: hard

"mocha@npm:^10.0.0, mocha@npm:^10.2.0":
  version: 10.8.2
  resolution: "mocha@npm:10.8.2"
  dependencies:
    ansi-colors: ^4.1.3
    browser-stdout: ^1.3.1
    chokidar: ^3.5.3
    debug: ^4.3.5
    diff: ^5.2.0
    escape-string-regexp: ^4.0.0
    find-up: ^5.0.0
    glob: ^8.1.0
    he: ^1.2.0
    js-yaml: ^4.1.0
    log-symbols: ^4.1.0
    minimatch: ^5.1.6
    ms: ^2.1.3
    serialize-javascript: ^6.0.2
    strip-json-comments: ^3.1.1
    supports-color: ^8.1.1
    workerpool: ^6.5.1
    yargs: ^16.2.0
    yargs-parser: ^20.2.9
    yargs-unparser: ^2.0.0
  bin:
    _mocha: bin/_mocha
    mocha: bin/mocha.js
  checksum: 68cb519503f1e8ffd9b0651e1aef75dfe4754425186756b21e53169da44b5bcb1889e2b743711205082763d3f9a42eb8eb2c13bb1a718a08cb3a5f563bfcacdc
  languageName: node
  linkType: hard

"mri@npm:^1.2.0":
  version: 1.2.0
  resolution: "mri@npm:1.2.0"
  checksum: 83f515abbcff60150873e424894a2f65d68037e5a7fcde8a9e2b285ee9c13ac581b63cfc1e6826c4732de3aeb84902f7c1e16b7aff46cd3f897a0f757a894e85
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"napi-macros@npm:~2.0.0":
  version: 2.0.0
  resolution: "napi-macros@npm:2.0.0"
  checksum: 30384819386977c1f82034757014163fa60ab3c5a538094f778d38788bebb52534966279956f796a92ea771c7f8ae072b975df65de910d051ffbdc927f62320c
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"ndjson@npm:2.0.0":
  version: 2.0.0
  resolution: "ndjson@npm:2.0.0"
  dependencies:
    json-stringify-safe: ^5.0.1
    minimist: ^1.2.5
    readable-stream: ^3.6.0
    split2: ^3.0.0
    through2: ^4.0.0
  bin:
    ndjson: cli.js
  checksum: f847a51a2275b8a6a1bfdb24095183836b71c3085670161678c9922bc59644f04e53ced385e549a5565fdc44c28e206bd3f2199d12525028f843a86b680c4446
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"nice-try@npm:^1.0.4":
  version: 1.0.5
  resolution: "nice-try@npm:1.0.5"
  checksum: 0b4af3b5bb5d86c289f7a026303d192a7eb4417231fe47245c460baeabae7277bcd8fd9c728fb6bd62c30b3e15cd6620373e2cf33353b095d8b403d3e8a15aff
  languageName: node
  linkType: hard

"node-addon-api@npm:^2.0.0":
  version: 2.0.2
  resolution: "node-addon-api@npm:2.0.2"
  dependencies:
    node-gyp: latest
  checksum: 31fb22d674648204f8dd94167eb5aac896c841b84a9210d614bf5d97c74ef059cc6326389cf0c54d2086e35312938401d4cc82e5fcd679202503eb8ac84814f8
  languageName: node
  linkType: hard

"node-addon-api@npm:^5.0.0":
  version: 5.1.0
  resolution: "node-addon-api@npm:5.1.0"
  dependencies:
    node-gyp: latest
  checksum: 2508bd2d2981945406243a7bd31362fc7af8b70b8b4d65f869c61731800058fb818cc2fd36c8eac714ddd0e568cc85becf5e165cebbdf7b5024d5151bbc75ea1
  languageName: node
  linkType: hard

"node-emoji@npm:^1.10.0":
  version: 1.11.0
  resolution: "node-emoji@npm:1.11.0"
  dependencies:
    lodash: ^4.17.21
  checksum: e8c856c04a1645062112a72e59a98b203505ed5111ff84a3a5f40611afa229b578c7d50f1e6a7f17aa62baeea4a640d2e2f61f63afc05423aa267af10977fb2b
  languageName: node
  linkType: hard

"node-fetch@npm:^2.5.0, node-fetch@npm:^2.6.1, node-fetch@npm:^2.6.7, node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-gyp-build@npm:4.3.0":
  version: 4.3.0
  resolution: "node-gyp-build@npm:4.3.0"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 1ecab16d9f275174d516e223f60f65ebe07540347d5c04a6a7d6921060b7f2e3af4f19463d9d1dcedc452e275c2ae71354a99405e55ebd5b655bb2f38025c728
  languageName: node
  linkType: hard

"node-gyp-build@npm:4.4.0":
  version: 4.4.0
  resolution: "node-gyp-build@npm:4.4.0"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 972a059f960253d254e0b23ce10f54c8982236fc0edcab85166d0b7f87443b2ce98391c877cfb2f6eeafcf03c538c5f4dd3e0bfff03828eb48634f58f4c64343
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.2.0, node-gyp-build@npm:^4.3.0":
  version: 4.8.4
  resolution: "node-gyp-build@npm:4.8.4"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 8b81ca8ffd5fa257ad8d067896d07908a36918bc84fb04647af09d92f58310def2d2b8614d8606d129d9cd9b48890a5d2bec18abe7fcff54818f72bedd3a7d74
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.0.0
  resolution: "node-gyp@npm:11.0.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: d7d5055ccc88177f721c7cd4f8f9440c29a0eb40e7b79dba89ef882ec957975dfc1dcb8225e79ab32481a02016eb13bbc051a913ea88d482d3cbdf2131156af4
  languageName: node
  linkType: hard

"nofilter@npm:^3.1.0":
  version: 3.1.0
  resolution: "nofilter@npm:3.1.0"
  checksum: 58aa85a5b4b35cbb6e42de8a8591c5e338061edc9f3e7286f2c335e9e9b9b8fa7c335ae45daa8a1f3433164dc0b9a3d187fa96f9516e04a17a1f9ce722becc4f
  languageName: node
  linkType: hard

"nopt@npm:3.x":
  version: 3.0.6
  resolution: "nopt@npm:3.0.6"
  dependencies:
    abbrev: 1
  bin:
    nopt: ./bin/nopt.js
  checksum: 7f8579029a0d7cb3341c6b1610b31e363f708b7aaaaf3580e3ec5ae8528d1f3a79d350d8bfa331776e6c6703a5a148b72edd9b9b4c1dd55874d8e70e963d1e20
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.0.0
  resolution: "nopt@npm:8.0.0"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 2cfc65e7ee38af2e04aea98f054753b0230011c0eeca4ecf131bd7d25984cbbf6f214586e0ae5dfcc2e830bc0bffa5a7fb28ea8d0b306ffd4ae8ea2d814c1ab3
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"number-to-bn@npm:1.7.0":
  version: 1.7.0
  resolution: "number-to-bn@npm:1.7.0"
  dependencies:
    bn.js: 4.11.6
    strip-hex-prefix: 1.0.0
  checksum: 5b8c9dbe7b49dc7a069e5f0ba4e197257c89db11463478cb002fee7a34dc8868636952bd9f6310e5fdf22b266e0e6dffb5f9537c741734718107e90ae59b3de4
  languageName: node
  linkType: hard

"oauth-sign@npm:~0.9.0":
  version: 0.9.0
  resolution: "oauth-sign@npm:0.9.0"
  checksum: 8f5497a127967866a3c67094c21efd295e46013a94e6e828573c62220e9af568cc1d2d04b16865ba583e430510fa168baf821ea78f355146d8ed7e350fc44c64
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.3
  resolution: "object-inspect@npm:1.13.3"
  checksum: 8c962102117241e18ea403b84d2521f78291b774b03a29ee80a9863621d88265ffd11d0d7e435c4c2cea0dc2a2fbf8bbc92255737a05536590f2df2e8756f297
  languageName: node
  linkType: hard

"obliterator@npm:^2.0.0":
  version: 2.0.4
  resolution: "obliterator@npm:2.0.4"
  checksum: f28ad35b6d812089315f375dc3e6e5f9bebf958ebe4b10ccd471c7115cbcf595e74bdac4783ae758e5b1f47e3096427fdb37cfa7bed566b132df92ff317b9a7c
  languageName: node
  linkType: hard

"once@npm:1.x, once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"open@npm:^7.4.2":
  version: 7.4.2
  resolution: "open@npm:7.4.2"
  dependencies:
    is-docker: ^2.0.0
    is-wsl: ^2.1.1
  checksum: 3333900ec0e420d64c23b831bc3467e57031461d843c801f569b2204a1acc3cd7b3ec3c7897afc9dde86491dfa289708eb92bba164093d8bd88fb2c231843c91
  languageName: node
  linkType: hard

"optionator@npm:^0.8.1":
  version: 0.8.3
  resolution: "optionator@npm:0.8.3"
  dependencies:
    deep-is: ~0.1.3
    fast-levenshtein: ~2.0.6
    levn: ~0.3.0
    prelude-ls: ~1.1.2
    type-check: ~0.3.2
    word-wrap: ~1.2.3
  checksum: b8695ddf3d593203e25ab0900e265d860038486c943ff8b774f596a310f8ceebdb30c6832407a8198ba3ec9debe1abe1f51d4aad94843612db3b76d690c61d34
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"ordinal@npm:^1.0.3":
  version: 1.0.3
  resolution: "ordinal@npm:1.0.3"
  checksum: 6761c5b7606b6c4b0c22b4097dab4fe7ffcddacc49238eedf9c0ced877f5d4e4ad3f4fd43fefa1cc3f167cc54c7149267441b2ae85b81ccf13f45cf4b7947164
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"outdent@npm:^0.5.0":
  version: 0.5.0
  resolution: "outdent@npm:0.5.0"
  checksum: 6e6c63dd09e9890e67ef9a0b4d35df0b0b850b2059ce3f7e19e4cc1a146b26dc5d8c45df238dbf187dfffc8bd82cd07d37c697544015680bcb9f07f29a36c678
  languageName: node
  linkType: hard

"ox@npm:0.6.9":
  version: 0.6.9
  resolution: "ox@npm:0.6.9"
  dependencies:
    "@adraffy/ens-normalize": ^1.10.1
    "@noble/curves": ^1.6.0
    "@noble/hashes": ^1.5.0
    "@scure/bip32": ^1.5.0
    "@scure/bip39": ^1.4.0
    abitype: ^1.0.6
    eventemitter3: 5.0.1
  peerDependencies:
    typescript: ">=5.4.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 6f35c9710ab3edb8146f0d2a7c482517c8e1cb2adf0cfb7aba23a17209cf7171546ad017cce98dd9e0f60cee5d77ddaaa72961023e4456de093d985b5712c546
  languageName: node
  linkType: hard

"p-filter@npm:^2.1.0":
  version: 2.1.0
  resolution: "p-filter@npm:2.1.0"
  dependencies:
    p-map: ^2.0.0
  checksum: 76e552ca624ce2233448d68b19eec9de42b695208121998f7e011edce71d1079a83096ee6a2078fb2a59cfa8a5c999f046edf00ebf16a8e780022010b4693234
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^2.0.0":
  version: 2.1.0
  resolution: "p-map@npm:2.1.0"
  checksum: 9e3ad3c9f6d75a5b5661bcad78c91f3a63849189737cd75e4f1225bf9ac205194e5c44aac2ef6f09562b1facdb9bd1425584d7ac375bfaa17b3f1a142dab936d
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"package-manager-detector@npm:^0.2.0":
  version: 0.2.8
  resolution: "package-manager-detector@npm:0.2.8"
  checksum: 6007d4a0bc8746d8fe01c941c4f4e1d86c192b04aebc121228122bcb103d16d71792e08143bd1a3d08a01ddbf2b38b6b8bb3fbeb1cd58656c654973c73cd80ac
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"patch-package@npm:^6.4.7":
  version: 6.5.1
  resolution: "patch-package@npm:6.5.1"
  dependencies:
    "@yarnpkg/lockfile": ^1.1.0
    chalk: ^4.1.2
    cross-spawn: ^6.0.5
    find-yarn-workspace-root: ^2.0.0
    fs-extra: ^9.0.0
    is-ci: ^2.0.0
    klaw-sync: ^6.0.0
    minimist: ^1.2.6
    open: ^7.4.2
    rimraf: ^2.6.3
    semver: ^5.6.0
    slash: ^2.0.0
    tmp: ^0.0.33
    yaml: ^1.10.2
  bin:
    patch-package: index.js
  checksum: 8530ffa30f11136b527c6eddf6da48fa12856ee510a47edb1f9cdf8a025636adb82968f5fae778b5e04ce8c87915ebdf5911422b54add59a5a42e372a8f30eb2
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: c6d7fa376423fe35b95b2d67990060c3ee304fc815ff0a2dc1c6c3cfaff2bd0d572ee67e18f19d0ea3bbe32e8add2a05021132ac40509416459fffee35200699
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^2.0.1":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: f7ab0ad42fe3fb8c7f11d0c4f849871e28fbd8e1add65c370e422512fc5887097b9cf34d09c1747d45c942a8c1e26468d6356e2df3f740bf177ab8ca7301ebfd
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.6, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pathval@npm:^1.1.1":
  version: 1.1.1
  resolution: "pathval@npm:1.1.1"
  checksum: 090e3147716647fb7fb5b4b8c8e5b55e5d0a6086d085b6cd23f3d3c01fcf0ff56fd3cc22f2f4a033bd2e46ed55d61ed8379e123b42afe7d531a2a5fc8bb556d6
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.0.17, pbkdf2@npm:^3.0.9":
  version: 3.1.2
  resolution: "pbkdf2@npm:3.1.2"
  dependencies:
    create-hash: ^1.1.2
    create-hmac: ^1.1.4
    ripemd160: ^2.0.1
    safe-buffer: ^5.0.1
    sha.js: ^2.4.8
  checksum: 2c950a100b1da72123449208e231afc188d980177d021d7121e96a2de7f2abbc96ead2b87d03d8fe5c318face097f203270d7e27908af9f471c165a4e8e69c92
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 534e641aa8f7cba160f0afec0599b6cecefbb516a2e837b512be0adbe6c1da5550e89c78059c7fabc5c9ffdf6627edabe23eb7c518c4500067a898fa65c2b550
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.0":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: a7a5188c954f82c6585720e9143297ccd0e35ad8072231608086ca950bee672d51b0ef676254af0788205e59bd4e4deb4e7708769226bed725bf13370a7d1464
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 9c4e34278cb09987685fa5ef81499c82546c033713518f6441778fbec623fc708777fe8ac633097c72d88470d5963094076c7305cafc7ad340aae27cfacd856b
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.0.0
  resolution: "possible-typed-array-names@npm:1.0.0"
  checksum: b32d403ece71e042385cc7856385cecf1cd8e144fa74d2f1de40d1e16035dba097bc189715925e79b67bdd1472796ff168d3a90d296356c9c94d272d5b95f3ae
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prelude-ls@npm:~1.1.2":
  version: 1.1.2
  resolution: "prelude-ls@npm:1.1.2"
  checksum: c4867c87488e4a0c233e158e4d0d5565b609b105d75e4c05dc760840475f06b731332eb93cc8c9cecb840aa8ec323ca3c9a56ad7820ad2e63f0261dadcb154e4
  languageName: node
  linkType: hard

"prettier@npm:^2.3.1, prettier@npm:^2.7.1":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: b49e409431bf129dd89238d64299ba80717b57ff5a6d1c1a8b1a28b590d998a34e083fa13573bc732bb8d2305becb4c9a4407f8486c81fa7d55100eb08263cf8
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prompts@npm:^2.4.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: ^3.0.3
    sisteransi: ^1.0.5
  checksum: d8fd1fe63820be2412c13bfc5d0a01909acc1f0367e32396962e737cb2fc52d004f3302475d5ce7d18a1e8a79985f93ff04ee03007d091029c3f9104bffc007d
  languageName: node
  linkType: hard

"proper-lockfile@npm:^4.1.1":
  version: 4.1.2
  resolution: "proper-lockfile@npm:4.1.2"
  dependencies:
    graceful-fs: ^4.2.4
    retry: ^0.12.0
    signal-exit: ^3.0.2
  checksum: 00078ee6a61c216a56a6140c7d2a98c6c733b3678503002dc073ab8beca5d50ca271de4c85fca13b9b8ee2ff546c36674d1850509b84a04a5d0363bcb8638939
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: ed7fcc2ba0a33404958e34d95d18638249a68c430e30fcb6c478497d72739ba64ce9810a24f53a7d921d0c065e5b78e3822759800698167256b04659366ca4d4
  languageName: node
  linkType: hard

"prr@npm:~1.0.1":
  version: 1.0.1
  resolution: "prr@npm:1.0.1"
  checksum: 3bca2db0479fd38f8c4c9439139b0c42dcaadcc2fbb7bb8e0e6afaa1383457f1d19aea9e5f961d5b080f1cfc05bfa1fe9e45c97a1d3fd6d421950a73d3108381
  languageName: node
  linkType: hard

"psl@npm:^1.1.28":
  version: 1.15.0
  resolution: "psl@npm:1.15.0"
  dependencies:
    punycode: ^2.3.1
  checksum: 6f777d82eecfe1c2406dadbc15e77467b186fec13202ec887a45d0209a2c6fca530af94a462a477c3c4a767ad892ec9ede7c482d98f61f653dd838b50e89dc15
  languageName: node
  linkType: hard

"punycode@npm:^1.4.1":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: fa6e698cb53db45e4628559e557ddaf554103d2a96a1d62892c8f4032cd3bc8871796cae9eabc1bc700e2b6677611521ce5bb1d9a27700086039965d0cf34518
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"qs@npm:^6.12.3":
  version: 6.13.1
  resolution: "qs@npm:6.13.1"
  dependencies:
    side-channel: ^1.0.6
  checksum: 86c5059146955fab76624e95771031541328c171b1d63d48a7ac3b1fdffe262faf8bc5fcadc1684e6f3da3ec87a8dedc8c0009792aceb20c5e94dc34cf468bb9
  languageName: node
  linkType: hard

"qs@npm:~6.5.2":
  version: 6.5.3
  resolution: "qs@npm:6.5.3"
  checksum: 6f20bf08cabd90c458e50855559539a28d00b2f2e7dddcb66082b16a43188418cb3cb77cbd09268bcef6022935650f0534357b8af9eeb29bf0f27ccb17655692
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2, queue-microtask@npm:^1.2.3":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"randombytes@npm:^2.0.1, randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"raw-body@npm:^2.4.1":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: ba1583c8d8a48e8fbb7a873fdbb2df66ea4ff83775421bfe21ee120140949ab048200668c47d9ae3880012f6e217052690628cf679ddfbd82c9fc9358d574676
  languageName: node
  linkType: hard

"read-yaml-file@npm:^1.1.0":
  version: 1.1.0
  resolution: "read-yaml-file@npm:1.1.0"
  dependencies:
    graceful-fs: ^4.1.5
    js-yaml: ^3.6.1
    pify: ^4.0.1
    strip-bom: ^3.0.0
  checksum: 41ee5f075507ef0403328dd54e225a61c3149f915675ce7fd0fd791ddcce2e6c30a9fe0f76ffa7a465c1c157b9b4ad8ded1dcf47dc3b396103eeb013490bbc2e
  languageName: node
  linkType: hard

"readable-stream@npm:3, readable-stream@npm:^3.0.0, readable-stream@npm:^3.1.0, readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.0.2
  resolution: "readdirp@npm:4.0.2"
  checksum: 309376e717f94fb7eb61bec21e2603243a9e2420cd2e9bf94ddf026aefea0d7377ed1a62f016d33265682e44908049a55c3cfc2307450a1421654ea008489b39
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"rechoir@npm:^0.6.2":
  version: 0.6.2
  resolution: "rechoir@npm:0.6.2"
  dependencies:
    resolve: ^1.1.6
  checksum: fe76bf9c21875ac16e235defedd7cbd34f333c02a92546142b7911a0f7c7059d2e16f441fe6fb9ae203f459c05a31b2bcf26202896d89e390eda7514d5d2702b
  languageName: node
  linkType: hard

"recursive-readdir@npm:^2.2.2":
  version: 2.2.3
  resolution: "recursive-readdir@npm:2.2.3"
  dependencies:
    minimatch: ^3.0.5
  checksum: 88ec96e276237290607edc0872b4f9842837b95cfde0cdbb1e00ba9623dfdf3514d44cdd14496ab60a0c2dd180a6ef8a3f1c34599e6cf2273afac9b72a6fb2b5
  languageName: node
  linkType: hard

"reduce-flatten@npm:^2.0.0":
  version: 2.0.0
  resolution: "reduce-flatten@npm:2.0.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 9f57c93277b5585d3c83b0cf76be47b473ae8c6d9142a46ce8b0291a04bb2cf902059f0f8445dcabb3fb7378e5fe4bb4ea1e008876343d42e46d3b484534ce38
  languageName: node
  linkType: hard

"repeat-string@npm:^1.0.0":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 1b809fc6db97decdc68f5b12c4d1a671c8e3f65ec4a40c238bc5200e44e85bcc52a54f78268ab9c29fcf5fe4f1343e805420056d1f30fa9a9ee4c2d93e3cc6c0
  languageName: node
  linkType: hard

"request@npm:^2.85.0":
  version: 2.88.2
  resolution: "request@npm:2.88.2"
  dependencies:
    aws-sign2: ~0.7.0
    aws4: ^1.8.0
    caseless: ~0.12.0
    combined-stream: ~1.0.6
    extend: ~3.0.2
    forever-agent: ~0.6.1
    form-data: ~2.3.2
    har-validator: ~5.1.3
    http-signature: ~1.2.0
    is-typedarray: ~1.0.0
    isstream: ~0.1.2
    json-stringify-safe: ~5.0.1
    mime-types: ~2.1.19
    oauth-sign: ~0.9.0
    performance-now: ^2.1.0
    qs: ~6.5.2
    safe-buffer: ^5.1.2
    tough-cookie: ~2.5.0
    tunnel-agent: ^0.6.0
    uuid: ^3.3.2
  checksum: 4e112c087f6eabe7327869da2417e9d28fcd0910419edd2eb17b6acfc4bfa1dad61954525949c228705805882d8a98a86a0ea12d7f739c01ee92af7062996983
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve@npm:1.1.x":
  version: 1.1.7
  resolution: "resolve@npm:1.1.7"
  checksum: afd20873fbde7641c9125efe3f940c2a99f6b1f90f1b7b743e744bdaac1cb105b2e4e0317bcc052ed7e31d57afa86b394a4dc9a1b33a297977be134fdf0250ab
  languageName: node
  linkType: hard

"resolve@npm:1.17.0":
  version: 1.17.0
  resolution: "resolve@npm:1.17.0"
  dependencies:
    path-parse: ^1.0.6
  checksum: 9ceaf83b3429f2d7ff5d0281b8d8f18a1f05b6ca86efea7633e76b8f76547f33800799dfdd24434942dec4fbd9e651ed3aef577d9a6b5ec87ad89c1060e24759
  languageName: node
  linkType: hard

"resolve@npm:^1.1.6":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ab7a32ff4046fcd7c6fdd525b24a7527847d03c3650c733b909b01b757f92eb23510afa9cc3e9bf3f26a3e073b48c88c706dfd4c1d2fb4a16a96b73b6328ddcf
  languageName: node
  linkType: hard

"resolve@patch:resolve@1.1.x#~builtin<compat/resolve>":
  version: 1.1.7
  resolution: "resolve@patch:resolve@npm%3A1.1.7#~builtin<compat/resolve>::version=1.1.7&hash=3bafbf"
  checksum: e9dbca78600ae56835c43a09f1276876c883e4b4bbd43e2683fa140671519d2bdebeb1c1576ca87c8c508ae2987b3ec481645ac5d3054b0f23254cfc1ce49942
  languageName: node
  linkType: hard

"resolve@patch:resolve@1.17.0#~builtin<compat/resolve>":
  version: 1.17.0
  resolution: "resolve@patch:resolve@npm%3A1.17.0#~builtin<compat/resolve>::version=1.17.0&hash=c3c19d"
  dependencies:
    path-parse: ^1.0.6
  checksum: 6fd799f282ddf078c4bc20ce863e3af01fa8cb218f0658d9162c57161a2dbafe092b13015b9a4c58d0e1e801cf7aa7a4f13115fea9db98c3f9a0c43e429bad6f
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.6#~builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#~builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8aac1e4e4628bd00bf4b94b23de137dd3fe44097a8d528fd66db74484be929936e20c696e1a3edf4488f37e14180b73df6f600992baea3e089e8674291f16c9d
  languageName: node
  linkType: hard

"retry@npm:0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 47c4d5be674f7c13eee4cfe927345023972197dbbdfba5d3af7e461d13b44de1bfd663bfc80d2f601f8ef3fc8164c16dd99655a221921954a65d044a2fc1233b
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rimraf@npm:^2.6.3":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: ./bin.js
  checksum: cdc7f6eacb17927f2a075117a823e1c5951792c6498ebcce81ca8203454a811d4cf8900314154d3259bb8f0b42ab17f67396a8694a54cae3283326e57ad250cd
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 50e27388dd2b3fa6677385fc1e2966e9157c89c86853b96d02e6915663a96b7ff4d590e14f6f70e90f9b554093aa5dbc05ac3012876be558c06a65437337bc05
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: ^3.0.0
    inherits: ^2.0.1
  checksum: 006accc40578ee2beae382757c4ce2908a826b27e2b079efdcd2959ee544ddf210b7b5d7d5e80467807604244e7388427330f5c6d4cd61e6edaddc5773ccc393
  languageName: node
  linkType: hard

"rlp@npm:2.2.6":
  version: 2.2.6
  resolution: "rlp@npm:2.2.6"
  dependencies:
    bn.js: ^4.11.1
  bin:
    rlp: bin/rlp
  checksum: 2601225df0fe7aa3b497b33a12fd9fbaf8fb1d2989ecc5c091918ed93ee77d1c3fab20ddd3891a9ca66a8ba66d993e6079be6fb31f450fcf38ba30873102ca46
  languageName: node
  linkType: hard

"rlp@npm:^2.2.3, rlp@npm:^2.2.4":
  version: 2.2.7
  resolution: "rlp@npm:2.2.7"
  dependencies:
    bn.js: ^5.2.0
  bin:
    rlp: bin/rlp
  checksum: 3db4dfe5c793f40ac7e0be689a1f75d05e6f2ca0c66189aeb62adab8c436b857ab4420a419251ee60370d41d957a55698fc5e23ab1e1b41715f33217bc4bb558
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rustbn.js@npm:~0.2.0":
  version: 0.2.0
  resolution: "rustbn.js@npm:0.2.0"
  checksum: 2148e7ba34e70682907ee29df4784639e6eb025481b2c91249403b7ec57181980161868d9aa24822a5075dd1bb5a180dfedc77309e5f0d27b6301f9b563af99a
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.0, safe-buffer@npm:^5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.0.2, safer-buffer@npm:^2.1.0, safer-buffer@npm:~2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sc-istanbul@npm:^0.4.5":
  version: 0.4.6
  resolution: "sc-istanbul@npm:0.4.6"
  dependencies:
    abbrev: 1.0.x
    async: 1.x
    escodegen: 1.8.x
    esprima: 2.7.x
    glob: ^5.0.15
    handlebars: ^4.0.1
    js-yaml: 3.x
    mkdirp: 0.5.x
    nopt: 3.x
    once: 1.x
    resolve: 1.1.x
    supports-color: ^3.1.0
    which: ^1.1.1
    wordwrap: ^1.0.0
  bin:
    istanbul: lib/cli.js
  checksum: 256472ebd35787985be7fc924f817f3e0fcf0ed17655250555bf24f76d44af18fd1b25a91c33458e17a4c57b80375bea22d46e2a982880ffbde1b1a94dfeed19
  languageName: node
  linkType: hard

"scrypt-js@npm:^3.0.0":
  version: 3.0.1
  resolution: "scrypt-js@npm:3.0.1"
  checksum: b7c7d1a68d6ca946f2fbb0778e0c4ec63c65501b54023b2af7d7e9f48fdb6c6580d6f7675cd53bda5944c5ebc057560d5a6365079752546865defb3b79dea454
  languageName: node
  linkType: hard

"secp256k1@npm:4.0.3":
  version: 4.0.3
  resolution: "secp256k1@npm:4.0.3"
  dependencies:
    elliptic: ^6.5.4
    node-addon-api: ^2.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
  checksum: 21e219adc0024fbd75021001358780a3cc6ac21273c3fcaef46943af73969729709b03f1df7c012a0baab0830fb9a06ccc6b42f8d50050c665cb98078eab477b
  languageName: node
  linkType: hard

"secp256k1@npm:^4.0.1":
  version: 4.0.4
  resolution: "secp256k1@npm:4.0.4"
  dependencies:
    elliptic: ^6.5.7
    node-addon-api: ^5.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
  checksum: 9314ddcd27506c5f8d9b21a2c131c62464762f597b82fe48ba89b50149ec95cd566d6ad2d4a922553dd0a8b4b14c1ccd83283f487229a941b6c7c02361ef5177
  languageName: node
  linkType: hard

"seedrandom@npm:3.0.5":
  version: 3.0.5
  resolution: "seedrandom@npm:3.0.5"
  checksum: 728b56bc3bc1b9ddeabd381e449b51cb31bdc0aa86e27fcd0190cea8c44613d5bcb2f6bb63ed79f78180cbe791c20b8ec31a9627f7b7fc7f476fd2bdb7e2da9f
  languageName: node
  linkType: hard

"semaphore-async-await@npm:^1.5.1":
  version: 1.5.1
  resolution: "semaphore-async-await@npm:1.5.1"
  checksum: 2dedf7c59ba5f2da860fed95a81017189de6257cbe06c9de0ff2e610a3ae427e9bde1ab7685a62b03ebc28982dee437110492215d75fd6dc8257ce7a38e66b74
  languageName: node
  linkType: hard

"semver@npm:^5.5.0, semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^6.3.0":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.5.3, semver@npm:^7.6.3":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: ^2.1.0
  checksum: c4839c6206c1d143c0f80763997a361310305751171dd95e4b57efee69b8f6edd8960a0b7fbfc45042aadff98b206d55428aee0dc276efe54f100899c7fa8ab7
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: c9a6f2c5b51a2dabdc0247db9c46460152ffc62ee139f3157440bd48e7c59425093f42719ac1d7931f054f153e2d26cf37dfeb8da17a794a58198a2705e527fd
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.8":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  bin:
    sha.js: ./bin.js
  checksum: ebd3f59d4b799000699097dadb831c8e3da3eb579144fd7eb7a19484cbcbb7aca3c68ba2bb362242eb09e33217de3b4ea56e4678184c334323eca24a58e3ad07
  languageName: node
  linkType: hard

"sha1@npm:^1.1.1":
  version: 1.1.1
  resolution: "sha1@npm:1.1.1"
  dependencies:
    charenc: ">= 0.0.1"
    crypt: ">= 0.0.1"
  checksum: da9f47e949988e2f595ef19733fd1dc736866ef6de4e421a55c13b444c03ae532e528b7350ae6ea55d9fb053be61d4648ec2cd5250d46cfdbdf4f6b4e763713d
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: ^1.0.0
  checksum: 9eed1750301e622961ba5d588af2212505e96770ec376a37ab678f965795e995ade7ed44910f5d3d3cb5e10165a1847f52d3348c64e146b8be922f7707958908
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 404c5a752cd40f94591dfd9346da40a735a05139dac890ffc229afba610854d8799aaa52f87f7e0c94c5007f2c6af55bdcaeb584b56691926c5eaf41dc8f1372
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shelljs@npm:^0.8.3":
  version: 0.8.5
  resolution: "shelljs@npm:0.8.5"
  dependencies:
    glob: ^7.0.0
    interpret: ^1.0.0
    rechoir: ^0.6.2
  bin:
    shjs: bin/shjs
  checksum: 7babc46f732a98f4c054ec1f048b55b9149b98aa2da32f6cf9844c434b43c6251efebd6eec120937bd0999e13811ebd45efe17410edb3ca938f82f9381302748
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slash@npm:^2.0.0":
  version: 2.0.0
  resolution: "slash@npm:2.0.0"
  checksum: 512d4350735375bd11647233cb0e2f93beca6f53441015eea241fe784d8068281c3987fbaa93e7ef1c38df68d9c60013045c92837423c69115297d6169aa85e6
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"solc@npm:0.8.15":
  version: 0.8.15
  resolution: "solc@npm:0.8.15"
  dependencies:
    command-exists: ^1.2.8
    commander: ^8.1.0
    follow-redirects: ^1.12.1
    js-sha3: 0.8.0
    memorystream: ^0.3.1
    semver: ^5.5.0
    tmp: 0.0.33
  bin:
    solcjs: solc.js
  checksum: a11de198bc5d481485a4a4803fb08a81a56dd9ffa7cdc62f8d6d5fc669f72e7cb4b22789004d54481353463421f6e6e3d1dffe7365b6d0ed5f37baee303266db
  languageName: node
  linkType: hard

"solc@npm:0.8.26":
  version: 0.8.26
  resolution: "solc@npm:0.8.26"
  dependencies:
    command-exists: ^1.2.8
    commander: ^8.1.0
    follow-redirects: ^1.12.1
    js-sha3: 0.8.0
    memorystream: ^0.3.1
    semver: ^5.5.0
    tmp: 0.0.33
  bin:
    solcjs: solc.js
  checksum: e3eaeac76e60676377b357af8f3919d4c8c6a74b74112b49279fe8c74a3dfa1de8afe4788689fc307453bde336edc8572988d2cf9e909f84d870420eb640400c
  languageName: node
  linkType: hard

"solidity-ast@npm:^0.4.51":
  version: 0.4.59
  resolution: "solidity-ast@npm:0.4.59"
  checksum: 348657bb98e027c0969d44c3bbcfb3ac4a3ea32db37ce582e291b544fb5361be5bbebf828c562bd6ddaa1ce89d3e241e3b528dbfbadcce0dbc51a655f5088d26
  languageName: node
  linkType: hard

"solidity-coverage@npm:^0.8.7":
  version: 0.8.14
  resolution: "solidity-coverage@npm:0.8.14"
  dependencies:
    "@ethersproject/abi": ^5.0.9
    "@solidity-parser/parser": ^0.19.0
    chalk: ^2.4.2
    death: ^1.1.0
    difflib: ^0.2.4
    fs-extra: ^8.1.0
    ghost-testrpc: ^0.0.2
    global-modules: ^2.0.0
    globby: ^10.0.1
    jsonschema: ^1.2.4
    lodash: ^4.17.21
    mocha: ^10.2.0
    node-emoji: ^1.10.0
    pify: ^4.0.1
    recursive-readdir: ^2.2.2
    sc-istanbul: ^0.4.5
    semver: ^7.3.4
    shelljs: ^0.8.3
    web3-utils: ^1.3.6
  peerDependencies:
    hardhat: ^2.11.0
  bin:
    solidity-coverage: plugins/bin.js
  checksum: da18ec6774dad50757dae48a84d174526c34bb6a0906c776748ba51d379d7af929fa1d73a9ded8b8ec35739366e92fc2a4f79eb0114e4e0f15862ecf9a223871
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.13":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"source-map@npm:~0.2.0":
  version: 0.2.0
  resolution: "source-map@npm:0.2.0"
  dependencies:
    amdefine: ">=0.0.4"
  checksum: 95fe800c3a93f8c0b9516c033bfc75f2678e27d2e6c0b23ae222f5ddc4afa0a39bd0be15d1c0a1e766d388f3761cc854a053a4330f49242e6045e1a4f9dc0e26
  languageName: node
  linkType: hard

"spawndamnit@npm:^3.0.1":
  version: 3.0.1
  resolution: "spawndamnit@npm:3.0.1"
  dependencies:
    cross-spawn: ^7.0.5
    signal-exit: ^4.0.1
  checksum: 47d88a7f1e5691e13e435eddc3d34123c2f7746e2853e91bfac5ea7c6e3bb4b1d1995223b25f7a8745871510d92f63ecd3c9fa02aa2896ac0c79fb618eb08bbe
  languageName: node
  linkType: hard

"split2@npm:^3.0.0":
  version: 3.2.2
  resolution: "split2@npm:3.2.2"
  dependencies:
    readable-stream: ^3.0.0
  checksum: 8127ddbedd0faf31f232c0e9192fede469913aa8982aa380752e0463b2e31c2359ef6962eb2d24c125bac59eeec76873678d723b1c7ff696216a1cd071e3994a
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"sshpk@npm:^1.7.0":
  version: 1.18.0
  resolution: "sshpk@npm:1.18.0"
  dependencies:
    asn1: ~0.2.3
    assert-plus: ^1.0.0
    bcrypt-pbkdf: ^1.0.0
    dashdash: ^1.12.0
    ecc-jsbn: ~0.1.1
    getpass: ^0.1.1
    jsbn: ~0.1.0
    safer-buffer: ^2.0.2
    tweetnacl: ~0.14.0
  bin:
    sshpk-conv: bin/sshpk-conv
    sshpk-sign: bin/sshpk-sign
    sshpk-verify: bin/sshpk-verify
  checksum: 01d43374eee3a7e37b3b82fdbecd5518cbb2e47ccbed27d2ae30f9753f22bd6ffad31225cb8ef013bc3fb7785e686cea619203ee1439a228f965558c367c3cfa
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.10
  resolution: "stacktrace-parser@npm:0.1.10"
  dependencies:
    type-fest: ^0.7.1
  checksum: f4fbddfc09121d91e587b60de4beb4941108e967d71ad3a171812dc839b010ca374d064ad0a296295fed13acd103609d99a4224a25b4e67de13cae131f1901ee
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"string-format@npm:^2.0.0":
  version: 2.0.0
  resolution: "string-format@npm:2.0.0"
  checksum: dada2ef95f6d36c66562c673d95315f80457fa7dce2f3609a2e75d1190b98c88319028cf0a5b6c043d01c18d581b2641579f79480584ba030d6ac6fceb30bc55
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.0.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.2, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-hex-prefix@npm:1.0.0":
  version: 1.0.0
  resolution: "strip-hex-prefix@npm:1.0.0"
  dependencies:
    is-hex-prefixed: 1.0.0
  checksum: 4cafe7caee1d281d3694d14920fd5d3c11adf09371cef7e2ccedd5b83efd9e9bd2219b5d6ce6e809df6e0f437dc9d30db1192116580875698aad164a6d6b285b
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strnum@npm:^1.0.5":
  version: 1.0.5
  resolution: "strnum@npm:1.0.5"
  checksum: 651b2031db5da1bf4a77fdd2f116a8ac8055157c5420f5569f64879133825915ad461513e7202a16d7fec63c54fd822410d0962f8ca12385c4334891b9ae6dd2
  languageName: node
  linkType: hard

"supports-color@npm:^3.1.0":
  version: 3.2.3
  resolution: "supports-color@npm:3.2.3"
  dependencies:
    has-flag: ^1.0.0
  checksum: 56afc05fa87d00100d90148c4d0a6e20a0af0d56dca5c54d4d40b2553ee737dab0ca4e8b53c4471afc035227b5b44dfa4824747a7f01ad733173536f7da6fbbb
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"table-layout@npm:^1.0.2":
  version: 1.0.2
  resolution: "table-layout@npm:1.0.2"
  dependencies:
    array-back: ^4.0.1
    deep-extend: ~0.6.0
    typical: ^5.2.0
    wordwrapjs: ^4.0.0
  checksum: 8f41b5671f101a5195747ec1727b1d35ea2cd5bf85addda11cc2f4b36892db9696ce3c2c7334b5b8a122505b34d19135fede50e25678df71b0439e0704fd953f
  languageName: node
  linkType: hard

"table@npm:^6.8.0":
  version: 6.9.0
  resolution: "table@npm:6.9.0"
  dependencies:
    ajv: ^8.0.1
    lodash.truncate: ^4.4.2
    slice-ansi: ^4.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
  checksum: f54a7d1c11cda8c676e1e9aff5e723646905ed4579cca14b3ce12d2b12eac3e18f5dbe2549fe0b79697164858e18961145db4dd0660bbeb0fb4032af0aaf32b4
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"term-size@npm:^2.1.0":
  version: 2.2.1
  resolution: "term-size@npm:2.2.1"
  checksum: 1ed981335483babc1e8206f843e06bd2bf89b85f0bf5a9a9d928033a0fcacdba183c03ba7d91814643015543ba002f1339f7112402a21da8f24b6c56b062a5a9
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"through2@npm:^4.0.0":
  version: 4.0.2
  resolution: "through2@npm:4.0.2"
  dependencies:
    readable-stream: 3
  checksum: ac7430bd54ccb7920fd094b1c7ff3e1ad6edd94202e5528331253e5fde0cc56ceaa690e8df9895de2e073148c52dfbe6c4db74cacae812477a35660090960cc0
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.6":
  version: 0.2.10
  resolution: "tinyglobby@npm:0.2.10"
  dependencies:
    fdir: ^6.4.2
    picomatch: ^4.0.2
  checksum: 7e2ffe262ebc149036bdef37c56b32d02d52cf09efa7d43dbdab2ea3c12844a4da881058835ce4c74d1891190e5ad5ec5133560a11ec8314849b68ad0d99d3f4
  languageName: node
  linkType: hard

"tmp@npm:0.0.33, tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: ~1.0.2
  checksum: 902d7aceb74453ea02abbf58c203f4a8fc1cead89b60b31e354f74ed5b3fb09ea817f94fb310f884a5d16987dd9fa5a735412a7c2dd088dd3d415aa819ae3a28
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"tough-cookie@npm:~2.5.0":
  version: 2.5.0
  resolution: "tough-cookie@npm:2.5.0"
  dependencies:
    psl: ^1.1.28
    punycode: ^2.1.1
  checksum: 16a8cd090224dd176eee23837cbe7573ca0fa297d7e468ab5e1c02d49a4e9a97bb05fef11320605eac516f91d54c57838a25864e8680e27b069a5231d8264977
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"ts-command-line-args@npm:^2.2.0":
  version: 2.5.1
  resolution: "ts-command-line-args@npm:2.5.1"
  dependencies:
    chalk: ^4.1.0
    command-line-args: ^5.1.1
    command-line-usage: ^6.1.0
    string-format: ^2.0.0
  bin:
    write-markdown: dist/write-markdown.js
  checksum: 7c0a7582e94f1d2160e3dd379851ec4f1758bc673ccd71bae07f839f83051b6b83e0ae14325c2d04ea728e5bde7b7eacfd2ab060b8fd4b8ab29e0bbf77f6c51e
  languageName: node
  linkType: hard

"ts-essentials@npm:^7.0.1":
  version: 7.0.3
  resolution: "ts-essentials@npm:7.0.3"
  peerDependencies:
    typescript: ">=3.7.0"
  checksum: 74d75868acf7f8b95e447d8b3b7442ca21738c6894e576df9917a352423fde5eb43c5651da5f78997da6061458160ae1f6b279150b42f47ccc58b73e55acaa2f
  languageName: node
  linkType: hard

"ts-node@npm:>=8.0.0":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": ^0.8.0
    "@tsconfig/node10": ^1.0.7
    "@tsconfig/node12": ^1.0.7
    "@tsconfig/node14": ^1.0.0
    "@tsconfig/node16": ^1.0.2
    acorn: ^8.4.1
    acorn-walk: ^8.1.1
    arg: ^4.1.0
    create-require: ^1.1.0
    diff: ^4.0.1
    make-error: ^1.1.1
    v8-compile-cache-lib: ^3.0.1
    yn: 3.1.1
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: fde256c9073969e234526e2cfead42591b9a2aec5222bac154b0de2fa9e4ceb30efcd717ee8bc785a56f3a119bdd5aa27b333d9dbec94ed254bd26f8944c67ac
  languageName: node
  linkType: hard

"tslib@npm:2.7.0":
  version: 2.7.0
  resolution: "tslib@npm:2.7.0"
  checksum: 1606d5c89f88d466889def78653f3aab0f88692e80bb2066d090ca6112ae250ec1cfa9dbfaab0d17b60da15a4186e8ec4d893801c67896b277c17374e36e1d28
  languageName: node
  linkType: hard

"tslib@npm:^1.11.1, tslib@npm:^1.9.3":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.3.1, tslib@npm:^2.6.2, tslib@npm:^2.7.0, tslib@npm:^2.8.0, tslib@npm:^2.8.1":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"tsort@npm:0.0.1":
  version: 0.0.1
  resolution: "tsort@npm:0.0.1"
  checksum: 581566c248690b9ea7e431e1545affb3d2cab0f5dcd0e45ddef815dfaec4864cb5f0cfd8072924dedbc0de9585ff07e3e65db60f14fab4123737b9bb6e72eacc
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 05f6510358f8afc62a057b8b692f05d70c1782b70db86d6a1e0d5e28a32389e52fa6e7707b6c5ecccacc031462e4bc35af85ecfe4bbc341767917b7cf6965711
  languageName: node
  linkType: hard

"tweetnacl-util@npm:^0.15.1":
  version: 0.15.1
  resolution: "tweetnacl-util@npm:0.15.1"
  checksum: ae6aa8a52cdd21a95103a4cc10657d6a2040b36c7a6da7b9d3ab811c6750a2d5db77e8c36969e75fdee11f511aa2b91c552496c6e8e989b6e490e54aca2864fc
  languageName: node
  linkType: hard

"tweetnacl@npm:^0.14.3, tweetnacl@npm:~0.14.0":
  version: 0.14.5
  resolution: "tweetnacl@npm:0.14.5"
  checksum: 6061daba1724f59473d99a7bb82e13f211cdf6e31315510ae9656fefd4779851cb927adad90f3b488c8ed77c106adc0421ea8055f6f976ff21b27c5c4e918487
  languageName: node
  linkType: hard

"tweetnacl@npm:^1.0.3":
  version: 1.0.3
  resolution: "tweetnacl@npm:1.0.3"
  checksum: e4a57cac188f0c53f24c7a33279e223618a2bfb5fea426231991652a13247bea06b081fd745d71291fcae0f4428d29beba1b984b1f1ce6f66b06a6d1ab90645c
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-check@npm:~0.3.2":
  version: 0.3.2
  resolution: "type-check@npm:0.3.2"
  dependencies:
    prelude-ls: ~1.1.2
  checksum: dd3b1495642731bc0e1fc40abe5e977e0263005551ac83342ecb6f4f89551d106b368ec32ad3fb2da19b3bd7b2d1f64330da2ea9176d8ddbfe389fb286eb5124
  languageName: node
  linkType: hard

"type-detect@npm:^4.0.0, type-detect@npm:^4.1.0":
  version: 4.1.0
  resolution: "type-detect@npm:4.1.0"
  checksum: 3b32f873cd02bc7001b00a61502b7ddc4b49278aabe68d652f732e1b5d768c072de0bc734b427abf59d0520a5f19a2e07309ab921ef02018fa1cb4af155cdb37
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 5b1b113529d59949d97b76977d545989ddc11b81bb0c766b6d2ccc65473cb4b4a5c7d24f5be2c2bb2de302a5d7a13c1732ea1d34c8c59b7e0ec1f890cf7fc424
  languageName: node
  linkType: hard

"typechain@npm:^8.0.0, typechain@npm:^8.3.2":
  version: 8.3.2
  resolution: "typechain@npm:8.3.2"
  dependencies:
    "@types/prettier": ^2.1.1
    debug: ^4.3.1
    fs-extra: ^7.0.0
    glob: 7.1.7
    js-sha3: ^0.8.0
    lodash: ^4.17.15
    mkdirp: ^1.0.4
    prettier: ^2.3.1
    ts-command-line-args: ^2.2.0
    ts-essentials: ^7.0.1
  peerDependencies:
    typescript: ">=4.3.0"
  bin:
    typechain: dist/cli/cli.js
  checksum: 146a1896fa93403404be78757790b0f95b5457efebcca16b61622e09c374d555ef4f837c1c4eedf77e03abc50276d96a2f33064ec09bb802f62d8cc2b13fce70
  languageName: node
  linkType: hard

"typescript@npm:>=4.5.0":
  version: 5.7.2
  resolution: "typescript@npm:5.7.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: b55300c4cefee8ee380d14fa9359ccb41ff8b54c719f6bc49b424899d662a5ce62ece390ce769568c7f4d14af844085255e63788740084444eb12ef423b13433
  languageName: node
  linkType: hard

"typescript@patch:typescript@>=4.5.0#~builtin<compat/typescript>":
  version: 5.7.2
  resolution: "typescript@patch:typescript@npm%3A5.7.2#~builtin<compat/typescript>::version=5.7.2&hash=d69c25"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 803430c6da2ba73c25a21880d8d4f08a56d9d2444e6db2ea949ac4abceeece8e4a442b7b9b585db7d8a0b47ebda2060e45fe8ee8b8aca23e27ec1d4844987ee6
  languageName: node
  linkType: hard

"typical@npm:^4.0.0":
  version: 4.0.0
  resolution: "typical@npm:4.0.0"
  checksum: a242081956825328f535e6195a924240b34daf6e7fdb573a1809a42b9f37fb8114fa99c7ab89a695e0cdb419d4149d067f6723e4b95855ffd39c6c4ca378efb3
  languageName: node
  linkType: hard

"typical@npm:^5.2.0":
  version: 5.2.0
  resolution: "typical@npm:5.2.0"
  checksum: ccaeb151a9a556291b495571ca44c4660f736fb49c29314bbf773c90fad92e9485d3cc2b074c933866c1595abbbc962f2b8bfc6e0f52a8c6b0cdd205442036ac
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.19.3
  resolution: "uglify-js@npm:3.19.3"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 7ed6272fba562eb6a3149cfd13cda662f115847865c03099e3995a0e7a910eba37b82d4fccf9e88271bb2bcbe505bb374967450f433c17fa27aa36d94a8d0553
  languageName: node
  linkType: hard

"undici-types@npm:~6.19.2":
  version: 6.19.8
  resolution: "undici-types@npm:6.19.8"
  checksum: de51f1b447d22571cf155dfe14ff6d12c5bdaec237c765085b439c38ca8518fc360e88c70f99469162bf2e14188a7b0bcb06e1ed2dc031042b984b0bb9544017
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: b7bc50f012dc6afbcce56c9fd62d7e86b20a62ff21f12b7b5cbf1973b9578d90f22a9c7fe50e638e96905d33893bf2f9f16d98929c4673c2480de05c6c96ea8b
  languageName: node
  linkType: hard

"undici@npm:^5.14.0":
  version: 5.28.4
  resolution: "undici@npm:5.28.4"
  dependencies:
    "@fastify/busboy": ^2.0.0
  checksum: a8193132d84540e4dc1895ecc8dbaa176e8a49d26084d6fbe48a292e28397cd19ec5d13bc13e604484e76f94f6e334b2bdc740d5f06a6e50c44072818d0c19f9
  languageName: node
  linkType: hard

"undici@npm:^6.11.1":
  version: 6.21.0
  resolution: "undici@npm:6.21.0"
  checksum: bc2eb26c4b010a4f816314d48d4529f62b1116405097b2c5f0ac68247c56049a857d11a9f05b237818f04ce4f51d6f5e8d6fcc6aae2ab816c2b7318a9706727c
  languageName: node
  linkType: hard

"unfetch@npm:^4.2.0":
  version: 4.2.0
  resolution: "unfetch@npm:4.2.0"
  checksum: 6a4b2557e1d921eaa80c4425ce27a404945ec26491ed06e62598f333996a91a44c7908cb26dc7c2746d735762b13276cf4aa41829b4c8f438dde63add3045d7a
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 40cdc60f6e61070fe658ca36016a8f4ec216b29bf04a55dce14e3710cc84c7448538ef4dad3728d0bfe29975ccd7bfb5f414c45e7b78883567fb31b246f02dff
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url@npm:^0.11.0":
  version: 0.11.4
  resolution: "url@npm:0.11.4"
  dependencies:
    punycode: ^1.4.1
    qs: ^6.12.3
  checksum: c25e587723d343d5d4248892393bfa5039ded9c2c07095a9d005bc64b7cb8956d623c0d8da8d1a28f71986a7a8d80fc2e9f9cf84235e48fa435a5cb4451062c6
  languageName: node
  linkType: hard

"utf-8-validate@npm:5.0.7":
  version: 5.0.7
  resolution: "utf-8-validate@npm:5.0.7"
  dependencies:
    node-gyp: latest
    node-gyp-build: ^4.3.0
  checksum: 588d272b359bf555a0c4c2ffe97286edc73126de132f63f4f0c80110bd06b67d3ce44d2b3d24feea6da13ced50c04d774ba4d25fe28576371cd714cd013bd3b7
  languageName: node
  linkType: hard

"utf8@npm:3.0.0":
  version: 3.0.0
  resolution: "utf8@npm:3.0.0"
  checksum: cb89a69ad9ab393e3eae9b25305b3ff08bebca9adc839191a34f90777eb2942f86a96369d2839925fea58f8f722f7e27031d697f10f5f39690f8c5047303e62d
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"util@npm:^0.12.5":
  version: 0.12.5
  resolution: "util@npm:0.12.5"
  dependencies:
    inherits: ^2.0.3
    is-arguments: ^1.0.4
    is-generator-function: ^1.0.7
    is-typed-array: ^1.1.3
    which-typed-array: ^1.1.2
  checksum: 705e51f0de5b446f4edec10739752ac25856541e0254ea1e7e45e5b9f9b0cb105bc4bd415736a6210edc68245a7f903bf085ffb08dd7deb8a0e847f60538a38a
  languageName: node
  linkType: hard

"uuid@npm:^3.3.2":
  version: 3.4.0
  resolution: "uuid@npm:3.4.0"
  bin:
    uuid: ./bin/uuid
  checksum: 58de2feed61c59060b40f8203c0e4ed7fd6f99d42534a499f1741218a1dd0c129f4aa1de797bcf822c8ea5da7e4137aa3673431a96dae729047f7aca7b27866f
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 78089ad549e21bcdbfca10c08850022b22024cdcc2da9b168bcf5a73a6ed7bf01a9cebb9eac28e03cd23a684d81e0502797e88f3ccd27a32aeab1cfc44c39da0
  languageName: node
  linkType: hard

"verror@npm:1.10.0":
  version: 1.10.0
  resolution: "verror@npm:1.10.0"
  dependencies:
    assert-plus: ^1.0.0
    core-util-is: 1.0.2
    extsprintf: ^1.2.0
  checksum: c431df0bedf2088b227a4e051e0ff4ca54df2c114096b0c01e1cbaadb021c30a04d7dd5b41ab277bcd51246ca135bf931d4c4c796ecae7a4fef6d744ecef36ea
  languageName: node
  linkType: hard

"viem@npm:^2.27.0":
  version: 2.28.0
  resolution: "viem@npm:2.28.0"
  dependencies:
    "@noble/curves": 1.8.2
    "@noble/hashes": 1.7.2
    "@scure/bip32": 1.6.2
    "@scure/bip39": 1.5.4
    abitype: 1.0.8
    isows: 1.0.6
    ox: 0.6.9
    ws: 8.18.1
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: e1c407ceb17c87de673c9f9b1cba69e76056f7203809c6d107ef4cf7ef4d47bdbd7cc0d31a6750fd8160907e544d7595552c2a069836b35bacae1b7c48309427
  languageName: node
  linkType: hard

"web3-core@npm:^4.4.0, web3-core@npm:^4.5.0, web3-core@npm:^4.6.0, web3-core@npm:^4.7.1":
  version: 4.7.1
  resolution: "web3-core@npm:4.7.1"
  dependencies:
    web3-errors: ^1.3.1
    web3-eth-accounts: ^4.3.1
    web3-eth-iban: ^4.0.7
    web3-providers-http: ^4.2.0
    web3-providers-ipc: ^4.0.7
    web3-providers-ws: ^4.0.8
    web3-types: ^1.10.0
    web3-utils: ^4.3.3
    web3-validator: ^2.0.6
  dependenciesMeta:
    web3-providers-ipc:
      optional: true
  checksum: 43b1c33fcbfda04f6e03625cce23286e6263f3f91aef3d0b3d133e126d2f15742093764523003076c388ce6b8336cd814d8d4e590c30fe419c3e3cc0bbe22f96
  languageName: node
  linkType: hard

"web3-errors@npm:^1.1.3, web3-errors@npm:^1.2.0, web3-errors@npm:^1.3.0, web3-errors@npm:^1.3.1":
  version: 1.3.1
  resolution: "web3-errors@npm:1.3.1"
  dependencies:
    web3-types: ^1.10.0
  checksum: 74efaa571a26ca590a2d1eefa33e73fcb870cb465c40430620514d34624731c895718815b5190a8eb248a5f14452672d48131830f555b934ab12448ab19bd39c
  languageName: node
  linkType: hard

"web3-eth-abi@npm:^4.4.1":
  version: 4.4.1
  resolution: "web3-eth-abi@npm:4.4.1"
  dependencies:
    abitype: 0.7.1
    web3-errors: ^1.3.1
    web3-types: ^1.10.0
    web3-utils: ^4.3.3
    web3-validator: ^2.0.6
  checksum: 0519a351e60ce0d74e555af181eb15cb2585c19d7d8eb84c9f8f4f05ae8495e5a8ace62d0c239a69452ccd8be3f0bd2bccda91cc0f7f4ae97b7dfcc6a2a13b4d
  languageName: node
  linkType: hard

"web3-eth-accounts@npm:^4.3.1":
  version: 4.3.1
  resolution: "web3-eth-accounts@npm:4.3.1"
  dependencies:
    "@ethereumjs/rlp": ^4.0.1
    crc-32: ^1.2.2
    ethereum-cryptography: ^2.0.0
    web3-errors: ^1.3.1
    web3-types: ^1.10.0
    web3-utils: ^4.3.3
    web3-validator: ^2.0.6
  checksum: 0cafe490507fbef5624cc463c53fbc85b1943b595c7e7002f5e6b5dc73ec56fc22ba8e89771347c17ecf2d9acfd479825242c1c454a1d601bd3fec65c39b8b8d
  languageName: node
  linkType: hard

"web3-eth-contract@npm:^4.5.0, web3-eth-contract@npm:^4.7.2":
  version: 4.7.2
  resolution: "web3-eth-contract@npm:4.7.2"
  dependencies:
    "@ethereumjs/rlp": ^5.0.2
    web3-core: ^4.7.1
    web3-errors: ^1.3.1
    web3-eth: ^4.11.1
    web3-eth-abi: ^4.4.1
    web3-types: ^1.10.0
    web3-utils: ^4.3.3
    web3-validator: ^2.0.6
  checksum: 4ab10b3214156d2acbb860a5fef7b1ba44fc7a3d3ec0c2273e0128c088c54e57ca99d918697a7e84f2ca9bbf4238e79fca46825959f55c7d01df23ec095ca599
  languageName: node
  linkType: hard

"web3-eth-ens@npm:^4.4.0":
  version: 4.4.0
  resolution: "web3-eth-ens@npm:4.4.0"
  dependencies:
    "@adraffy/ens-normalize": ^1.8.8
    web3-core: ^4.5.0
    web3-errors: ^1.2.0
    web3-eth: ^4.8.0
    web3-eth-contract: ^4.5.0
    web3-net: ^4.1.0
    web3-types: ^1.7.0
    web3-utils: ^4.3.0
    web3-validator: ^2.0.6
  checksum: f64dd27e679686993fbdbc9d6be22cc61afe0e63ce4bd2472b5812bec3c9c7cbdb1e1f2edfba9418694299f6603be062f8da834944f2cfe33f0b1b2497b412b5
  languageName: node
  linkType: hard

"web3-eth-iban@npm:^4.0.7":
  version: 4.0.7
  resolution: "web3-eth-iban@npm:4.0.7"
  dependencies:
    web3-errors: ^1.1.3
    web3-types: ^1.3.0
    web3-utils: ^4.0.7
    web3-validator: ^2.0.3
  checksum: c21785ece6c69146a605f60ebdd530e8a3faeda4302cbecef4665639c297fc11edd2f0dc8a6f6ba50b3f32d2c252d106687c24e31af3d297d5365a90f9badae0
  languageName: node
  linkType: hard

"web3-eth-personal@npm:^4.1.0":
  version: 4.1.0
  resolution: "web3-eth-personal@npm:4.1.0"
  dependencies:
    web3-core: ^4.6.0
    web3-eth: ^4.9.0
    web3-rpc-methods: ^1.3.0
    web3-types: ^1.8.0
    web3-utils: ^4.3.1
    web3-validator: ^2.0.6
  checksum: fc436e51641bdae4adc18dc41ad8a7359fbd91d3ed42416b095b4269257504e0296514c3ab67748b93a9a474c8d7e65e246eeebb7aab8a1f1a87baf64667406c
  languageName: node
  linkType: hard

"web3-eth@npm:^4.11.1, web3-eth@npm:^4.8.0, web3-eth@npm:^4.9.0":
  version: 4.11.1
  resolution: "web3-eth@npm:4.11.1"
  dependencies:
    setimmediate: ^1.0.5
    web3-core: ^4.7.1
    web3-errors: ^1.3.1
    web3-eth-abi: ^4.4.1
    web3-eth-accounts: ^4.3.1
    web3-net: ^4.1.0
    web3-providers-ws: ^4.0.8
    web3-rpc-methods: ^1.3.0
    web3-types: ^1.10.0
    web3-utils: ^4.3.3
    web3-validator: ^2.0.6
  checksum: 21b7e3e92499be72d92f594ccdffe44806231e2f50808d41b723e0a03617a2b31434d829465107cd74ac57fbf283c087d425a3423d5899ef128b90a8466a6d2b
  languageName: node
  linkType: hard

"web3-net@npm:^4.1.0":
  version: 4.1.0
  resolution: "web3-net@npm:4.1.0"
  dependencies:
    web3-core: ^4.4.0
    web3-rpc-methods: ^1.3.0
    web3-types: ^1.6.0
    web3-utils: ^4.3.0
  checksum: 8a257fbee5e73de20cf43a974be923f2d0eddcacd8bba39cc1426f97942d38c42816a3658ccf8db09920c8d99710e255c38ff9b84cdda011181c12b8c097a71d
  languageName: node
  linkType: hard

"web3-providers-http@npm:^4.2.0":
  version: 4.2.0
  resolution: "web3-providers-http@npm:4.2.0"
  dependencies:
    cross-fetch: ^4.0.0
    web3-errors: ^1.3.0
    web3-types: ^1.7.0
    web3-utils: ^4.3.1
  checksum: 8f65965979dc8a79720a52c9255acea5a564f6f6bd3d69eada60890e439bf71fd555ca3691a81543450f347652075e4a82adacb273b33e0444ec02b3ae6e34ce
  languageName: node
  linkType: hard

"web3-providers-ipc@npm:^4.0.7":
  version: 4.0.7
  resolution: "web3-providers-ipc@npm:4.0.7"
  dependencies:
    web3-errors: ^1.1.3
    web3-types: ^1.3.0
    web3-utils: ^4.0.7
  checksum: 83e734d833bd3663bc6d4a802c3eea83144a54244635d81d714913bd2f08a7463610fdb574bbbb1328c730340fea13730d4e33465fbf175d1c747170c142c7a7
  languageName: node
  linkType: hard

"web3-providers-ws@npm:^4.0.8":
  version: 4.0.8
  resolution: "web3-providers-ws@npm:4.0.8"
  dependencies:
    "@types/ws": 8.5.3
    isomorphic-ws: ^5.0.0
    web3-errors: ^1.2.0
    web3-types: ^1.7.0
    web3-utils: ^4.3.1
    ws: ^8.17.1
  checksum: ecbc2324c4a5ae3cb8ad756cf2081b380dd12103f3fe4b451366fa29cd8b2db85f63ead97afe524420603c9afe0519797bab300d6e29b8a96eb3084494855c26
  languageName: node
  linkType: hard

"web3-rpc-methods@npm:^1.3.0":
  version: 1.3.0
  resolution: "web3-rpc-methods@npm:1.3.0"
  dependencies:
    web3-core: ^4.4.0
    web3-types: ^1.6.0
    web3-validator: ^2.0.6
  checksum: 21673d6d2f539b0082a806bd0bf5d62882871584bf06337fc9b8399b6aeacd352c9fb19c6cae1cd25c145a60897c301fd12417848e34648bd5d5c7df7a76e095
  languageName: node
  linkType: hard

"web3-rpc-providers@npm:^1.0.0-rc.4":
  version: 1.0.0-rc.4
  resolution: "web3-rpc-providers@npm:1.0.0-rc.4"
  dependencies:
    web3-errors: ^1.3.1
    web3-providers-http: ^4.2.0
    web3-providers-ws: ^4.0.8
    web3-types: ^1.10.0
    web3-utils: ^4.3.3
    web3-validator: ^2.0.6
  checksum: bebb9cfaff5d179712af91a4da262b61ca69025234d1b35bf81487d49bab7fcb9a1327510d1ae51534a68be487263906c4d156a7e4f8b4dcda542925b2bf3411
  languageName: node
  linkType: hard

"web3-types@npm:^1.10.0, web3-types@npm:^1.3.0, web3-types@npm:^1.6.0, web3-types@npm:^1.7.0, web3-types@npm:^1.8.0":
  version: 1.10.0
  resolution: "web3-types@npm:1.10.0"
  checksum: a7e1a67dc0629073a55c096574cf60e4ba140a5b06558ed37b49403cf2100f973a389d2434ee0fbc510c0f874a69aca90c9c91cb98ee2c75927331f4c191c5bb
  languageName: node
  linkType: hard

"web3-utils@npm:^1.3.6":
  version: 1.10.4
  resolution: "web3-utils@npm:1.10.4"
  dependencies:
    "@ethereumjs/util": ^8.1.0
    bn.js: ^5.2.1
    ethereum-bloom-filters: ^1.0.6
    ethereum-cryptography: ^2.1.2
    ethjs-unit: 0.1.6
    number-to-bn: 1.7.0
    randombytes: ^2.1.0
    utf8: 3.0.0
  checksum: a1535817a4653f1b5cc868aa19305158122379078a41e13642e1ba64803f6f8e5dd2fb8c45c033612b8f52dde42d8008afce85296c0608276fe1513dece66a49
  languageName: node
  linkType: hard

"web3-utils@npm:^4.0.7, web3-utils@npm:^4.3.0, web3-utils@npm:^4.3.1, web3-utils@npm:^4.3.3":
  version: 4.3.3
  resolution: "web3-utils@npm:4.3.3"
  dependencies:
    ethereum-cryptography: ^2.0.0
    eventemitter3: ^5.0.1
    web3-errors: ^1.3.1
    web3-types: ^1.10.0
    web3-validator: ^2.0.6
  checksum: 7ba4fa6caae6e393e2ecbca7c7d36011c4a115658a4ab16a49cd52a5542f6dc2ad30766b8b8d908e1d68be800455554e8f87ae19c5e8ed8581987dc0fff55b7b
  languageName: node
  linkType: hard

"web3-validator@npm:^2.0.3, web3-validator@npm:^2.0.6":
  version: 2.0.6
  resolution: "web3-validator@npm:2.0.6"
  dependencies:
    ethereum-cryptography: ^2.0.0
    util: ^0.12.5
    web3-errors: ^1.2.0
    web3-types: ^1.6.0
    zod: ^3.21.4
  checksum: 15981ffce73cfa75c07f1ce0dbf65fe35fbdedc3ce19876e829b71a2a0e98aaf3aae90df764e6da7df3ff098d8fbf2ab37f58652fa93d7c3f8cb93b00f608c14
  languageName: node
  linkType: hard

"web3@npm:^4.5.0":
  version: 4.16.0
  resolution: "web3@npm:4.16.0"
  dependencies:
    web3-core: ^4.7.1
    web3-errors: ^1.3.1
    web3-eth: ^4.11.1
    web3-eth-abi: ^4.4.1
    web3-eth-accounts: ^4.3.1
    web3-eth-contract: ^4.7.2
    web3-eth-ens: ^4.4.0
    web3-eth-iban: ^4.0.7
    web3-eth-personal: ^4.1.0
    web3-net: ^4.1.0
    web3-providers-http: ^4.2.0
    web3-providers-ws: ^4.0.8
    web3-rpc-methods: ^1.3.0
    web3-rpc-providers: ^1.0.0-rc.4
    web3-types: ^1.10.0
    web3-utils: ^4.3.3
    web3-validator: ^2.0.6
  checksum: 7164f1068a3c56f6a5c0560468d4aef5945a1a357d916c36f8c6f6a61f5fbd562658c4a17e620e0dad2edc32f8c1a577483849616c4f8721f137525c1c18f1a5
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.2":
  version: 1.1.18
  resolution: "which-typed-array@npm:1.1.18"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
  checksum: d2feea7f51af66b3a240397aa41c796585033e1069f18e5b6d4cd3878538a1e7780596fd3ea9bf347c43d9e98e13be09b37d9ea3887cef29b11bc291fd47bb52
  languageName: node
  linkType: hard

"which@npm:^1.1.1, which@npm:^1.2.9, which@npm:^1.3.1":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: f2e185c6242244b8426c9df1510e86629192d93c1a986a7d2a591f2c24869e7ffd03d6dac07ca863b2e4c06f59a4cc9916c585b72ee9fa1aa609d0124df15e04
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"widest-line@npm:^3.1.0":
  version: 3.1.0
  resolution: "widest-line@npm:3.1.0"
  dependencies:
    string-width: ^4.0.0
  checksum: 03db6c9d0af9329c37d74378ff1d91972b12553c7d72a6f4e8525fe61563fa7adb0b9d6e8d546b7e059688712ea874edd5ded475999abdeedf708de9849310e0
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5, word-wrap@npm:~1.2.3":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 2a44b2788165d0a3de71fd517d4880a8e20ea3a82c080ce46e294f0b68b69a2e49cff5f99c600e275c698a90d12c5ea32aff06c311f0db2eb3f1201f3e7b2a04
  languageName: node
  linkType: hard

"wordwrapjs@npm:^4.0.0":
  version: 4.0.1
  resolution: "wordwrapjs@npm:4.0.1"
  dependencies:
    reduce-flatten: ^2.0.0
    typical: ^5.2.0
  checksum: 3d927f3c95d0ad990968da54c0ad8cde2801d8e91006cd7474c26e6b742cc8557250ce495c9732b2f9db1f903601cb74ec282e0f122ee0d02d7abe81e150eea8
  languageName: node
  linkType: hard

"workerpool@npm:^6.5.1":
  version: 6.5.1
  resolution: "workerpool@npm:6.5.1"
  checksum: f86d13f9139c3a57c5a5867e81905cd84134b499849405dec2ffe5b1acd30dabaa1809f6f6ee603a7c65e1e4325f21509db6b8398eaf202c8b8f5809e26a2e16
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:7.4.6":
  version: 7.4.6
  resolution: "ws@npm:7.4.6"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 3a990b32ed08c72070d5e8913e14dfcd831919205be52a3ff0b4cdd998c8d554f167c9df3841605cde8b11d607768cacab3e823c58c96a5c08c987e093eb767a
  languageName: node
  linkType: hard

"ws@npm:8.17.1":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 442badcce1f1178ec87a0b5372ae2e9771e07c4929a3180321901f226127f252441e8689d765aa5cfba5f50ac60dd830954afc5aeae81609aefa11d3ddf5cecf
  languageName: node
  linkType: hard

"ws@npm:8.18.1":
  version: 8.18.1
  resolution: "ws@npm:8.18.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 4658357185d891bc45cc2d42a84f9e192d047e8476fb5cba25b604f7d75ca87ca0dd54cd0b2cc49aeee57c79045a741cb7d0b14501953ac60c790cd105c42f23
  languageName: node
  linkType: hard

"ws@npm:^7.4.6":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: f9bb062abf54cc8f02d94ca86dcd349c3945d63851f5d07a3a61c2fcb755b15a88e943a63cf580cbdb5b74436d67ef6b67f745b8f7c0814e411379138e1863cb
  languageName: node
  linkType: hard

"ws@npm:^8.17.1":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 91d4d35bc99ff6df483bdf029b9ea4bfd7af1f16fc91231a96777a63d263e1eabf486e13a2353970efc534f9faa43bdbf9ee76525af22f4752cbc5ebda333975
  languageName: node
  linkType: hard

"xtend@npm:^4.0.1, xtend@npm:^4.0.2, xtend@npm:~4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yaml@npm:^1.10.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: ce4ada136e8a78a0b08dc10b4b900936912d15de59905b2bf415b4d33c63df1d555d23acb2a41b23cf9fb5da41c256441afca3d6509de7247daa062fd2c5ea5f
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2, yargs-parser@npm:^20.2.9":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 8bb69015f2b0ff9e17b2c8e6bfe224ab463dd00ca211eece72a4cd8a906224d2703fb8a326d36fdd0e68701e201b2a60ed7cf81ce0fd9b3799f9fe7745977ae3
  languageName: node
  linkType: hard

"yargs-unparser@npm:^2.0.0":
  version: 2.0.0
  resolution: "yargs-unparser@npm:2.0.0"
  dependencies:
    camelcase: ^6.0.0
    decamelize: ^4.0.0
    flat: ^5.0.2
    is-plain-obj: ^2.1.0
  checksum: 68f9a542c6927c3768c2f16c28f71b19008710abd6b8f8efbac6dcce26bbb68ab6503bed1d5994bdbc2df9a5c87c161110c1dfe04c6a3fe5c6ad1b0e15d9a8a3
  languageName: node
  linkType: hard

"yargs@npm:^16.2.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: ^7.0.2
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.0
    y18n: ^5.0.5
    yargs-parser: ^20.2.2
  checksum: b14afbb51e3251a204d81937c86a7e9d4bdbf9a2bcee38226c900d00f522969ab675703bee2a6f99f8e20103f608382936034e64d921b74df82b63c07c5e8f59
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 2c487b0e149e746ef48cda9f8bad10fc83693cd69d7f9dcd8be4214e985de33a29c9e24f3c0d6bcf2288427040a8947406ab27f7af67ee9456e6b84854f02dd6
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zod@npm:^3.21.4":
  version: 3.24.1
  resolution: "zod@npm:3.24.1"
  checksum: dcd5334725b29555593c186fd6505878bb7ccb4f5954f728d2de24bf71f9397492d83bdb69d5b8a376eb500a02273ae0691b57deb1eb8718df3f64c77cc5534a
  languageName: node
  linkType: hard
