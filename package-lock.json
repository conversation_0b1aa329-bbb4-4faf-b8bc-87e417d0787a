{"name": "fula-chain", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "fula-chain", "version": "1.0.0", "dependencies": {"@chainlink/contracts": "^1.3.0", "@nomicfoundation/hardhat-ignition": "^0.15.9", "@openzeppelin/contracts": "^5.0.2", "@openzeppelin/contracts-upgradeable": "^5.0.2", "@openzeppelin/upgrades-core": "^1.41.0", "@polkadot/api": "^15.0.2", "@polkadot/keyring": "^13.2.3", "@polkadot/types": "^15.0.2", "@polkadot/types-codec": "^15.0.2", "@polkadot/types-create": "^15.0.2", "@polkadot/types-known": "^15.0.2", "@polkadot/types-support": "^15.0.2", "@polkadot/util": "^13.2.3", "tslib": "^2.8.1"}, "devDependencies": {"@buildwithsygma/sygma-sdk-core": "^2.7", "@chainsafe/hardhat-plugin-multichain-deploy": "^1.0.4", "@chainsafe/hardhat-ts-artifact-plugin": "^1.0.0", "@nomicfoundation/hardhat-chai-matchers": "^2.0.4", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-network-helpers": "^1.0.10", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomicfoundation/hardhat-verify": "^2.0.4", "@nomicfoundation/hardhat-web3-v4": "^1.0.0", "@openzeppelin/hardhat-upgrades": "^3.8.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.2.0", "@types/eslint": "^8", "@types/mocha": ">=9.1.0", "@types/node": "^22.10.2", "chai": "4", "eslint": "^8", "ethers": "^6.13.4", "hardhat": "^2.19.5", "hardhat-gas-reporter": "^1.0.10", "solidity-coverage": "^0.8.7", "ts-node": ">=8.0.0", "typechain": "^8.3.2", "typescript": ">=4.5.0", "web3": "^4.5.0"}}, "node_modules/@adraffy/ens-normalize": {"version": "1.10.1", "license": "MIT"}, "node_modules/@arbitrum/nitro-contracts": {"version": "1.1.1", "hasInstallScript": true, "license": "BUSL-1.1", "dependencies": {"@offchainlabs/upgrade-executor": "1.1.0-beta.0", "@openzeppelin/contracts": "4.5.0", "@openzeppelin/contracts-upgradeable": "4.5.2", "patch-package": "^6.4.7"}}, "node_modules/@arbitrum/nitro-contracts/node_modules/@openzeppelin/contracts": {"version": "4.5.0", "license": "MIT"}, "node_modules/@arbitrum/nitro-contracts/node_modules/@openzeppelin/contracts-upgradeable": {"version": "4.5.2", "license": "MIT"}, "node_modules/@arbitrum/token-bridge-contracts": {"version": "1.1.2", "license": "Apache-2.0", "dependencies": {"@arbitrum/nitro-contracts": "^1.0.0-beta.8", "@offchainlabs/upgrade-executor": "1.1.0-beta.0", "@openzeppelin/contracts": "4.8.3", "@openzeppelin/contracts-upgradeable": "4.8.3"}, "optionalDependencies": {"@openzeppelin/upgrades-core": "^1.24.1"}}, "node_modules/@arbitrum/token-bridge-contracts/node_modules/@arbitrum/nitro-contracts": {"version": "1.3.0", "hasInstallScript": true, "license": "BUSL-1.1", "dependencies": {"@offchainlabs/upgrade-executor": "1.1.0-beta.0", "@openzeppelin/contracts": "4.5.0", "@openzeppelin/contracts-upgradeable": "4.5.2", "patch-package": "^6.4.7"}}, "node_modules/@arbitrum/token-bridge-contracts/node_modules/@arbitrum/nitro-contracts/node_modules/@openzeppelin/contracts": {"version": "4.5.0", "license": "MIT"}, "node_modules/@arbitrum/token-bridge-contracts/node_modules/@arbitrum/nitro-contracts/node_modules/@openzeppelin/contracts-upgradeable": {"version": "4.5.2", "license": "MIT"}, "node_modules/@arbitrum/token-bridge-contracts/node_modules/@openzeppelin/contracts": {"version": "4.8.3", "license": "MIT"}, "node_modules/@arbitrum/token-bridge-contracts/node_modules/@openzeppelin/contracts-upgradeable": {"version": "4.8.3", "license": "MIT"}, "node_modules/@aws-crypto/sha256-js": {"version": "1.2.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@aws-crypto/util": "^1.2.2", "@aws-sdk/types": "^3.1.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-js/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/@aws-crypto/util": {"version": "1.2.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "^3.1.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/util/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/@aws-sdk/types": {"version": "3.714.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@smithy/types": "^3.7.2", "tslib": "^2.6.2"}, "engines": {"node": ">=16.0.0"}}, "node_modules/@aws-sdk/util-utf8-browser": {"version": "3.259.0", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.3.1"}}, "node_modules/@babel/runtime": {"version": "7.26.0", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core": {"version": "2.11.2", "dev": true, "license": "LGPL-3.0-or-later", "dependencies": {"@buildwithsygma/sygma-contracts": "2.5.1", "@ethersproject/abi": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/contracts": "^5.7.0", "@ethersproject/providers": "^5.7.2", "@polkadot/api": "10.7.2", "@polkadot/api-augment": "10.7.2", "@polkadot/extension-inject": "0.46.3", "@polkadot/keyring": "12.2.1", "@polkadot/rpc-provider": "10.7.2", "@polkadot/ui-keyring": "3.4.1", "@polkadot/util": "12.2.1", "@polkadot/util-crypto": "12.2.1", "ethers": "5.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@buildwithsygma/sygma-contracts": {"version": "2.5.1", "dev": true, "license": "BUSL-1.1", "dependencies": {"@openzeppelin/contracts": "4.5.0"}, "peerDependencies": {"ethers": ">= 5.0.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@ethersproject/hdnode": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.6.0", "@ethersproject/basex": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/pbkdf2": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/sha2": "^5.6.0", "@ethersproject/signing-key": "^5.6.0", "@ethersproject/strings": "^5.6.0", "@ethersproject/transactions": "^5.6.0", "@ethersproject/wordlists": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@ethersproject/json-wallets": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.6.0", "@ethersproject/address": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/hdnode": "^5.6.0", "@ethersproject/keccak256": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/pbkdf2": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/random": "^5.6.0", "@ethersproject/strings": "^5.6.0", "@ethersproject/transactions": "^5.6.0", "aes-js": "3.0.0", "scrypt-js": "3.0.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@ethersproject/json-wallets/node_modules/@ethersproject/hdnode": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/pbkdf2": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@openzeppelin/contracts": {"version": "4.5.0", "dev": true, "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/api-augment": "10.7.2", "@polkadot/api-base": "10.7.2", "@polkadot/api-derive": "10.7.2", "@polkadot/keyring": "^12.2.1", "@polkadot/rpc-augment": "10.7.2", "@polkadot/rpc-core": "10.7.2", "@polkadot/rpc-provider": "10.7.2", "@polkadot/types": "10.7.2", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/types-known": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "eventemitter3": "^5.0.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/api/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject": {"version": "0.46.3", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/rpc-provider": "^10.7.1", "@polkadot/types": "^10.7.1", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "@polkadot/x-global": "^12.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@polkadot/api": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/rpc-provider": {"version": "10.13.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.6.2", "@polkadot/types": "10.13.1", "@polkadot/types-support": "10.13.1", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "@polkadot/x-fetch": "^12.6.2", "@polkadot/x-global": "^12.6.2", "@polkadot/x-ws": "^12.6.2", "eventemitter3": "^5.0.1", "mock-socket": "^9.3.1", "nock": "^13.5.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@substrate/connect": "0.8.8"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/extension-inject/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/keyring": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.2.1", "@polkadot/util-crypto": "12.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@polkadot/util": "12.2.1", "@polkadot/util-crypto": "12.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types": {"version": "10.13.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.6.2", "@polkadot/types-augment": "10.13.1", "@polkadot/types-codec": "10.13.1", "@polkadot/types-create": "10.13.1", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-codec/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-create/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/networks": "^12.2.1", "@polkadot/types": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-known/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-support": {"version": "10.13.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types-support/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/types-augment": {"version": "10.13.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types": "10.13.1", "@polkadot/types-codec": "10.13.1", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/types-codec": {"version": "10.13.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.6.2", "@polkadot/x-bigint": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/types-create": {"version": "10.13.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.13.1", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/types/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/ui-keyring": {"version": "3.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/ui-settings": "3.4.1", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "mkdirp": "^3.0.1", "rxjs": "^7.8.1", "store": "^2.0.12", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@polkadot/keyring": "*", "@polkadot/ui-settings": "*", "@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/ui-keyring/node_modules/@polkadot/util": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/util/-/util-12.6.2.tgz", "integrity": "sha512-l8TubR7CLEY47240uki0TQzFvtnxFIO7uI/0GoWzpYD/O62EIAMRsuY01N4DuwgKq2ZWD59WhzsLYmA5K6ksdw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/ui-keyring/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/ui-settings": {"version": "3.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/networks": "^12.2.1", "@polkadot/util": "^12.2.1", "eventemitter3": "^5.0.1", "store": "^2.0.12", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@polkadot/networks": "*", "@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.2.1", "@polkadot/x-global": "12.2.1", "@polkadot/x-textdecoder": "12.2.1", "@polkadot/x-textencoder": "12.2.1", "@types/bn.js": "^5.1.1", "bn.js": "^5.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "1.0.0", "@noble/hashes": "1.3.0", "@polkadot/networks": "12.2.1", "@polkadot/util": "12.2.1", "@polkadot/wasm-crypto": "^7.2.1", "@polkadot/wasm-util": "^7.2.1", "@polkadot/x-bigint": "12.2.1", "@polkadot/x-randomvalues": "12.2.1", "@scure/base": "1.1.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@polkadot/util": "12.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@noble/curves": {"version": "1.0.0", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"@noble/hashes": "1.3.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@noble/hashes": {"version": "1.3.0", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@polkadot/networks": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.2.1", "@substrate/ss58-registry": "^1.40.0", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@polkadot/x-bigint": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@polkadot/x-global": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@polkadot/x-randomvalues": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@polkadot/util": "12.2.1", "@polkadot/wasm-util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util-crypto/node_modules/@scure/base": {"version": "1.1.1", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util/node_modules/@polkadot/x-bigint": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util/node_modules/@polkadot/x-global": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util/node_modules/@polkadot/x-textdecoder": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/util/node_modules/@polkadot/x-textencoder": {"version": "12.2.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.2.1", "tslib": "^2.5.0"}, "engines": {"node": ">=16"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/@substrate/connect": {"version": "0.8.8", "dev": true, "license": "GPL-3.0-only", "optional": true, "dependencies": {"@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.1", "@substrate/light-client-extension-helpers": "^0.0.4", "smoldot": "2.0.22"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers": {"version": "5.6.2", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "5.6.0", "@ethersproject/abstract-provider": "5.6.0", "@ethersproject/abstract-signer": "5.6.0", "@ethersproject/address": "5.6.0", "@ethersproject/base64": "5.6.0", "@ethersproject/basex": "5.6.0", "@ethersproject/bignumber": "5.6.0", "@ethersproject/bytes": "5.6.1", "@ethersproject/constants": "5.6.0", "@ethersproject/contracts": "5.6.0", "@ethersproject/hash": "5.6.0", "@ethersproject/hdnode": "5.6.0", "@ethersproject/json-wallets": "5.6.0", "@ethersproject/keccak256": "5.6.0", "@ethersproject/logger": "5.6.0", "@ethersproject/networks": "5.6.1", "@ethersproject/pbkdf2": "5.6.0", "@ethersproject/properties": "5.6.0", "@ethersproject/providers": "5.6.2", "@ethersproject/random": "5.6.0", "@ethersproject/rlp": "5.6.0", "@ethersproject/sha2": "5.6.0", "@ethersproject/signing-key": "5.6.0", "@ethersproject/solidity": "5.6.0", "@ethersproject/strings": "5.6.0", "@ethersproject/transactions": "5.6.0", "@ethersproject/units": "5.6.0", "@ethersproject/wallet": "5.6.0", "@ethersproject/web": "5.6.0", "@ethersproject/wordlists": "5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/constants": "^5.6.0", "@ethersproject/hash": "^5.6.0", "@ethersproject/keccak256": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/strings": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/abstract-signer": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/hash": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/networks": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/transactions": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abi/node_modules/@ethersproject/web": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/networks": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/transactions": "^5.6.0", "@ethersproject/web": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/networks": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/transactions": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-provider/node_modules/@ethersproject/web": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/networks": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/transactions": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/abstract-signer/node_modules/@ethersproject/web": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/address": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/keccak256": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/rlp": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/address/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/address/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/address/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/address/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/address/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/base64": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/base64/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/base64/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/basex": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/properties": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/basex/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/basex/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/basex/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/bignumber": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0", "bn.js": "^4.11.9"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/bignumber/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/bignumber/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/bignumber/node_modules/bn.js": {"version": "4.12.1", "dev": true, "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/bytes": {"version": "5.6.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/bytes/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/constants": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/constants/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/constants/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/constants/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.6.0", "@ethersproject/abstract-provider": "^5.6.0", "@ethersproject/abstract-signer": "^5.6.0", "@ethersproject/address": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/constants": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/transactions": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/abi": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/abstract-signer": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/hash": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/networks": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/transactions": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/contracts/node_modules/@ethersproject/web": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.6.0", "@ethersproject/address": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/keccak256": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/strings": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/abstract-signer": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/networks": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/transactions": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/hash/node_modules/@ethersproject/web": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/keccak256": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/keccak256/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/keccak256/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/logger": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/networks": {"version": "5.6.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/networks/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/pbkdf2": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/sha2": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/pbkdf2/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/pbkdf2/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/pbkdf2/node_modules/@ethersproject/sha2": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/properties": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/properties/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers": {"version": "5.6.2", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.6.0", "@ethersproject/abstract-signer": "^5.6.0", "@ethersproject/address": "^5.6.0", "@ethersproject/basex": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/constants": "^5.6.0", "@ethersproject/hash": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/networks": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/random": "^5.6.0", "@ethersproject/rlp": "^5.6.0", "@ethersproject/sha2": "^5.6.0", "@ethersproject/strings": "^5.6.0", "@ethersproject/transactions": "^5.6.0", "@ethersproject/web": "^5.6.0", "bech32": "1.1.4", "ws": "7.4.6"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/abstract-signer": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/basex": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/hash": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/networks": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/random": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/sha2": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/transactions": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/providers/node_modules/@ethersproject/web": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/random": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/random/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/random/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/rlp": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/rlp/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/rlp/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/sha2": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/sha2/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/sha2/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/signing-key": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "bn.js": "^4.11.9", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/signing-key/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/signing-key/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/signing-key/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/signing-key/node_modules/bn.js": {"version": "4.12.1", "dev": true, "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/strings": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/constants": "^5.6.0", "@ethersproject/logger": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/strings/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/strings/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/strings/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/strings/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/constants": "^5.6.0", "@ethersproject/keccak256": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/rlp": "^5.6.0", "@ethersproject/signing-key": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/transactions/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/strings": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/web/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.6.0", "@ethersproject/hash": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/strings": "^5.6.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/abstract-signer": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/address": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/base64": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/bignumber": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/bytes": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/constants": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/hash": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/keccak256": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/logger": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/networks": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/properties": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/rlp": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/signing-key": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/strings": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/transactions": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ethers/node_modules/@ethersproject/wordlists/node_modules/@ethersproject/web": {"version": "5.7.1", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@buildwithsygma/sygma-sdk-core/node_modules/ws": {"version": "7.4.6", "dev": true, "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@bytecodealliance/preview2-shim": {"version": "0.17.0", "license": "(Apache-2.0 WITH LLVM-exception)"}, "node_modules/@chainlink/contracts": {"version": "1.3.0", "license": "MIT", "dependencies": {"@arbitrum/nitro-contracts": "1.1.1", "@arbitrum/token-bridge-contracts": "1.1.2", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "~2.27.8", "@eth-optimism/contracts": "0.6.0", "@openzeppelin/contracts": "4.9.3", "@openzeppelin/contracts-upgradeable": "4.9.3", "@scroll-tech/contracts": "0.1.0", "@zksync/contracts": "git+https://github.com/matter-labs/era-contracts.git#446d391d34bdb48255d5f8fef8a8248925fc98b9", "semver": "^7.6.3"}, "engines": {"node": ">=18", "pnpm": ">=9"}}, "node_modules/@chainlink/contracts/node_modules/@openzeppelin/contracts": {"version": "4.9.3", "license": "MIT"}, "node_modules/@chainlink/contracts/node_modules/@openzeppelin/contracts-upgradeable": {"version": "4.9.3", "license": "MIT"}, "node_modules/@chainsafe/hardhat-plugin-multichain-deploy": {"version": "1.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"@chainsafe/hardhat-plugin-multichain-deploy-contracts": "^1.0.2", "web3": "^4.3.0"}, "peerDependencies": {"@buildwithsygma/sygma-sdk-core": ">= 2.7.1", "hardhat": "^2.0.0"}}, "node_modules/@chainsafe/hardhat-plugin-multichain-deploy-contracts": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/@chainsafe/hardhat-ts-artifact-plugin": {"version": "1.0.0", "dev": true, "license": "MIT", "peerDependencies": {"hardhat": "^2.0.0"}}, "node_modules/@changesets/apply-release-plan": {"version": "7.0.7", "license": "MIT", "dependencies": {"@changesets/config": "^3.0.5", "@changesets/get-version-range-type": "^0.4.0", "@changesets/git": "^3.0.2", "@changesets/should-skip-package": "^0.1.1", "@changesets/types": "^6.0.0", "@manypkg/get-packages": "^1.1.3", "detect-indent": "^6.0.0", "fs-extra": "^7.0.1", "lodash.startcase": "^4.4.0", "outdent": "^0.5.0", "prettier": "^2.7.1", "resolve-from": "^5.0.0", "semver": "^7.5.3"}}, "node_modules/@changesets/assemble-release-plan": {"version": "6.0.5", "license": "MIT", "dependencies": {"@changesets/errors": "^0.2.0", "@changesets/get-dependents-graph": "^2.1.2", "@changesets/should-skip-package": "^0.1.1", "@changesets/types": "^6.0.0", "@manypkg/get-packages": "^1.1.3", "semver": "^7.5.3"}}, "node_modules/@changesets/changelog-git": {"version": "0.2.0", "license": "MIT", "dependencies": {"@changesets/types": "^6.0.0"}}, "node_modules/@changesets/changelog-github": {"version": "0.5.0", "license": "MIT", "dependencies": {"@changesets/get-github-info": "^0.6.0", "@changesets/types": "^6.0.0", "dotenv": "^8.1.0"}}, "node_modules/@changesets/cli": {"version": "2.27.11", "license": "MIT", "dependencies": {"@changesets/apply-release-plan": "^7.0.7", "@changesets/assemble-release-plan": "^6.0.5", "@changesets/changelog-git": "^0.2.0", "@changesets/config": "^3.0.5", "@changesets/errors": "^0.2.0", "@changesets/get-dependents-graph": "^2.1.2", "@changesets/get-release-plan": "^4.0.6", "@changesets/git": "^3.0.2", "@changesets/logger": "^0.1.1", "@changesets/pre": "^2.0.1", "@changesets/read": "^0.6.2", "@changesets/should-skip-package": "^0.1.1", "@changesets/types": "^6.0.0", "@changesets/write": "^0.3.2", "@manypkg/get-packages": "^1.1.3", "ansi-colors": "^4.1.3", "ci-info": "^3.7.0", "enquirer": "^2.4.1", "external-editor": "^3.1.0", "fs-extra": "^7.0.1", "mri": "^1.2.0", "p-limit": "^2.2.0", "package-manager-detector": "^0.2.0", "picocolors": "^1.1.0", "resolve-from": "^5.0.0", "semver": "^7.5.3", "spawndamnit": "^3.0.1", "term-size": "^2.1.0"}, "bin": {"changeset": "bin.js"}}, "node_modules/@changesets/cli/node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@changesets/config": {"version": "3.0.5", "license": "MIT", "dependencies": {"@changesets/errors": "^0.2.0", "@changesets/get-dependents-graph": "^2.1.2", "@changesets/logger": "^0.1.1", "@changesets/types": "^6.0.0", "@manypkg/get-packages": "^1.1.3", "fs-extra": "^7.0.1", "micromatch": "^4.0.8"}}, "node_modules/@changesets/errors": {"version": "0.2.0", "license": "MIT", "dependencies": {"extendable-error": "^0.1.5"}}, "node_modules/@changesets/get-dependents-graph": {"version": "2.1.2", "license": "MIT", "dependencies": {"@changesets/types": "^6.0.0", "@manypkg/get-packages": "^1.1.3", "picocolors": "^1.1.0", "semver": "^7.5.3"}}, "node_modules/@changesets/get-github-info": {"version": "0.6.0", "license": "MIT", "dependencies": {"dataloader": "^1.4.0", "node-fetch": "^2.5.0"}}, "node_modules/@changesets/get-release-plan": {"version": "4.0.6", "license": "MIT", "dependencies": {"@changesets/assemble-release-plan": "^6.0.5", "@changesets/config": "^3.0.5", "@changesets/pre": "^2.0.1", "@changesets/read": "^0.6.2", "@changesets/types": "^6.0.0", "@manypkg/get-packages": "^1.1.3"}}, "node_modules/@changesets/get-version-range-type": {"version": "0.4.0", "license": "MIT"}, "node_modules/@changesets/git": {"version": "3.0.2", "license": "MIT", "dependencies": {"@changesets/errors": "^0.2.0", "@manypkg/get-packages": "^1.1.3", "is-subdir": "^1.1.1", "micromatch": "^4.0.8", "spawndamnit": "^3.0.1"}}, "node_modules/@changesets/logger": {"version": "0.1.1", "license": "MIT", "dependencies": {"picocolors": "^1.1.0"}}, "node_modules/@changesets/parse": {"version": "0.4.0", "license": "MIT", "dependencies": {"@changesets/types": "^6.0.0", "js-yaml": "^3.13.1"}}, "node_modules/@changesets/pre": {"version": "2.0.1", "license": "MIT", "dependencies": {"@changesets/errors": "^0.2.0", "@changesets/types": "^6.0.0", "@manypkg/get-packages": "^1.1.3", "fs-extra": "^7.0.1"}}, "node_modules/@changesets/read": {"version": "0.6.2", "license": "MIT", "dependencies": {"@changesets/git": "^3.0.2", "@changesets/logger": "^0.1.1", "@changesets/parse": "^0.4.0", "@changesets/types": "^6.0.0", "fs-extra": "^7.0.1", "p-filter": "^2.1.0", "picocolors": "^1.1.0"}}, "node_modules/@changesets/should-skip-package": {"version": "0.1.1", "license": "MIT", "dependencies": {"@changesets/types": "^6.0.0", "@manypkg/get-packages": "^1.1.3"}}, "node_modules/@changesets/types": {"version": "6.0.0", "license": "MIT"}, "node_modules/@changesets/write": {"version": "0.3.2", "license": "MIT", "dependencies": {"@changesets/types": "^6.0.0", "fs-extra": "^7.0.1", "human-id": "^1.0.2", "prettier": "^2.7.1"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "devOptional": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.1", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/@eslint/eslintrc/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@eslint/js": {"version": "8.57.1", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@eth-optimism/contracts": {"version": "0.6.0", "license": "MIT", "dependencies": {"@eth-optimism/core-utils": "0.12.0", "@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0"}, "peerDependencies": {"ethers": "^5"}}, "node_modules/@eth-optimism/core-utils": {"version": "0.12.0", "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.7.0", "@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/contracts": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/providers": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0", "bufio": "^1.0.7", "chai": "^4.3.4"}}, "node_modules/@ethereumjs/rlp": {"version": "4.0.1", "dev": true, "license": "MPL-2.0", "bin": {"rlp": "bin/rlp"}, "engines": {"node": ">=14"}}, "node_modules/@ethereumjs/util": {"version": "8.1.0", "dev": true, "license": "MPL-2.0", "dependencies": {"@ethereumjs/rlp": "^4.0.1", "ethereum-cryptography": "^2.0.0", "micro-ftch": "^0.3.1"}, "engines": {"node": ">=14"}}, "node_modules/@ethersproject/abi": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@ethersproject/abstract-signer": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@ethersproject/address": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@ethersproject/base64": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@ethersproject/basex": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@ethersproject/bignumber": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@ethersproject/bytes": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/constants": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@ethersproject/contracts": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.7.0", "@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0"}}, "node_modules/@ethersproject/hash": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@ethersproject/hdnode": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/pbkdf2": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}}, "node_modules/@ethersproject/json-wallets": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hdnode": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/pbkdf2": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "aes-js": "3.0.0", "scrypt-js": "3.0.1"}}, "node_modules/@ethersproject/keccak256": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@ethersproject/logger": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@ethersproject/networks": {"version": "5.7.1", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/pbkdf2": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/sha2": "^5.7.0"}}, "node_modules/@ethersproject/properties": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/providers": {"version": "5.7.2", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0", "bech32": "1.1.4", "ws": "7.4.6"}}, "node_modules/@ethersproject/providers/node_modules/ws": {"version": "7.4.6", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@ethersproject/random": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/rlp": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/sha2": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "hash.js": "1.1.7"}}, "node_modules/@ethersproject/signing-key": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@ethersproject/solidity": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/keccak256": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/sha2": "^5.6.0", "@ethersproject/strings": "^5.6.0"}}, "node_modules/@ethersproject/strings": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/transactions": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@ethersproject/units": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.6.0", "@ethersproject/constants": "^5.6.0", "@ethersproject/logger": "^5.6.0"}}, "node_modules/@ethersproject/wallet": {"version": "5.6.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.6.0", "@ethersproject/abstract-signer": "^5.6.0", "@ethersproject/address": "^5.6.0", "@ethersproject/bignumber": "^5.6.0", "@ethersproject/bytes": "^5.6.0", "@ethersproject/hash": "^5.6.0", "@ethersproject/hdnode": "^5.6.0", "@ethersproject/json-wallets": "^5.6.0", "@ethersproject/keccak256": "^5.6.0", "@ethersproject/logger": "^5.6.0", "@ethersproject/properties": "^5.6.0", "@ethersproject/random": "^5.6.0", "@ethersproject/signing-key": "^5.6.0", "@ethersproject/transactions": "^5.6.0", "@ethersproject/wordlists": "^5.6.0"}}, "node_modules/@ethersproject/web": {"version": "5.7.1", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@ethersproject/wordlists": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@fastify/busboy": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "devOptional": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "devOptional": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "devOptional": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@manypkg/find-root": {"version": "1.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.5.5", "@types/node": "^12.7.1", "find-up": "^4.1.0", "fs-extra": "^8.1.0"}}, "node_modules/@manypkg/find-root/node_modules/@types/node": {"version": "12.20.55", "license": "MIT"}, "node_modules/@manypkg/find-root/node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@manypkg/find-root/node_modules/fs-extra": {"version": "8.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/@manypkg/find-root/node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@manypkg/find-root/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/@manypkg/get-packages": {"version": "1.1.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.5.5", "@changesets/types": "^4.0.1", "@manypkg/find-root": "^1.1.0", "fs-extra": "^8.1.0", "globby": "^11.0.0", "read-yaml-file": "^1.1.0"}}, "node_modules/@manypkg/get-packages/node_modules/@changesets/types": {"version": "4.1.0", "license": "MIT"}, "node_modules/@manypkg/get-packages/node_modules/fs-extra": {"version": "8.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/@manypkg/get-packages/node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@manypkg/get-packages/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/@metamask/eth-sig-util": {"version": "4.0.1", "license": "ISC", "dependencies": {"ethereumjs-abi": "^0.6.8", "ethereumjs-util": "^6.2.1", "ethjs-util": "^0.1.6", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@metamask/eth-sig-util/node_modules/@types/bn.js": {"version": "4.11.6", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@metamask/eth-sig-util/node_modules/bn.js": {"version": "4.12.1", "license": "MIT"}, "node_modules/@metamask/eth-sig-util/node_modules/elliptic": {"version": "6.6.1", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/@metamask/eth-sig-util/node_modules/ethereum-cryptography": {"version": "0.1.3", "license": "MIT", "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}, "node_modules/@metamask/eth-sig-util/node_modules/ethereumjs-util": {"version": "6.2.1", "license": "MPL-2.0", "dependencies": {"@types/bn.js": "^4.11.3", "bn.js": "^4.11.0", "create-hash": "^1.1.2", "elliptic": "^6.5.2", "ethereum-cryptography": "^0.1.3", "ethjs-util": "0.1.6", "rlp": "^2.2.3"}}, "node_modules/@noble/curves": {"version": "1.7.0", "license": "MIT", "dependencies": {"@noble/hashes": "1.6.0"}, "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/curves/node_modules/@noble/hashes": {"version": "1.6.0", "license": "MIT", "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/hashes": {"version": "1.6.1", "license": "MIT", "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/secp256k1": {"version": "1.7.1", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT"}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@nomicfoundation/edr": {"version": "0.6.5", "license": "MIT", "dependencies": {"@nomicfoundation/edr-darwin-arm64": "0.6.5", "@nomicfoundation/edr-darwin-x64": "0.6.5", "@nomicfoundation/edr-linux-arm64-gnu": "0.6.5", "@nomicfoundation/edr-linux-arm64-musl": "0.6.5", "@nomicfoundation/edr-linux-x64-gnu": "0.6.5", "@nomicfoundation/edr-linux-x64-musl": "0.6.5", "@nomicfoundation/edr-win32-x64-msvc": "0.6.5"}, "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/edr-darwin-arm64": {"version": "0.6.5", "license": "MIT", "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/edr-darwin-x64": {"version": "0.6.5", "license": "MIT", "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/edr-linux-arm64-gnu": {"version": "0.6.5", "license": "MIT", "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/edr-linux-arm64-musl": {"version": "0.6.5", "license": "MIT", "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/edr-linux-x64-gnu": {"version": "0.6.5", "license": "MIT", "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/edr-linux-x64-musl": {"version": "0.6.5", "license": "MIT", "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/edr-win32-x64-msvc": {"version": "0.6.5", "license": "MIT", "engines": {"node": ">= 18"}}, "node_modules/@nomicfoundation/ethereumjs-common": {"version": "4.0.4", "license": "MIT", "dependencies": {"@nomicfoundation/ethereumjs-util": "9.0.4"}}, "node_modules/@nomicfoundation/ethereumjs-rlp": {"version": "5.0.4", "license": "MPL-2.0", "bin": {"rlp": "bin/rlp.cjs"}, "engines": {"node": ">=18"}}, "node_modules/@nomicfoundation/ethereumjs-tx": {"version": "5.0.4", "license": "MPL-2.0", "dependencies": {"@nomicfoundation/ethereumjs-common": "4.0.4", "@nomicfoundation/ethereumjs-rlp": "5.0.4", "@nomicfoundation/ethereumjs-util": "9.0.4", "ethereum-cryptography": "0.1.3"}, "engines": {"node": ">=18"}, "peerDependencies": {"c-kzg": "^2.1.2"}, "peerDependenciesMeta": {"c-kzg": {"optional": true}}}, "node_modules/@nomicfoundation/ethereumjs-tx/node_modules/ethereum-cryptography": {"version": "0.1.3", "license": "MIT", "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}, "node_modules/@nomicfoundation/ethereumjs-util": {"version": "9.0.4", "license": "MPL-2.0", "dependencies": {"@nomicfoundation/ethereumjs-rlp": "5.0.4", "ethereum-cryptography": "0.1.3"}, "engines": {"node": ">=18"}, "peerDependencies": {"c-kzg": "^2.1.2"}, "peerDependenciesMeta": {"c-kzg": {"optional": true}}}, "node_modules/@nomicfoundation/ethereumjs-util/node_modules/ethereum-cryptography": {"version": "0.1.3", "license": "MIT", "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}, "node_modules/@nomicfoundation/hardhat-chai-matchers": {"version": "2.0.8", "dev": true, "license": "MIT", "dependencies": {"@types/chai-as-promised": "^7.1.3", "chai-as-promised": "^7.1.1", "deep-eql": "^4.0.1", "ordinal": "^1.0.3"}, "peerDependencies": {"@nomicfoundation/hardhat-ethers": "^3.0.0", "chai": "^4.2.0", "ethers": "^6.1.0", "hardhat": "^2.9.4"}}, "node_modules/@nomicfoundation/hardhat-ethers": {"version": "3.0.8", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "lodash.isequal": "^4.5.0"}, "peerDependencies": {"ethers": "^6.1.0", "hardhat": "^2.0.0"}}, "node_modules/@nomicfoundation/hardhat-ignition": {"version": "0.15.9", "license": "MIT", "dependencies": {"@nomicfoundation/ignition-core": "^0.15.9", "@nomicfoundation/ignition-ui": "^0.15.9", "chalk": "^4.0.0", "debug": "^4.3.2", "fs-extra": "^10.0.0", "json5": "^2.2.3", "prompts": "^2.4.2"}, "peerDependencies": {"@nomicfoundation/hardhat-verify": "^2.0.1", "hardhat": "^2.18.0"}}, "node_modules/@nomicfoundation/hardhat-ignition/node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@nomicfoundation/hardhat-network-helpers": {"version": "1.0.12", "dev": true, "license": "MIT", "dependencies": {"ethereumjs-util": "^7.1.4"}, "peerDependencies": {"hardhat": "^2.9.5"}}, "node_modules/@nomicfoundation/hardhat-toolbox": {"version": "4.0.0", "dev": true, "license": "MIT", "peerDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "@types/node": ">=16.0.0", "chai": "^4.2.0", "ethers": "^6.4.0", "hardhat": "^2.11.0", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.1", "ts-node": ">=8.0.0", "typechain": "^8.3.0", "typescript": ">=4.5.0"}}, "node_modules/@nomicfoundation/hardhat-verify": {"version": "2.0.12", "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.1.2", "@ethersproject/address": "^5.0.2", "cbor": "^8.1.0", "debug": "^4.1.1", "lodash.clonedeep": "^4.5.0", "picocolors": "^1.1.0", "semver": "^6.3.0", "table": "^6.8.0", "undici": "^5.14.0"}, "peerDependencies": {"hardhat": "^2.0.4"}}, "node_modules/@nomicfoundation/hardhat-verify/node_modules/cbor": {"version": "8.1.0", "license": "MIT", "dependencies": {"nofilter": "^3.1.0"}, "engines": {"node": ">=12.19"}}, "node_modules/@nomicfoundation/hardhat-verify/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@nomicfoundation/hardhat-web3-v4": {"version": "1.0.0", "dev": true, "license": "MIT", "peerDependencies": {"hardhat": "^2.0.0", "web3": "^4.0.1"}}, "node_modules/@nomicfoundation/ignition-core": {"version": "0.15.9", "license": "MIT", "dependencies": {"@ethersproject/address": "5.6.1", "@nomicfoundation/solidity-analyzer": "^0.1.1", "cbor": "^9.0.0", "debug": "^4.3.2", "ethers": "^6.7.0", "fs-extra": "^10.0.0", "immer": "10.0.2", "lodash": "4.17.21", "ndjson": "2.0.0"}}, "node_modules/@nomicfoundation/ignition-core/node_modules/@ethersproject/address": {"version": "5.6.1", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.6.2", "@ethersproject/bytes": "^5.6.1", "@ethersproject/keccak256": "^5.6.1", "@ethersproject/logger": "^5.6.0", "@ethersproject/rlp": "^5.6.1"}}, "node_modules/@nomicfoundation/ignition-core/node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@nomicfoundation/ignition-ui": {"version": "0.15.9"}, "node_modules/@nomicfoundation/slang": {"version": "0.18.3", "license": "MIT", "dependencies": {"@bytecodealliance/preview2-shim": "0.17.0"}}, "node_modules/@nomicfoundation/solidity-analyzer": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 12"}, "optionalDependencies": {"@nomicfoundation/solidity-analyzer-darwin-arm64": "0.1.2", "@nomicfoundation/solidity-analyzer-darwin-x64": "0.1.2", "@nomicfoundation/solidity-analyzer-linux-arm64-gnu": "0.1.2", "@nomicfoundation/solidity-analyzer-linux-arm64-musl": "0.1.2", "@nomicfoundation/solidity-analyzer-linux-x64-gnu": "0.1.2", "@nomicfoundation/solidity-analyzer-linux-x64-musl": "0.1.2", "@nomicfoundation/solidity-analyzer-win32-x64-msvc": "0.1.2"}}, "node_modules/@nomicfoundation/solidity-analyzer-darwin-arm64": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/@nomicfoundation/solidity-analyzer-darwin-x64": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/@nomicfoundation/solidity-analyzer-linux-arm64-gnu": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/@nomicfoundation/solidity-analyzer-linux-arm64-musl": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/@nomicfoundation/solidity-analyzer-linux-x64-gnu": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/@nomicfoundation/solidity-analyzer-linux-x64-musl": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/@nomicfoundation/solidity-analyzer-win32-x64-msvc": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/@offchainlabs/upgrade-executor": {"version": "1.1.0-beta.0", "license": "Apache 2.0", "dependencies": {"@openzeppelin/contracts": "4.7.3", "@openzeppelin/contracts-upgradeable": "4.7.3"}}, "node_modules/@offchainlabs/upgrade-executor/node_modules/@openzeppelin/contracts": {"version": "4.7.3", "license": "MIT"}, "node_modules/@offchainlabs/upgrade-executor/node_modules/@openzeppelin/contracts-upgradeable": {"version": "4.7.3", "license": "MIT"}, "node_modules/@openzeppelin/contracts": {"version": "5.1.0", "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable": {"version": "5.1.0", "license": "MIT", "peerDependencies": {"@openzeppelin/contracts": "5.1.0"}}, "node_modules/@openzeppelin/defender-sdk-base-client": {"version": "1.15.2", "dev": true, "license": "MIT", "dependencies": {"amazon-cognito-identity-js": "^6.3.6", "async-retry": "^1.3.3"}}, "node_modules/@openzeppelin/defender-sdk-deploy-client": {"version": "1.15.2", "dev": true, "license": "MIT", "dependencies": {"@openzeppelin/defender-sdk-base-client": "^1.15.2", "axios": "^1.7.2", "lodash": "^4.17.21"}}, "node_modules/@openzeppelin/defender-sdk-network-client": {"version": "1.15.2", "dev": true, "license": "MIT", "dependencies": {"@openzeppelin/defender-sdk-base-client": "^1.15.2", "axios": "^1.7.2", "lodash": "^4.17.21"}}, "node_modules/@openzeppelin/hardhat-upgrades": {"version": "3.8.0", "dev": true, "license": "MIT", "dependencies": {"@openzeppelin/defender-sdk-base-client": "^1.14.4", "@openzeppelin/defender-sdk-deploy-client": "^1.14.4", "@openzeppelin/defender-sdk-network-client": "^1.14.4", "@openzeppelin/upgrades-core": "^1.41.0", "chalk": "^4.1.0", "debug": "^4.1.1", "ethereumjs-util": "^7.1.5", "proper-lockfile": "^4.1.1", "undici": "^6.11.1"}, "bin": {"migrate-oz-cli-project": "dist/scripts/migrate-oz-cli-project.js"}, "peerDependencies": {"@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "ethers": "^6.6.0", "hardhat": "^2.0.2"}, "peerDependenciesMeta": {"@nomicfoundation/hardhat-verify": {"optional": true}}}, "node_modules/@openzeppelin/hardhat-upgrades/node_modules/undici": {"version": "6.21.0", "dev": true, "license": "MIT", "engines": {"node": ">=18.17"}}, "node_modules/@openzeppelin/upgrades-core": {"version": "1.41.0", "license": "MIT", "dependencies": {"@nomicfoundation/slang": "^0.18.3", "cbor": "^9.0.0", "chalk": "^4.1.0", "compare-versions": "^6.0.0", "debug": "^4.1.1", "ethereumjs-util": "^7.0.3", "minimatch": "^9.0.5", "minimist": "^1.2.7", "proper-lockfile": "^4.1.1", "solidity-ast": "^0.4.51"}, "bin": {"openzeppelin-upgrades-core": "dist/cli/cli.js"}}, "node_modules/@openzeppelin/upgrades-core/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@polkadot-api/client": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@polkadot-api/metadata-builders": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@polkadot-api/substrate-bindings": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@polkadot-api/substrate-client": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@polkadot-api/utils": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0"}, "peerDependencies": {"rxjs": ">=7.8.0"}}, "node_modules/@polkadot-api/client/node_modules/@polkadot-api/substrate-client": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@polkadot-api/client/node_modules/@polkadot-api/utils": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@polkadot-api/json-rpc-provider": {"version": "0.0.1", "license": "MIT", "optional": true}, "node_modules/@polkadot-api/json-rpc-provider-proxy": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@polkadot-api/metadata-builders": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@polkadot-api/substrate-bindings": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@polkadot-api/utils": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0"}}, "node_modules/@polkadot-api/metadata-builders/node_modules/@polkadot-api/utils": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@polkadot-api/observable-client": {"version": "0.3.2", "license": "MIT", "optional": true, "dependencies": {"@polkadot-api/metadata-builders": "0.3.2", "@polkadot-api/substrate-bindings": "0.6.0", "@polkadot-api/utils": "0.1.0"}, "peerDependencies": {"@polkadot-api/substrate-client": "0.1.4", "rxjs": ">=7.8.0"}}, "node_modules/@polkadot-api/observable-client/node_modules/@polkadot-api/metadata-builders": {"version": "0.3.2", "license": "MIT", "optional": true, "dependencies": {"@polkadot-api/substrate-bindings": "0.6.0", "@polkadot-api/utils": "0.1.0"}}, "node_modules/@polkadot-api/observable-client/node_modules/@polkadot-api/substrate-bindings": {"version": "0.6.0", "license": "MIT", "optional": true, "dependencies": {"@noble/hashes": "^1.3.1", "@polkadot-api/utils": "0.1.0", "@scure/base": "^1.1.1", "scale-ts": "^1.6.0"}}, "node_modules/@polkadot-api/substrate-bindings": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@noble/hashes": "^1.3.1", "@polkadot-api/utils": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@scure/base": "^1.1.1", "scale-ts": "^1.6.0"}}, "node_modules/@polkadot-api/substrate-bindings/node_modules/@polkadot-api/utils": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@polkadot-api/substrate-client": {"version": "0.1.4", "license": "MIT", "optional": true, "dependencies": {"@polkadot-api/json-rpc-provider": "0.0.1", "@polkadot-api/utils": "0.1.0"}}, "node_modules/@polkadot-api/utils": {"version": "0.1.0", "license": "MIT", "optional": true}, "node_modules/@polkadot/api": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/api-augment": "15.0.2", "@polkadot/api-base": "15.0.2", "@polkadot/api-derive": "15.0.2", "@polkadot/keyring": "^13.2.3", "@polkadot/rpc-augment": "15.0.2", "@polkadot/rpc-core": "15.0.2", "@polkadot/rpc-provider": "15.0.2", "@polkadot/types": "15.0.2", "@polkadot/types-augment": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/types-create": "15.0.2", "@polkadot/types-known": "15.0.2", "@polkadot/util": "^13.2.3", "@polkadot/util-crypto": "^13.2.3", "eventemitter3": "^5.0.1", "rxjs": "^7.8.1", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api-augment": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/api-base": "10.7.2", "@polkadot/rpc-augment": "10.7.2", "@polkadot/types": "10.7.2", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-augment/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/api-base": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/rpc-core": "10.7.2", "@polkadot/types": "10.7.2", "@polkadot/util": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-base/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/api-derive": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/api": "10.7.2", "@polkadot/api-augment": "10.7.2", "@polkadot/api-base": "10.7.2", "@polkadot/rpc-core": "10.7.2", "@polkadot/types": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/api": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/api-augment": "10.7.2", "@polkadot/api-base": "10.7.2", "@polkadot/api-derive": "10.7.2", "@polkadot/keyring": "^12.2.1", "@polkadot/rpc-augment": "10.7.2", "@polkadot/rpc-core": "10.7.2", "@polkadot/rpc-provider": "10.7.2", "@polkadot/types": "10.7.2", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/types-known": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "eventemitter3": "^5.0.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/types-known": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/networks": "^12.2.1", "@polkadot/types": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/api-derive/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/api/node_modules/@polkadot-api/json-rpc-provider-proxy": {"version": "0.1.0", "license": "MIT", "optional": true}, "node_modules/@polkadot/api/node_modules/@polkadot/api-augment": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/api-base": "15.0.2", "@polkadot/rpc-augment": "15.0.2", "@polkadot/types": "15.0.2", "@polkadot/types-augment": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/util": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/api-base": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/rpc-core": "15.0.2", "@polkadot/types": "15.0.2", "@polkadot/util": "^13.2.3", "rxjs": "^7.8.1", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/api-derive": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/api": "15.0.2", "@polkadot/api-augment": "15.0.2", "@polkadot/api-base": "15.0.2", "@polkadot/rpc-core": "15.0.2", "@polkadot/types": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/util": "^13.2.3", "@polkadot/util-crypto": "^13.2.3", "rxjs": "^7.8.1", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/rpc-augment": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/rpc-core": "15.0.2", "@polkadot/types": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/util": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/rpc-core": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/rpc-augment": "15.0.2", "@polkadot/rpc-provider": "15.0.2", "@polkadot/types": "15.0.2", "@polkadot/util": "^13.2.3", "rxjs": "^7.8.1", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/rpc-provider": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^13.2.3", "@polkadot/types": "15.0.2", "@polkadot/types-support": "15.0.2", "@polkadot/util": "^13.2.3", "@polkadot/util-crypto": "^13.2.3", "@polkadot/x-fetch": "^13.2.3", "@polkadot/x-global": "^13.2.3", "@polkadot/x-ws": "^13.2.3", "eventemitter3": "^5.0.1", "mock-socket": "^9.3.1", "nock": "^13.5.5", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@substrate/connect": "0.8.11"}}, "node_modules/@polkadot/api/node_modules/@polkadot/types-augment": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/types": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/util": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/x-fetch": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "node-fetch": "^3.3.2", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/x-global": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@polkadot/x-ws": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "tslib": "^2.8.0", "ws": "^8.18.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api/node_modules/@substrate/connect": {"version": "0.8.11", "license": "GPL-3.0-only", "optional": true, "dependencies": {"@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.5", "@substrate/light-client-extension-helpers": "^1.0.0", "smoldot": "2.0.26"}}, "node_modules/@polkadot/api/node_modules/@substrate/light-client-extension-helpers": {"version": "1.0.0", "license": "MIT", "optional": true, "dependencies": {"@polkadot-api/json-rpc-provider": "^0.0.1", "@polkadot-api/json-rpc-provider-proxy": "^0.1.0", "@polkadot-api/observable-client": "^0.3.0", "@polkadot-api/substrate-client": "^0.1.2", "@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.5", "rxjs": "^7.8.1"}, "peerDependencies": {"smoldot": "2.x"}}, "node_modules/@polkadot/api/node_modules/node-fetch": {"version": "3.3.2", "license": "MIT", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/@polkadot/api/node_modules/smoldot": {"version": "2.0.26", "license": "GPL-3.0-or-later WITH Classpath-exception-2.0", "optional": true, "dependencies": {"ws": "^8.8.1"}}, "node_modules/@polkadot/keyring": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/util": "13.2.3", "@polkadot/util-crypto": "13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "13.2.3", "@polkadot/util-crypto": "13.2.3"}}, "node_modules/@polkadot/networks": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@substrate/ss58-registry": "^1.44.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/networks/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/rpc-augment": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/rpc-core": "10.7.2", "@polkadot/types": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-augment/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/rpc-core": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/rpc-augment": "10.7.2", "@polkadot/rpc-provider": "10.7.2", "@polkadot/types": "10.7.2", "@polkadot/util": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-core/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/rpc-provider": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types": "10.7.2", "@polkadot/types-support": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "@polkadot/x-fetch": "^12.2.1", "@polkadot/x-global": "^12.2.1", "@polkadot/x-ws": "^12.2.1", "eventemitter3": "^5.0.1", "mock-socket": "^9.2.1", "nock": "^13.3.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}, "optionalDependencies": {"@substrate/connect": "0.7.26"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/types-support": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/types": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^13.2.3", "@polkadot/types-augment": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/types-create": "15.0.2", "@polkadot/util": "^13.2.3", "@polkadot/util-crypto": "^13.2.3", "rxjs": "^7.8.1", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-augment": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/keyring": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/types": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/keyring": "^12.2.1", "@polkadot/types-augment": "10.7.2", "@polkadot/types-codec": "10.7.2", "@polkadot/types-create": "10.7.2", "@polkadot/util": "^12.2.1", "@polkadot/util-crypto": "^12.2.1", "rxjs": "^7.8.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/types-codec": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^12.2.1", "@polkadot/x-bigint": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/types-create": {"version": "10.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "10.7.2", "@polkadot/util": "^12.2.1", "tslib": "^2.5.2"}, "engines": {"node": ">=16"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/util": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/util-crypto": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/wasm-util": {"version": "7.4.1", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/types-augment/node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/types-codec": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^13.2.3", "@polkadot/x-bigint": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-codec/node_modules/@polkadot/x-bigint": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-codec/node_modules/@polkadot/x-global": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-create": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/types-codec": "15.0.2", "@polkadot/util": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-known": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/networks": "^13.2.3", "@polkadot/types": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/types-create": "15.0.2", "@polkadot/util": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-known/node_modules/@polkadot/networks": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/util": "13.2.3", "@substrate/ss58-registry": "^1.51.0", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-support": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/util": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types/node_modules/@polkadot/types-augment": {"version": "15.0.2", "license": "Apache-2.0", "dependencies": {"@polkadot/types": "15.0.2", "@polkadot/types-codec": "15.0.2", "@polkadot/util": "^13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-bigint": "13.2.3", "@polkadot/x-global": "13.2.3", "@polkadot/x-textdecoder": "13.2.3", "@polkadot/x-textencoder": "13.2.3", "@types/bn.js": "^5.1.6", "bn.js": "^5.2.1", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util-crypto": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "13.2.3", "@polkadot/util": "13.2.3", "@polkadot/wasm-crypto": "^7.4.1", "@polkadot/wasm-util": "^7.4.1", "@polkadot/x-bigint": "13.2.3", "@polkadot/x-randomvalues": "13.2.3", "@scure/base": "^1.1.7", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "13.2.3"}}, "node_modules/@polkadot/util-crypto/node_modules/@polkadot/networks": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/util": "13.2.3", "@substrate/ss58-registry": "^1.51.0", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util-crypto/node_modules/@polkadot/x-bigint": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util-crypto/node_modules/@polkadot/x-global": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util/node_modules/@polkadot/x-bigint": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util/node_modules/@polkadot/x-global": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util/node_modules/@polkadot/x-textdecoder": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util/node_modules/@polkadot/x-textencoder": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/wasm-bridge": {"version": "7.4.1", "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/wasm-crypto": {"version": "7.4.1", "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-init": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.4.1", "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/wasm-crypto-init": {"version": "7.4.1", "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-bridge": "7.4.1", "@polkadot/wasm-crypto-asmjs": "7.4.1", "@polkadot/wasm-crypto-wasm": "7.4.1", "@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.4.1", "license": "Apache-2.0", "dependencies": {"@polkadot/wasm-util": "7.4.1", "tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/wasm-util": {"version": "7.4.1", "license": "Apache-2.0", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/x-bigint": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-fetch": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "node-fetch": "^3.3.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-fetch/node_modules/node-fetch": {"version": "3.3.2", "dev": true, "license": "MIT", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/@polkadot/x-global": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-randomvalues": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "13.2.3", "tslib": "^2.8.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "13.2.3", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/x-randomvalues/node_modules/@polkadot/x-global": {"version": "13.2.3", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-textdecoder": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-textencoder": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-ws": {"version": "12.6.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2", "ws": "^8.15.1"}, "engines": {"node": ">=18"}}, "node_modules/@scroll-tech/contracts": {"version": "0.1.0", "license": "MIT"}, "node_modules/@scure/base": {"version": "1.2.1", "license": "MIT", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip32": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"@noble/curves": "~1.4.0", "@noble/hashes": "~1.4.0", "@scure/base": "~1.1.6"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip32/node_modules/@noble/curves": {"version": "1.4.2", "dev": true, "license": "MIT", "dependencies": {"@noble/hashes": "1.4.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip32/node_modules/@noble/hashes": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip32/node_modules/@scure/base": {"version": "1.1.9", "dev": true, "license": "MIT", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip39": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"@noble/hashes": "~1.4.0", "@scure/base": "~1.1.6"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip39/node_modules/@noble/hashes": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@scure/bip39/node_modules/@scure/base": {"version": "1.1.9", "dev": true, "license": "MIT", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@sentry/core": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/hub": "5.30.0", "@sentry/minimal": "5.30.0", "@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/core/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@sentry/hub": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/hub/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@sentry/minimal": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/hub": "5.30.0", "@sentry/types": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/minimal/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@sentry/node": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/core": "5.30.0", "@sentry/hub": "5.30.0", "@sentry/tracing": "5.30.0", "@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "cookie": "^0.4.1", "https-proxy-agent": "^5.0.0", "lru_map": "^0.3.3", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/node/node_modules/agent-base": {"version": "6.0.2", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/@sentry/node/node_modules/https-proxy-agent": {"version": "5.0.1", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/@sentry/node/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@sentry/tracing": {"version": "5.30.0", "license": "MIT", "dependencies": {"@sentry/hub": "5.30.0", "@sentry/minimal": "5.30.0", "@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/tracing/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@sentry/types": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=6"}}, "node_modules/@sentry/utils": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/utils/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@smithy/types": {"version": "3.7.2", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=16.0.0"}}, "node_modules/@solidity-parser/parser": {"version": "0.14.5", "dev": true, "license": "MIT", "dependencies": {"antlr4ts": "^0.5.0-alpha.4"}}, "node_modules/@substrate/connect": {"version": "0.7.26", "dev": true, "license": "GPL-3.0-only", "optional": true, "dependencies": {"@substrate/connect-extension-protocol": "^1.0.1", "eventemitter3": "^4.0.7", "smoldot": "1.0.4"}}, "node_modules/@substrate/connect-extension-protocol": {"version": "2.2.1", "license": "GPL-3.0-only", "optional": true}, "node_modules/@substrate/connect-known-chains": {"version": "1.8.1", "license": "GPL-3.0-only", "optional": true}, "node_modules/@substrate/connect/node_modules/@substrate/connect-extension-protocol": {"version": "1.0.1", "dev": true, "license": "GPL-3.0-only", "optional": true}, "node_modules/@substrate/connect/node_modules/eventemitter3": {"version": "4.0.7", "dev": true, "license": "MIT", "optional": true}, "node_modules/@substrate/connect/node_modules/smoldot": {"version": "1.0.4", "dev": true, "license": "GPL-3.0-or-later WITH Classpath-exception-2.0", "optional": true, "dependencies": {"pako": "^2.0.4", "ws": "^8.8.1"}}, "node_modules/@substrate/light-client-extension-helpers": {"version": "0.0.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@polkadot-api/client": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@polkadot-api/json-rpc-provider": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@polkadot-api/json-rpc-provider-proxy": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@polkadot-api/substrate-client": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.1", "rxjs": "^7.8.1"}, "peerDependencies": {"smoldot": "2.x"}}, "node_modules/@substrate/light-client-extension-helpers/node_modules/@polkadot-api/json-rpc-provider": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@substrate/light-client-extension-helpers/node_modules/@polkadot-api/substrate-client": {"version": "0.0.1-492c132563ea6b40ae1fc5470dec4cd18768d182.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/@substrate/ss58-registry": {"version": "1.51.0", "license": "Apache-2.0"}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "devOptional": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "devOptional": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "devOptional": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "devOptional": true, "license": "MIT"}, "node_modules/@typechain/ethers-v6": {"version": "0.5.1", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.15", "ts-essentials": "^7.0.1"}, "peerDependencies": {"ethers": "6.x", "typechain": "^8.3.2", "typescript": ">=4.7.0"}}, "node_modules/@typechain/hardhat": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"fs-extra": "^9.1.0"}, "peerDependencies": {"@typechain/ethers-v6": "^0.5.1", "ethers": "^6.1.0", "hardhat": "^2.9.9", "typechain": "^8.3.2"}}, "node_modules/@typechain/hardhat/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@types/bn.js": {"version": "5.1.6", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/chai": {"version": "4.3.20", "dev": true, "license": "MIT"}, "node_modules/@types/chai-as-promised": {"version": "7.1.8", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "*"}}, "node_modules/@types/chai-as-promised/node_modules/@types/chai": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"@types/deep-eql": "*"}}, "node_modules/@types/concat-stream": {"version": "1.6.1", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/deep-eql": {"version": "4.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/eslint": {"version": "8.56.12", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/estree": {"version": "1.0.6", "dev": true, "license": "MIT"}, "node_modules/@types/form-data": {"version": "0.0.33", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/glob": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/lru-cache": {"version": "5.1.1", "license": "MIT"}, "node_modules/@types/minimatch": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/@types/mocha": {"version": "10.0.10", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "22.10.2", "license": "MIT", "dependencies": {"undici-types": "~6.20.0"}}, "node_modules/@types/pbkdf2": {"version": "3.1.2", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/prettier": {"version": "2.7.3", "dev": true, "license": "MIT"}, "node_modules/@types/qs": {"version": "6.9.17", "dev": true, "license": "MIT"}, "node_modules/@types/secp256k1": {"version": "4.0.6", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/ws": {"version": "8.5.3", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@ungap/structured-clone": {"version": "1.2.1", "dev": true, "license": "ISC"}, "node_modules/@yarnpkg/lockfile": {"version": "1.1.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@zksync/contracts": {"name": "era-contracts", "version": "0.1.0", "resolved": "git+ssh://**************/matter-labs/era-contracts.git#446d391d34bdb48255d5f8fef8a8248925fc98b9", "integrity": "sha512-PV//gJPWNy/fp4QTnm5GxzyGpiCp3T2cRqmrzx5olIs/Kw7VGhksj49sUdnXYnWmMnWCuWxwLgrQ8fwVcOsyHA==", "workspaces": {"packages": ["l1-contracts", "l2-contracts", "system-contracts", "gas-bound-caller"], "nohoist": ["**/@openzeppelin/**"]}}, "node_modules/abitype": {"version": "0.7.1", "dev": true, "license": "MIT", "peerDependencies": {"typescript": ">=4.9.4", "zod": "^3 >=3.19.1"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "node_modules/acorn": {"version": "8.14.0", "devOptional": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "devOptional": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/adm-zip": {"version": "0.4.16", "license": "MIT", "engines": {"node": ">=0.3.0"}}, "node_modules/aes-js": {"version": "3.0.0", "dev": true, "license": "MIT"}, "node_modules/aggregate-error": {"version": "3.1.0", "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/amazon-cognito-identity-js": {"version": "6.3.14", "dev": true, "license": "Apache-2.0", "dependencies": {"@aws-crypto/sha256-js": "1.2.2", "buffer": "4.9.2", "fast-base64-decode": "^1.0.0", "isomorphic-unfetch": "^3.0.0", "js-cookie": "^2.2.1"}}, "node_modules/amdefine": {"version": "1.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON> OR MIT", "optional": true, "engines": {"node": ">=0.4.2"}}, "node_modules/ansi-align": {"version": "3.0.1", "license": "ISC", "dependencies": {"string-width": "^4.1.0"}}, "node_modules/ansi-colors": {"version": "4.1.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/antlr4ts": {"version": "0.5.0-dev", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"source-map-support": "^0.5.16"}}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/anymatch/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/arg": {"version": "4.1.3", "devOptional": true, "license": "MIT"}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/argparse/node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/array-back": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/array-union": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array-uniq": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/asap": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/assertion-error": {"version": "1.1.0", "license": "MIT", "engines": {"node": "*"}}, "node_modules/astral-regex": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/async": {"version": "1.5.2", "dev": true, "license": "MIT"}, "node_modules/async-retry": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"retry": "0.13.1"}}, "node_modules/async-retry/node_modules/retry": {"version": "0.13.1", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/asynckit": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/axios": {"version": "1.7.9", "dev": true, "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base-x": {"version": "3.0.10", "license": "MIT", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bech32": {"version": "1.1.4", "license": "MIT"}, "node_modules/better-path-resolve": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-windows": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/binary-extensions": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/blakejs": {"version": "1.2.1", "license": "MIT"}, "node_modules/bn.js": {"version": "5.2.1", "license": "MIT"}, "node_modules/boxen": {"version": "5.1.2", "license": "MIT", "dependencies": {"ansi-align": "^3.0.0", "camelcase": "^6.2.0", "chalk": "^4.1.0", "cli-boxes": "^2.2.1", "string-width": "^4.2.2", "type-fest": "^0.20.2", "widest-line": "^3.1.0", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/brorand": {"version": "1.1.0", "license": "MIT"}, "node_modules/browser-stdout": {"version": "1.3.1", "license": "ISC"}, "node_modules/browserify-aes": {"version": "1.2.0", "license": "MIT", "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/bs58": {"version": "4.0.1", "license": "MIT", "dependencies": {"base-x": "^3.0.2"}}, "node_modules/bs58check": {"version": "2.1.2", "license": "MIT", "dependencies": {"bs58": "^4.0.0", "create-hash": "^1.1.0", "safe-buffer": "^5.1.2"}}, "node_modules/buffer": {"version": "4.9.2", "dev": true, "license": "MIT", "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/buffer-xor": {"version": "1.0.3", "license": "MIT"}, "node_modules/bufio": {"version": "1.2.2", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "6.3.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/caseless": {"version": "0.12.0", "dev": true, "license": "Apache-2.0"}, "node_modules/cbor": {"version": "9.0.2", "license": "MIT", "dependencies": {"nofilter": "^3.1.0"}, "engines": {"node": ">=16"}}, "node_modules/chai": {"version": "4.5.0", "license": "MIT", "dependencies": {"assertion-error": "^1.1.0", "check-error": "^1.0.3", "deep-eql": "^4.1.3", "get-func-name": "^2.0.2", "loupe": "^2.3.6", "pathval": "^1.1.1", "type-detect": "^4.1.0"}, "engines": {"node": ">=4"}}, "node_modules/chai-as-promised": {"version": "7.1.2", "dev": true, "license": "WTFPL", "dependencies": {"check-error": "^1.0.2"}, "peerDependencies": {"chai": ">= 2.1.2 < 6"}}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/chardet": {"version": "0.7.0", "license": "MIT"}, "node_modules/charenc": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/check-error": {"version": "1.0.3", "license": "MIT", "dependencies": {"get-func-name": "^2.0.2"}, "engines": {"node": "*"}}, "node_modules/chokidar": {"version": "3.6.0", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/ci-info": {"version": "2.0.0", "license": "MIT"}, "node_modules/cipher-base": {"version": "1.0.6", "license": "MIT", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/clean-stack": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-boxes": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-table3": {"version": "0.5.1", "dev": true, "license": "MIT", "dependencies": {"object-assign": "^4.1.0", "string-width": "^2.1.1"}, "engines": {"node": ">=6"}, "optionalDependencies": {"colors": "^1.1.2"}}, "node_modules/cli-table3/node_modules/ansi-regex": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/cli-table3/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/cli-table3/node_modules/string-width": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cli-table3/node_modules/strip-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cliui": {"version": "7.0.4", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/colors": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/command-exists": {"version": "1.2.9", "license": "MIT"}, "node_modules/command-line-args": {"version": "5.2.1", "dev": true, "license": "MIT", "dependencies": {"array-back": "^3.1.0", "find-replace": "^3.0.0", "lodash.camelcase": "^4.3.0", "typical": "^4.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/command-line-args/node_modules/typical": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/command-line-usage": {"version": "6.1.3", "dev": true, "license": "MIT", "dependencies": {"array-back": "^4.0.2", "chalk": "^2.4.2", "table-layout": "^1.0.2", "typical": "^5.2.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/command-line-usage/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/command-line-usage/node_modules/array-back": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/command-line-usage/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/command-line-usage/node_modules/color-convert": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/command-line-usage/node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/command-line-usage/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/command-line-usage/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/command-line-usage/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/commander": {"version": "8.3.0", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/compare-versions": {"version": "6.1.1", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "dev": true, "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/concat-stream/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/concat-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/concat-stream/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/cookie": {"version": "0.4.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/core-util-is": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/crc-32": {"version": "1.2.2", "dev": true, "license": "Apache-2.0", "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/create-hash": {"version": "1.2.0", "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/create-hmac": {"version": "1.1.7", "license": "MIT", "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/create-require": {"version": "1.1.1", "devOptional": true, "license": "MIT"}, "node_modules/cross-fetch": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cross-spawn/node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cross-spawn/node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cross-spawn/node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cross-spawn/node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/crypt": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/dataloader": {"version": "1.4.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/death": {"version": "1.1.0", "dev": true}, "node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/deep-eql": {"version": "4.1.4", "license": "MIT", "dependencies": {"type-detect": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/deep-extend": {"version": "0.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/define-data-property": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/detect-indent": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/diff": {"version": "5.2.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/difflib": {"version": "0.2.4", "dev": true, "dependencies": {"heap": ">= 0.2.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dotenv": {"version": "8.6.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/dunder-proto": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/elliptic": {"version": "6.5.4", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/elliptic/node_modules/bn.js": {"version": "4.12.1", "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/encoding": {"version": "0.1.13", "license": "MIT", "optional": true, "peer": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/encoding/node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "optional": true, "peer": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/enquirer": {"version": "2.4.1", "license": "MIT", "dependencies": {"ansi-colors": "^4.1.1", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8.6"}}, "node_modules/env-paths": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/es-define-property": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escodegen": {"version": "1.8.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^2.7.1", "estraverse": "^1.9.1", "esutils": "^2.0.2", "optionator": "^0.8.1"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=0.12.0"}, "optionalDependencies": {"source-map": "~0.2.0"}}, "node_modules/escodegen/node_modules/estraverse": {"version": "1.9.3", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/escodegen/node_modules/levn": {"version": "0.3.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/escodegen/node_modules/optionator": {"version": "0.8.3", "dev": true, "license": "MIT", "dependencies": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/escodegen/node_modules/prelude-ls": {"version": "1.1.2", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/escodegen/node_modules/source-map": {"version": "0.2.0", "dev": true, "optional": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/escodegen/node_modules/type-check": {"version": "0.3.2", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/eslint": {"version": "8.57.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/eslint/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/eslint/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "2.7.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/eth-gas-reporter": {"version": "0.2.27", "dev": true, "license": "MIT", "dependencies": {"@solidity-parser/parser": "^0.14.0", "axios": "^1.5.1", "cli-table3": "^0.5.0", "colors": "1.4.0", "ethereum-cryptography": "^1.0.3", "ethers": "^5.7.2", "fs-readdir-recursive": "^1.1.0", "lodash": "^4.17.14", "markdown-table": "^1.1.3", "mocha": "^10.2.0", "req-cwd": "^2.0.0", "sha1": "^1.1.1", "sync-request": "^6.0.0"}, "peerDependencies": {"@codechecks/client": "^0.1.0"}, "peerDependenciesMeta": {"@codechecks/client": {"optional": true}}}, "node_modules/eth-gas-reporter/node_modules/@ethersproject/solidity": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/eth-gas-reporter/node_modules/@ethersproject/units": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/eth-gas-reporter/node_modules/@ethersproject/wallet": {"version": "5.7.0", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/hdnode": "^5.7.0", "@ethersproject/json-wallets": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}}, "node_modules/eth-gas-reporter/node_modules/@noble/hashes": {"version": "1.2.0", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT"}, "node_modules/eth-gas-reporter/node_modules/@scure/base": {"version": "1.1.9", "dev": true, "license": "MIT", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/eth-gas-reporter/node_modules/@scure/bip32": {"version": "1.1.5", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"@noble/hashes": "~1.2.0", "@noble/secp256k1": "~1.7.0", "@scure/base": "~1.1.0"}}, "node_modules/eth-gas-reporter/node_modules/@scure/bip39": {"version": "1.1.1", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"@noble/hashes": "~1.2.0", "@scure/base": "~1.1.0"}}, "node_modules/eth-gas-reporter/node_modules/ethereum-cryptography": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"@noble/hashes": "1.2.0", "@noble/secp256k1": "1.7.1", "@scure/bip32": "1.1.5", "@scure/bip39": "1.1.1"}}, "node_modules/eth-gas-reporter/node_modules/ethers": {"version": "5.7.2", "dev": true, "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "5.7.0", "@ethersproject/abstract-provider": "5.7.0", "@ethersproject/abstract-signer": "5.7.0", "@ethersproject/address": "5.7.0", "@ethersproject/base64": "5.7.0", "@ethersproject/basex": "5.7.0", "@ethersproject/bignumber": "5.7.0", "@ethersproject/bytes": "5.7.0", "@ethersproject/constants": "5.7.0", "@ethersproject/contracts": "5.7.0", "@ethersproject/hash": "5.7.0", "@ethersproject/hdnode": "5.7.0", "@ethersproject/json-wallets": "5.7.0", "@ethersproject/keccak256": "5.7.0", "@ethersproject/logger": "5.7.0", "@ethersproject/networks": "5.7.1", "@ethersproject/pbkdf2": "5.7.0", "@ethersproject/properties": "5.7.0", "@ethersproject/providers": "5.7.2", "@ethersproject/random": "5.7.0", "@ethersproject/rlp": "5.7.0", "@ethersproject/sha2": "5.7.0", "@ethersproject/signing-key": "5.7.0", "@ethersproject/solidity": "5.7.0", "@ethersproject/strings": "5.7.0", "@ethersproject/transactions": "5.7.0", "@ethersproject/units": "5.7.0", "@ethersproject/wallet": "5.7.0", "@ethersproject/web": "5.7.1", "@ethersproject/wordlists": "5.7.0"}}, "node_modules/ethereum-bloom-filters": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"@noble/hashes": "^1.4.0"}}, "node_modules/ethereum-cryptography": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"@noble/curves": "1.4.2", "@noble/hashes": "1.4.0", "@scure/bip32": "1.4.0", "@scure/bip39": "1.3.0"}}, "node_modules/ethereum-cryptography/node_modules/@noble/curves": {"version": "1.4.2", "dev": true, "license": "MIT", "dependencies": {"@noble/hashes": "1.4.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/ethereum-cryptography/node_modules/@noble/hashes": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/ethereumjs-abi": {"version": "0.6.8", "license": "MIT", "dependencies": {"bn.js": "^4.11.8", "ethereumjs-util": "^6.0.0"}}, "node_modules/ethereumjs-abi/node_modules/@types/bn.js": {"version": "4.11.6", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/ethereumjs-abi/node_modules/bn.js": {"version": "4.12.1", "license": "MIT"}, "node_modules/ethereumjs-abi/node_modules/elliptic": {"version": "6.6.1", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/ethereumjs-abi/node_modules/ethereum-cryptography": {"version": "0.1.3", "license": "MIT", "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}, "node_modules/ethereumjs-abi/node_modules/ethereumjs-util": {"version": "6.2.1", "license": "MPL-2.0", "dependencies": {"@types/bn.js": "^4.11.3", "bn.js": "^4.11.0", "create-hash": "^1.1.2", "elliptic": "^6.5.2", "ethereum-cryptography": "^0.1.3", "ethjs-util": "0.1.6", "rlp": "^2.2.3"}}, "node_modules/ethereumjs-util": {"version": "7.1.5", "license": "MPL-2.0", "dependencies": {"@types/bn.js": "^5.1.0", "bn.js": "^5.1.2", "create-hash": "^1.1.2", "ethereum-cryptography": "^0.1.3", "rlp": "^2.2.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/ethereumjs-util/node_modules/ethereum-cryptography": {"version": "0.1.3", "license": "MIT", "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}, "node_modules/ethers": {"version": "6.13.4", "funding": [{"type": "individual", "url": "https://github.com/sponsors/ethers-io/"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@adraffy/ens-normalize": "1.10.1", "@noble/curves": "1.2.0", "@noble/hashes": "1.3.2", "@types/node": "22.7.5", "aes-js": "4.0.0-beta.5", "tslib": "2.7.0", "ws": "8.17.1"}, "engines": {"node": ">=14.0.0"}}, "node_modules/ethers/node_modules/@noble/curves": {"version": "1.2.0", "license": "MIT", "dependencies": {"@noble/hashes": "1.3.2"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/ethers/node_modules/@noble/hashes": {"version": "1.3.2", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/ethers/node_modules/@types/node": {"version": "22.7.5", "license": "MIT", "dependencies": {"undici-types": "~6.19.2"}}, "node_modules/ethers/node_modules/aes-js": {"version": "4.0.0-beta.5", "license": "MIT"}, "node_modules/ethers/node_modules/tslib": {"version": "2.7.0", "license": "0BSD"}, "node_modules/ethers/node_modules/undici-types": {"version": "6.19.8", "license": "MIT"}, "node_modules/ethers/node_modules/ws": {"version": "8.17.1", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/ethjs-unit": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"bn.js": "4.11.6", "number-to-bn": "1.7.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/ethjs-unit/node_modules/bn.js": {"version": "4.11.6", "dev": true, "license": "MIT"}, "node_modules/ethjs-util": {"version": "0.1.6", "license": "MIT", "dependencies": {"is-hex-prefixed": "1.0.0", "strip-hex-prefix": "1.0.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/eventemitter3": {"version": "5.0.1", "license": "MIT"}, "node_modules/evp_bytestokey": {"version": "1.0.3", "license": "MIT", "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/extendable-error": {"version": "0.1.7", "license": "MIT"}, "node_modules/external-editor": {"version": "3.1.0", "license": "MIT", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "node_modules/fast-base64-decode": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.2", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fast-uri": {"version": "3.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/fastq": {"version": "1.18.0", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fdir": {"version": "6.4.2", "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/fetch-blob": {"version": "3.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-replace": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"array-back": "^3.0.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-up/node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-up/node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-up/node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-yarn-workspace-root": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"micromatch": "^4.0.2"}}, "node_modules/flat": {"version": "5.0.2", "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/flat-cache": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flat-cache/node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/flatted": {"version": "3.3.2", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.3", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/form-data": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "license": "MIT", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/fp-ts": {"version": "1.19.3", "license": "MIT"}, "node_modules/fs-extra": {"version": "7.0.1", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-extra/node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/fs-extra/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/fs-readdir-recursive": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-func-name": {"version": "2.0.2", "license": "MIT", "engines": {"node": "*"}}, "node_modules/get-intrinsic": {"version": "1.2.6", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "dunder-proto": "^1.0.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "function-bind": "^1.1.2", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-port": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ghost-testrpc": {"version": "0.0.2", "dev": true, "license": "ISC", "dependencies": {"chalk": "^2.4.2", "node-emoji": "^1.10.0"}, "bin": {"testrpc-sc": "index.js"}}, "node_modules/ghost-testrpc/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/ghost-testrpc/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/ghost-testrpc/node_modules/color-convert": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/ghost-testrpc/node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/ghost-testrpc/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/ghost-testrpc/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ghost-testrpc/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/global-modules": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"global-prefix": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/global-prefix": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "engines": {"node": ">=6"}}, "node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/handlebars": {"version": "4.7.8", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.5", "neo-async": "^2.6.2", "source-map": "^0.6.1", "wordwrap": "^1.0.0"}, "bin": {"handlebars": "bin/handlebars"}, "engines": {"node": ">=0.4.7"}, "optionalDependencies": {"uglify-js": "^3.1.4"}}, "node_modules/hardhat": {"version": "2.22.17", "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.1.2", "@metamask/eth-sig-util": "^4.0.0", "@nomicfoundation/edr": "^0.6.5", "@nomicfoundation/ethereumjs-common": "4.0.4", "@nomicfoundation/ethereumjs-tx": "5.0.4", "@nomicfoundation/ethereumjs-util": "9.0.4", "@nomicfoundation/solidity-analyzer": "^0.1.0", "@sentry/node": "^5.18.1", "@types/bn.js": "^5.1.0", "@types/lru-cache": "^5.1.0", "adm-zip": "^0.4.16", "aggregate-error": "^3.0.0", "ansi-escapes": "^4.3.0", "boxen": "^5.1.2", "chokidar": "^4.0.0", "ci-info": "^2.0.0", "debug": "^4.1.1", "enquirer": "^2.3.0", "env-paths": "^2.2.0", "ethereum-cryptography": "^1.0.3", "ethereumjs-abi": "^0.6.8", "find-up": "^5.0.0", "fp-ts": "1.19.3", "fs-extra": "^7.0.1", "immutable": "^4.0.0-rc.12", "io-ts": "1.10.4", "json-stream-stringify": "^3.1.4", "keccak": "^3.0.2", "lodash": "^4.17.11", "mnemonist": "^0.38.0", "mocha": "^10.0.0", "p-map": "^4.0.0", "picocolors": "^1.1.0", "raw-body": "^2.4.1", "resolve": "1.17.0", "semver": "^6.3.0", "solc": "0.8.26", "source-map-support": "^0.5.13", "stacktrace-parser": "^0.1.10", "tinyglobby": "^0.2.6", "tsort": "0.0.1", "undici": "^5.14.0", "uuid": "^8.3.2", "ws": "^7.4.6"}, "bin": {"hardhat": "internal/cli/bootstrap.js"}, "peerDependencies": {"ts-node": "*", "typescript": "*"}, "peerDependenciesMeta": {"ts-node": {"optional": true}, "typescript": {"optional": true}}}, "node_modules/hardhat-gas-reporter": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"array-uniq": "1.0.3", "eth-gas-reporter": "^0.2.25", "sha1": "^1.1.1"}, "peerDependencies": {"hardhat": "^2.0.2"}}, "node_modules/hardhat/node_modules/@noble/hashes": {"version": "1.2.0", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT"}, "node_modules/hardhat/node_modules/@scure/base": {"version": "1.1.9", "license": "MIT", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/hardhat/node_modules/@scure/bip32": {"version": "1.1.5", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"@noble/hashes": "~1.2.0", "@noble/secp256k1": "~1.7.0", "@scure/base": "~1.1.0"}}, "node_modules/hardhat/node_modules/@scure/bip39": {"version": "1.1.1", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"@noble/hashes": "~1.2.0", "@scure/base": "~1.1.0"}}, "node_modules/hardhat/node_modules/chokidar": {"version": "4.0.3", "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/hardhat/node_modules/ethereum-cryptography": {"version": "1.2.0", "license": "MIT", "dependencies": {"@noble/hashes": "1.2.0", "@noble/secp256k1": "1.7.1", "@scure/bip32": "1.1.5", "@scure/bip39": "1.1.1"}}, "node_modules/hardhat/node_modules/p-map": {"version": "4.0.0", "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/hardhat/node_modules/readdirp": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">= 14.16.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/hardhat/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/hardhat/node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hash-base": {"version": "3.1.0", "license": "MIT", "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "engines": {"node": ">=4"}}, "node_modules/hash.js": {"version": "1.1.7", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/hasown": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/heap": {"version": "0.2.7", "dev": true, "license": "MIT"}, "node_modules/hmac-drbg": {"version": "1.0.1", "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/http-basic": {"version": "8.1.3", "dev": true, "license": "MIT", "dependencies": {"caseless": "^0.12.0", "concat-stream": "^1.6.2", "http-response-object": "^3.0.1", "parse-cache-control": "^1.0.1"}, "engines": {"node": ">=6.0.0"}}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-response-object": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"@types/node": "^10.0.3"}}, "node_modules/http-response-object/node_modules/@types/node": {"version": "10.17.60", "dev": true, "license": "MIT"}, "node_modules/human-id": {"version": "1.0.2", "license": "MIT"}, "node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immer": {"version": "10.0.2", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/immutable": {"version": "4.3.7", "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "node_modules/interpret": {"version": "1.4.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/io-ts": {"version": "1.10.4", "license": "MIT", "dependencies": {"fp-ts": "^1.0.0"}}, "node_modules/io-ts/node_modules/fp-ts": {"version": "1.19.5", "license": "MIT"}, "node_modules/is-arguments": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-callable": {"version": "1.2.7", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ci": {"version": "2.0.0", "license": "MIT", "dependencies": {"ci-info": "^2.0.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.16.1", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-hex-prefixed": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-subdir": {"version": "1.2.0", "license": "MIT", "dependencies": {"better-path-resolve": "1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/is-typed-array": {"version": "1.1.15", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-windows": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "2.2.0", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isomorphic-unfetch": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"node-fetch": "^2.6.1", "unfetch": "^4.2.0"}}, "node_modules/isomorphic-ws": {"version": "5.0.0", "dev": true, "license": "MIT", "peerDependencies": {"ws": "*"}}, "node_modules/js-cookie": {"version": "2.2.1", "dev": true, "license": "MIT"}, "node_modules/js-sha3": {"version": "0.8.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/js-yaml/node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json-stream-stringify": {"version": "3.1.6", "license": "MIT", "engines": {"node": ">=7.10.1"}}, "node_modules/json-stringify-safe": {"version": "5.0.1", "license": "ISC"}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonschema": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/keccak": {"version": "3.0.4", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-addon-api": "^2.0.0", "node-gyp-build": "^4.2.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kind-of": {"version": "6.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/klaw-sync": {"version": "6.0.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.11"}}, "node_modules/kleur": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.camelcase": {"version": "4.3.0", "dev": true, "license": "MIT"}, "node_modules/lodash.clonedeep": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "dev": true, "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.startcase": {"version": "4.4.0", "license": "MIT"}, "node_modules/lodash.truncate": {"version": "4.4.2", "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/loupe": {"version": "2.3.7", "license": "MIT", "dependencies": {"get-func-name": "^2.0.1"}}, "node_modules/lru_map": {"version": "0.3.3", "license": "MIT"}, "node_modules/make-error": {"version": "1.3.6", "devOptional": true, "license": "ISC"}, "node_modules/markdown-table": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/math-intrinsics": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/md5.js": {"version": "1.3.5", "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/memorystream": {"version": "0.3.1", "engines": {"node": ">= 0.10.0"}}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micro-ftch": {"version": "0.3.1", "dev": true, "license": "MIT"}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/micromatch/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/mime-db": {"version": "1.52.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "license": "MIT"}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimatch/node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp": {"version": "3.0.1", "dev": true, "license": "MIT", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mnemonist": {"version": "0.38.5", "license": "MIT", "dependencies": {"obliterator": "^2.0.0"}}, "node_modules/mocha": {"version": "10.8.2", "license": "MIT", "dependencies": {"ansi-colors": "^4.1.3", "browser-stdout": "^1.3.1", "chokidar": "^3.5.3", "debug": "^4.3.5", "diff": "^5.2.0", "escape-string-regexp": "^4.0.0", "find-up": "^5.0.0", "glob": "^8.1.0", "he": "^1.2.0", "js-yaml": "^4.1.0", "log-symbols": "^4.1.0", "minimatch": "^5.1.6", "ms": "^2.1.3", "serialize-javascript": "^6.0.2", "strip-json-comments": "^3.1.1", "supports-color": "^8.1.1", "workerpool": "^6.5.1", "yargs": "^16.2.0", "yargs-parser": "^20.2.9", "yargs-unparser": "^2.0.0"}, "bin": {"_mocha": "bin/_mocha", "mocha": "bin/mocha.js"}, "engines": {"node": ">= 14.0.0"}}, "node_modules/mocha/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/mocha/node_modules/glob": {"version": "8.1.0", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mocha/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/mocha/node_modules/minimatch": {"version": "5.1.6", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/mock-socket": {"version": "9.3.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/mri": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/ndjson": {"version": "2.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"json-stringify-safe": "^5.0.1", "minimist": "^1.2.5", "readable-stream": "^3.6.0", "split2": "^3.0.0", "through2": "^4.0.0"}, "bin": {"ndjson": "cli.js"}, "engines": {"node": ">=10"}}, "node_modules/neo-async": {"version": "2.6.2", "dev": true, "license": "MIT"}, "node_modules/nice-try": {"version": "1.0.5", "license": "MIT"}, "node_modules/nock": {"version": "13.5.6", "license": "MIT", "dependencies": {"debug": "^4.1.0", "json-stringify-safe": "^5.0.1", "propagate": "^2.0.0"}, "engines": {"node": ">= 10.13"}}, "node_modules/node-addon-api": {"version": "2.0.2", "license": "MIT"}, "node_modules/node-domexception": {"version": "1.0.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "engines": {"node": ">=10.5.0"}}, "node_modules/node-emoji": {"version": "1.11.0", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.21"}}, "node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-gyp-build": {"version": "4.8.4", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/nofilter": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=12.19"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/number-to-bn": {"version": "1.7.0", "dev": true, "license": "MIT", "dependencies": {"bn.js": "4.11.6", "strip-hex-prefix": "1.0.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/number-to-bn/node_modules/bn.js": {"version": "4.11.6", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/obliterator": {"version": "2.0.4", "license": "MIT"}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/open": {"version": "7.4.2", "license": "MIT", "dependencies": {"is-docker": "^2.0.0", "is-wsl": "^2.1.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ordinal": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/os-tmpdir": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/outdent": {"version": "0.5.0", "license": "MIT"}, "node_modules/p-filter": {"version": "2.1.0", "license": "MIT", "dependencies": {"p-map": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/p-filter/node_modules/p-map": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-manager-detector": {"version": "0.2.8", "license": "MIT"}, "node_modules/pako": {"version": "2.1.0", "dev": true, "license": "(MIT AND Zlib)", "optional": true}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-cache-control": {"version": "1.0.1", "dev": true}, "node_modules/patch-package": {"version": "6.5.1", "license": "MIT", "dependencies": {"@yarnpkg/lockfile": "^1.1.0", "chalk": "^4.1.2", "cross-spawn": "^6.0.5", "find-yarn-workspace-root": "^2.0.0", "fs-extra": "^9.0.0", "is-ci": "^2.0.0", "klaw-sync": "^6.0.0", "minimist": "^1.2.6", "open": "^7.4.2", "rimraf": "^2.6.3", "semver": "^5.6.0", "slash": "^2.0.0", "tmp": "^0.0.33", "yaml": "^1.10.2"}, "bin": {"patch-package": "index.js"}, "engines": {"node": ">=10", "npm": ">5"}}, "node_modules/patch-package/node_modules/cross-spawn": {"version": "6.0.6", "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/patch-package/node_modules/fs-extra": {"version": "9.1.0", "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/patch-package/node_modules/semver": {"version": "5.7.2", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/patch-package/node_modules/slash": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pathval": {"version": "1.1.1", "license": "MIT", "engines": {"node": "*"}}, "node_modules/pbkdf2": {"version": "3.1.2", "license": "MIT", "dependencies": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "engines": {"node": ">=0.12"}}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/possible-typed-array-names": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/promise": {"version": "8.3.0", "dev": true, "license": "MIT", "dependencies": {"asap": "~2.0.6"}}, "node_modules/prompts": {"version": "2.4.2", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/propagate": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/proper-lockfile": {"version": "4.1.2", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "retry": "^0.12.0", "signal-exit": "^3.0.2"}}, "node_modules/proper-lockfile/node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/proxy-from-env": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.13.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/randombytes": {"version": "2.1.0", "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/raw-body": {"version": "2.5.2", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/read-yaml-file": {"version": "1.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.5", "js-yaml": "^3.6.1", "pify": "^4.0.1", "strip-bom": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/readdirp/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/rechoir": {"version": "0.6.2", "dev": true, "dependencies": {"resolve": "^1.1.6"}, "engines": {"node": ">= 0.10"}}, "node_modules/rechoir/node_modules/resolve": {"version": "1.22.10", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/recursive-readdir": {"version": "2.2.3", "dev": true, "license": "MIT", "dependencies": {"minimatch": "^3.0.5"}, "engines": {"node": ">=6.0.0"}}, "node_modules/reduce-flatten": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "license": "MIT"}, "node_modules/req-cwd": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"req-from": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/req-from": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/req-from/node_modules/resolve-from": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.17.0", "license": "MIT", "dependencies": {"path-parse": "^1.0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/retry": {"version": "0.12.0", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "2.7.1", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/ripemd160": {"version": "2.0.2", "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/rlp": {"version": "2.2.7", "license": "MPL-2.0", "dependencies": {"bn.js": "^5.2.0"}, "bin": {"rlp": "bin/rlp"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.1", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sc-istanbul": {"version": "0.4.6", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"abbrev": "1.0.x", "async": "1.x", "escodegen": "1.8.x", "esprima": "2.7.x", "glob": "^5.0.15", "handlebars": "^4.0.1", "js-yaml": "3.x", "mkdirp": "0.5.x", "nopt": "3.x", "once": "1.x", "resolve": "1.1.x", "supports-color": "^3.1.0", "which": "^1.1.1", "wordwrap": "^1.0.0"}, "bin": {"istanbul": "lib/cli.js"}}, "node_modules/sc-istanbul/node_modules/abbrev": {"version": "1.0.9", "dev": true, "license": "ISC"}, "node_modules/sc-istanbul/node_modules/glob": {"version": "5.0.15", "dev": true, "license": "ISC", "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/sc-istanbul/node_modules/has-flag": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/sc-istanbul/node_modules/mkdirp": {"version": "0.5.6", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/sc-istanbul/node_modules/nopt": {"version": "3.0.6", "dev": true, "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/sc-istanbul/node_modules/nopt/node_modules/abbrev": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/sc-istanbul/node_modules/resolve": {"version": "1.1.7", "dev": true, "license": "MIT"}, "node_modules/sc-istanbul/node_modules/supports-color": {"version": "3.2.3", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^1.0.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/scale-ts": {"version": "1.6.1", "license": "MIT", "optional": true}, "node_modules/scrypt-js": {"version": "3.0.1", "license": "MIT"}, "node_modules/secp256k1": {"version": "4.0.4", "hasInstallScript": true, "license": "MIT", "dependencies": {"elliptic": "^6.5.7", "node-addon-api": "^5.0.0", "node-gyp-build": "^4.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/secp256k1/node_modules/bn.js": {"version": "4.12.1", "license": "MIT"}, "node_modules/secp256k1/node_modules/elliptic": {"version": "6.6.1", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/secp256k1/node_modules/node-addon-api": {"version": "5.1.0", "license": "MIT"}, "node_modules/semver": {"version": "7.6.3", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/serialize-javascript": {"version": "6.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/set-function-length": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/sha.js": {"version": "2.4.11", "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/sha1": {"version": "1.1.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": ">= 0.0.1", "crypt": ">= 0.0.1"}, "engines": {"node": "*"}}, "node_modules/shebang-command": {"version": "1.2.0", "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-regex": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/shelljs": {"version": "0.8.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}, "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=4"}}, "node_modules/side-channel": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/sisteransi": {"version": "1.0.5", "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/smoldot": {"version": "2.0.22", "dev": true, "license": "GPL-3.0-or-later WITH Classpath-exception-2.0", "optional": true, "dependencies": {"ws": "^8.8.1"}}, "node_modules/solc": {"version": "0.8.26", "license": "MIT", "dependencies": {"command-exists": "^1.2.8", "commander": "^8.1.0", "follow-redirects": "^1.12.1", "js-sha3": "0.8.0", "memorystream": "^0.3.1", "semver": "^5.5.0", "tmp": "0.0.33"}, "bin": {"solcjs": "solc.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/solc/node_modules/semver": {"version": "5.7.2", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/solidity-ast": {"version": "0.4.59", "license": "MIT"}, "node_modules/solidity-coverage": {"version": "0.8.14", "dev": true, "license": "ISC", "dependencies": {"@ethersproject/abi": "^5.0.9", "@solidity-parser/parser": "^0.19.0", "chalk": "^2.4.2", "death": "^1.1.0", "difflib": "^0.2.4", "fs-extra": "^8.1.0", "ghost-testrpc": "^0.0.2", "global-modules": "^2.0.0", "globby": "^10.0.1", "jsonschema": "^1.2.4", "lodash": "^4.17.21", "mocha": "^10.2.0", "node-emoji": "^1.10.0", "pify": "^4.0.1", "recursive-readdir": "^2.2.2", "sc-istanbul": "^0.4.5", "semver": "^7.3.4", "shelljs": "^0.8.3", "web3-utils": "^1.3.6"}, "bin": {"solidity-coverage": "plugins/bin.js"}, "peerDependencies": {"hardhat": "^2.11.0"}}, "node_modules/solidity-coverage/node_modules/@solidity-parser/parser": {"version": "0.19.0", "dev": true, "license": "MIT"}, "node_modules/solidity-coverage/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/solidity-coverage/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/solidity-coverage/node_modules/color-convert": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/solidity-coverage/node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/solidity-coverage/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/solidity-coverage/node_modules/fs-extra": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/solidity-coverage/node_modules/globby": {"version": "10.0.2", "dev": true, "license": "MIT", "dependencies": {"@types/glob": "^7.1.1", "array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.0.3", "glob": "^7.1.3", "ignore": "^5.1.1", "merge2": "^1.2.3", "slash": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/solidity-coverage/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/solidity-coverage/node_modules/jsonfile": {"version": "4.0.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/solidity-coverage/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/solidity-coverage/node_modules/universalify": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/solidity-coverage/node_modules/web3-utils": {"version": "1.10.4", "dev": true, "license": "LGPL-3.0", "dependencies": {"@ethereumjs/util": "^8.1.0", "bn.js": "^5.2.1", "ethereum-bloom-filters": "^1.0.6", "ethereum-cryptography": "^2.1.2", "ethjs-unit": "0.1.6", "number-to-bn": "1.7.0", "randombytes": "^2.1.0", "utf8": "3.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/spawndamnit": {"version": "3.0.1", "license": "SEE LICENSE IN LICENSE", "dependencies": {"cross-spawn": "^7.0.5", "signal-exit": "^4.0.1"}}, "node_modules/split2": {"version": "3.2.2", "license": "ISC", "dependencies": {"readable-stream": "^3.0.0"}}, "node_modules/stacktrace-parser": {"version": "0.1.10", "license": "MIT", "dependencies": {"type-fest": "^0.7.1"}, "engines": {"node": ">=6"}}, "node_modules/stacktrace-parser/node_modules/type-fest": {"version": "0.7.1", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/store": {"version": "2.0.12", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-format": {"version": "2.0.0", "dev": true, "license": "WTFPL OR MIT"}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-hex-prefix": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-hex-prefixed": "1.0.0"}, "engines": {"node": ">=6.5.0", "npm": ">=3"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sync-request": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"http-response-object": "^3.0.1", "sync-rpc": "^1.2.1", "then-request": "^6.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/sync-rpc": {"version": "1.3.6", "dev": true, "license": "MIT", "dependencies": {"get-port": "^3.1.0"}}, "node_modules/table": {"version": "6.9.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/table-layout": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"array-back": "^4.0.1", "deep-extend": "~0.6.0", "typical": "^5.2.0", "wordwrapjs": "^4.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/table-layout/node_modules/array-back": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/table/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/table/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/term-size": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/then-request": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"@types/concat-stream": "^1.6.0", "@types/form-data": "0.0.33", "@types/node": "^8.0.0", "@types/qs": "^6.2.31", "caseless": "~0.12.0", "concat-stream": "^1.6.0", "form-data": "^2.2.0", "http-basic": "^8.1.1", "http-response-object": "^3.0.1", "promise": "^8.0.0", "qs": "^6.4.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/then-request/node_modules/@types/node": {"version": "8.10.66", "dev": true, "license": "MIT"}, "node_modules/then-request/node_modules/form-data": {"version": "2.5.2", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.12"}}, "node_modules/through2": {"version": "4.0.2", "license": "MIT", "dependencies": {"readable-stream": "3"}}, "node_modules/tinyglobby": {"version": "0.2.10", "license": "MIT", "dependencies": {"fdir": "^6.4.2", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}}, "node_modules/tmp": {"version": "0.0.33", "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/ts-command-line-args": {"version": "2.5.1", "dev": true, "license": "ISC", "dependencies": {"chalk": "^4.1.0", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "string-format": "^2.0.0"}, "bin": {"write-markdown": "dist/write-markdown.js"}}, "node_modules/ts-essentials": {"version": "7.0.3", "dev": true, "license": "MIT", "peerDependencies": {"typescript": ">=3.7.0"}}, "node_modules/ts-node": {"version": "10.9.2", "devOptional": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/ts-node/node_modules/diff": {"version": "4.0.2", "devOptional": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/tsort": {"version": "0.0.1", "license": "MIT"}, "node_modules/tweetnacl": {"version": "1.0.3", "license": "Unlicense"}, "node_modules/tweetnacl-util": {"version": "0.15.1", "license": "Unlicense"}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typechain": {"version": "8.3.2", "dev": true, "license": "MIT", "dependencies": {"@types/prettier": "^2.1.1", "debug": "^4.3.1", "fs-extra": "^7.0.0", "glob": "7.1.7", "js-sha3": "^0.8.0", "lodash": "^4.17.15", "mkdirp": "^1.0.4", "prettier": "^2.3.1", "ts-command-line-args": "^2.2.0", "ts-essentials": "^7.0.1"}, "bin": {"typechain": "dist/cli/cli.js"}, "peerDependencies": {"typescript": ">=4.3.0"}}, "node_modules/typechain/node_modules/glob": {"version": "7.1.7", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/typechain/node_modules/mkdirp": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/typedarray": {"version": "0.0.6", "dev": true, "license": "MIT"}, "node_modules/typescript": {"version": "5.7.2", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typical": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/uglify-js": {"version": "3.19.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "optional": true, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/undici": {"version": "5.28.4", "license": "MIT", "dependencies": {"@fastify/busboy": "^2.0.0"}, "engines": {"node": ">=14.0"}}, "node_modules/undici-types": {"version": "6.20.0", "license": "MIT"}, "node_modules/unfetch": {"version": "4.2.0", "dev": true, "license": "MIT"}, "node_modules/universalify": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/utf8": {"version": "3.0.0", "dev": true, "license": "MIT"}, "node_modules/util": {"version": "0.12.5", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "devOptional": true, "license": "MIT"}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/web3": {"version": "4.16.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.7.1", "web3-errors": "^1.3.1", "web3-eth": "^4.11.1", "web3-eth-abi": "^4.4.1", "web3-eth-accounts": "^4.3.1", "web3-eth-contract": "^4.7.2", "web3-eth-ens": "^4.4.0", "web3-eth-iban": "^4.0.7", "web3-eth-personal": "^4.1.0", "web3-net": "^4.1.0", "web3-providers-http": "^4.2.0", "web3-providers-ws": "^4.0.8", "web3-rpc-methods": "^1.3.0", "web3-rpc-providers": "^1.0.0-rc.4", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14.0.0", "npm": ">=6.12.0"}}, "node_modules/web3-core": {"version": "4.7.1", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-errors": "^1.3.1", "web3-eth-accounts": "^4.3.1", "web3-eth-iban": "^4.0.7", "web3-providers-http": "^4.2.0", "web3-providers-ws": "^4.0.8", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}, "optionalDependencies": {"web3-providers-ipc": "^4.0.7"}}, "node_modules/web3-errors": {"version": "1.3.1", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-types": "^1.10.0"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth": {"version": "4.11.1", "dev": true, "license": "LGPL-3.0", "dependencies": {"setimmediate": "^1.0.5", "web3-core": "^4.7.1", "web3-errors": "^1.3.1", "web3-eth-abi": "^4.4.1", "web3-eth-accounts": "^4.3.1", "web3-net": "^4.1.0", "web3-providers-ws": "^4.0.8", "web3-rpc-methods": "^1.3.0", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-abi": {"version": "4.4.1", "dev": true, "license": "LGPL-3.0", "dependencies": {"abitype": "0.7.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-accounts": {"version": "4.3.1", "dev": true, "license": "LGPL-3.0", "dependencies": {"@ethereumjs/rlp": "^4.0.1", "crc-32": "^1.2.2", "ethereum-cryptography": "^2.0.0", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-contract": {"version": "4.7.2", "dev": true, "license": "LGPL-3.0", "dependencies": {"@ethereumjs/rlp": "^5.0.2", "web3-core": "^4.7.1", "web3-errors": "^1.3.1", "web3-eth": "^4.11.1", "web3-eth-abi": "^4.4.1", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-contract/node_modules/@ethereumjs/rlp": {"version": "5.0.2", "dev": true, "license": "MPL-2.0", "bin": {"rlp": "bin/rlp.cjs"}, "engines": {"node": ">=18"}}, "node_modules/web3-eth-ens": {"version": "4.4.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"@adraffy/ens-normalize": "^1.8.8", "web3-core": "^4.5.0", "web3-errors": "^1.2.0", "web3-eth": "^4.8.0", "web3-eth-contract": "^4.5.0", "web3-net": "^4.1.0", "web3-types": "^1.7.0", "web3-utils": "^4.3.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-ens/node_modules/@adraffy/ens-normalize": {"version": "1.11.0", "dev": true, "license": "MIT"}, "node_modules/web3-eth-iban": {"version": "4.0.7", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-errors": "^1.1.3", "web3-types": "^1.3.0", "web3-utils": "^4.0.7", "web3-validator": "^2.0.3"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-eth-personal": {"version": "4.1.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.6.0", "web3-eth": "^4.9.0", "web3-rpc-methods": "^1.3.0", "web3-types": "^1.8.0", "web3-utils": "^4.3.1", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-net": {"version": "4.1.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.4.0", "web3-rpc-methods": "^1.3.0", "web3-types": "^1.6.0", "web3-utils": "^4.3.0"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-http": {"version": "4.2.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"cross-fetch": "^4.0.0", "web3-errors": "^1.3.0", "web3-types": "^1.7.0", "web3-utils": "^4.3.1"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-ipc": {"version": "4.0.7", "dev": true, "license": "LGPL-3.0", "optional": true, "dependencies": {"web3-errors": "^1.1.3", "web3-types": "^1.3.0", "web3-utils": "^4.0.7"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-providers-ws": {"version": "4.0.8", "dev": true, "license": "LGPL-3.0", "dependencies": {"@types/ws": "8.5.3", "isomorphic-ws": "^5.0.0", "web3-errors": "^1.2.0", "web3-types": "^1.7.0", "web3-utils": "^4.3.1", "ws": "^8.17.1"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-rpc-methods": {"version": "1.3.0", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-core": "^4.4.0", "web3-types": "^1.6.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-rpc-providers": {"version": "1.0.0-rc.4", "dev": true, "license": "LGPL-3.0", "dependencies": {"web3-errors": "^1.3.1", "web3-providers-http": "^4.2.0", "web3-providers-ws": "^4.0.8", "web3-types": "^1.10.0", "web3-utils": "^4.3.3", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-types": {"version": "1.10.0", "dev": true, "license": "LGPL-3.0", "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-utils": {"version": "4.3.3", "dev": true, "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "eventemitter3": "^5.0.1", "web3-errors": "^1.3.1", "web3-types": "^1.10.0", "web3-validator": "^2.0.6"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/web3-validator": {"version": "2.0.6", "dev": true, "license": "LGPL-3.0", "dependencies": {"ethereum-cryptography": "^2.0.0", "util": "^0.12.5", "web3-errors": "^1.2.0", "web3-types": "^1.6.0", "zod": "^3.21.4"}, "engines": {"node": ">=14", "npm": ">=6.12.0"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "1.3.1", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-typed-array": {"version": "1.1.18", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.3", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/widest-line": {"version": "3.1.0", "license": "MIT", "dependencies": {"string-width": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wordwrap": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/wordwrapjs": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"reduce-flatten": "^2.0.0", "typical": "^5.2.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/workerpool": {"version": "6.5.1", "license": "Apache-2.0"}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/ws": {"version": "8.18.0", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yaml": {"version": "1.10.2", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yargs": {"version": "16.2.0", "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/yargs-parser": {"version": "20.2.9", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs-unparser": {"version": "2.0.0", "license": "MIT", "dependencies": {"camelcase": "^6.0.0", "decamelize": "^4.0.0", "flat": "^5.0.2", "is-plain-obj": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/yn": {"version": "3.1.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zod": {"version": "3.24.1", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}}}