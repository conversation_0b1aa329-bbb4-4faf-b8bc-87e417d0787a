{"manifestVersion": "3.2", "proxies": [{"address": "0x71072eED8b969073daD554BeE5d39D52bA404901", "txHash": "0x0be96f15d5c446f2789fd14e0191856218435fc72b9c7bcd056d371c17a8f584", "kind": "uups"}, {"address": "0x373fc25db85570F240cAe7661E49AA6605992c40", "txHash": "0x20cf349c9ec776eef0438ee78017420d0c5011cdbefa40bac4adff6336e71512", "kind": "uups"}, {"address": "0x17F81d955D1977964ed8C5A748e62fc829d898Dd", "txHash": "0xe43cae061765fce9f2e02c0c70747ed68cfe7f0d677e17684da3ee9bc4b1da18", "kind": "uups"}, {"address": "0xFd3F71338f422B518e9eb6A76fF0D32093cD5fc8", "txHash": "0xc28a4931e42b87ace5e7f786c6fdf1120cd0e5dd02a2ae2efa9fc5948f3112fd", "kind": "uups"}, {"address": "0xE91214431dBF3279c27062611dF162c16Fd35230", "txHash": "0x396158df2d7309f99f0599ba38ed84b0d43569152228375b738e74699b6106d1", "kind": "uups"}, {"address": "0x2eCcDe2765223B84e4BB4c901e27AA1e60A5c133", "txHash": "0xc668cb27418361889375dbdf846c818cade511d914a852bb6443a23d37566e83", "kind": "uups"}, {"address": "0xed1211C59554c301FBaA2F4ebBD9DF91a21F7E47", "txHash": "0xf5cec01f161f500777f0b4c8c9353c2f75761d2a1ab8556fe598e288be1726cb", "kind": "uups"}, {"address": "0x00886BC1e8c096E0B6Fa12Bb2E1539D5934CD2DA", "txHash": "0xd3dd5c79ce003c9343dbcafc1cc4b0d3aaa67c5552fa0bba92a565040c3d21ca", "kind": "uups"}, {"address": "0x7E8C63b3b07a3D8687af9EB5B496192B0d6F1578", "txHash": "0x42c6d724ca946626eb4a05661b8c27f775e946968baf9c89aa8438d69b8904da", "kind": "uups"}, {"address": "0xb35e004ab0AcA69397E8d09dD755736dc5419C15", "txHash": "0xc75bddd6c378103e0534255bc8ce23c1cc458967f1f110e6df9d4b1cf9f733f6", "kind": "uups"}, {"address": "0xF12DB2c20d92cC18eD9922b03b55ECC352b9013c", "txHash": "0x5d8a6789c1c7d3005f9983a2557c2c957a1d25d3fbb412659d064a6b9d131356", "kind": "uups"}, {"address": "0x3A21d01BE82ECED19e00e4BAf9485C83a90E2186", "txHash": "0xd11cb3183233655d7d0c95605a70cd5e5e9aa4cf3b11201177d1c5a064513d19", "kind": "uups"}, {"address": "0xAF45F3887044CB57Be15d6C0e92823481E09837b", "txHash": "0x9202e60309baef0b62c383061a7bb25ab79574559be33cb36c3cb3c0f4f7de82", "kind": "uups"}, {"address": "0xc854A41F5A0956CA09C2704efBb0f266a2Ba48F3", "txHash": "0x97f1d9f2625772e0d903d7b5730adb099c839e1bf0b9717b3c7417ab6f45dc4a", "kind": "uups"}], "impls": {"0f1ed185b9859d70b982d1e6a2a3ab3e06403f5e26baf7508d968755c28f90d0": {"address": "0xFE44c9A33E243994cB94CFFC3A76F62785c463ea", "txHash": "0xacb3198f41e139b613587bf6ba122d018135f41cbc58d3c7fe82b4a5ae2ca998", "layout": {"solcVersion": "0.8.19", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:40"}, {"label": "_balances", "offset": 0, "slot": "51", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40"}, {"label": "_allowances", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:42"}, {"label": "_totalSupply", "offset": 0, "slot": "53", "type": "t_uint256", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:44"}, {"label": "_name", "offset": 0, "slot": "54", "type": "t_string_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:46"}, {"label": "_symbol", "offset": 0, "slot": "55", "type": "t_string_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:47"}, {"label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)45_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:376"}, {"label": "_owner", "offset": 0, "slot": "101", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_hashedName", "offset": 0, "slot": "151", "type": "t_bytes32", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:40", "renamedFrom": "_HASHED_NAME"}, {"label": "_hashedVersion", "offset": 0, "slot": "152", "type": "t_bytes32", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:42", "renamedFrom": "_HASHED_VERSION"}, {"label": "_name", "offset": 0, "slot": "153", "type": "t_string_storage", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:44"}, {"label": "_version", "offset": 0, "slot": "154", "type": "t_string_storage", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:45"}, {"label": "__gap", "offset": 0, "slot": "155", "type": "t_array(t_uint256)48_storage", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:204"}, {"label": "_nonces", "offset": 0, "slot": "203", "type": "t_mapping(t_address,t_struct(Counter)2160_storage)", "contract": "ERC20PermitUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\ERC20PermitUpgradeable.sol:28"}, {"label": "_PERMIT_TYPEHASH_DEPRECATED_SLOT", "offset": 0, "slot": "204", "type": "t_bytes32", "contract": "ERC20PermitUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\ERC20PermitUpgradeable.sol:40", "renamedFrom": "_PERMIT_TYPEHASH"}, {"label": "__gap", "offset": 0, "slot": "205", "type": "t_array(t_uint256)49_storage", "contract": "ERC20PermitUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\ERC20PermitUpgradeable.sol:108"}, {"label": "__gap", "offset": 0, "slot": "254", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "304", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "bridgeOperators", "offset": 0, "slot": "354", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:12"}, {"label": "poolContracts", "offset": 0, "slot": "355", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:13"}, {"label": "proofContracts", "offset": 0, "slot": "356", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:14"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)45_storage": {"label": "uint256[45]", "numberOfBytes": "1440"}, "t_array(t_uint256)48_storage": {"label": "uint256[48]", "numberOfBytes": "1536"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(Counter)2160_storage)": {"label": "mapping(address => struct CountersUpgradeable.Counter)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Counter)2160_storage": {"label": "struct CountersUpgradeable.Counter", "members": [{"label": "_value", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {}}}, "2006da8464b4c5b9ff4ccc42267366ae48da371ccd774d2b8ee575186177396d": {"address": "0x921f5faCb71d5E6eB3c5CdBa53Ff2ED7Ea6300fa", "txHash": "0x5190d422e2faacf59378843516c9aa1cf7b7e2fc1e5dc339b2785f459675f505", "layout": {"solcVersion": "0.8.19", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:40"}, {"label": "_owner", "offset": 0, "slot": "51", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "52", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "__gap", "offset": 0, "slot": "101", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "151", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "token", "offset": 0, "slot": "201", "type": "t_contract(StorageToken)5686", "contract": "StoragePool", "src": "contracts\\StoragePool.sol:10"}, {"label": "pools", "offset": 0, "slot": "202", "type": "t_mapping(t_uint256,t_struct(Pool)6040_storage)", "contract": "StoragePool", "src": "contracts\\StoragePool.sol:11"}, {"label": "joinRequests", "offset": 0, "slot": "203", "type": "t_mapping(t_uint256,t_array(t_struct(JoinRequest)6067_storage)dyn_storage)", "contract": "StoragePool", "src": "contracts\\StoragePool.sol:12"}, {"label": "lockedTokens", "offset": 0, "slot": "204", "type": "t_mapping(t_address,t_uint256)", "contract": "StoragePool", "src": "contracts\\StoragePool.sol:13"}, {"label": "poolCounter", "offset": 0, "slot": "205", "type": "t_uint256", "contract": "StoragePool", "src": "contracts\\StoragePool.sol:14"}, {"label": "poolCreationTokens", "offset": 0, "slot": "206", "type": "t_uint256", "contract": "StoragePool", "src": "contracts\\StoragePool.sol:15"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_struct(JoinRequest)6067_storage)dyn_storage": {"label": "struct IStoragePool.JoinRequest[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_contract(StorageToken)5686": {"label": "contract StorageToken", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(Member)6049_storage)": {"label": "mapping(address => struct IStoragePool.Member)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_array(t_struct(JoinRequest)6067_storage)dyn_storage)": {"label": "mapping(uint256 => struct IStoragePool.JoinRequest[])", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(Pool)6040_storage)": {"label": "mapping(uint256 => struct IStoragePool.Pool)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Criteria)6052_storage": {"label": "struct IStoragePool.Criteria", "members": [{"label": "minPingTime", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(JoinRequest)6067_storage": {"label": "struct IStoragePool.JoinRequest", "members": [{"label": "peerId", "type": "t_string_storage", "offset": 0, "slot": "0"}, {"label": "accountId", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "poolId", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "votes", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "3"}, {"label": "approvals", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "rejections", "type": "t_uint256", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_struct(Member)6049_storage": {"label": "struct IStoragePool.Member", "members": [{"label": "joinDate", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "peerId", "type": "t_string_storage", "offset": 0, "slot": "1"}, {"label": "accountId", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "reputationScore", "type": "t_uint256", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(Pool)6040_storage": {"label": "struct IStoragePool.Pool", "members": [{"label": "name", "type": "t_string_storage", "offset": 0, "slot": "0"}, {"label": "id", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "region", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "creator", "type": "t_address", "offset": 0, "slot": "3"}, {"label": "members", "type": "t_mapping(t_address,t_struct(Member)6049_storage)", "offset": 0, "slot": "4"}, {"label": "memberList", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "5"}, {"label": "criteria", "type": "t_struct(Criteria)6052_storage", "offset": 0, "slot": "6"}, {"label": "requiredTokens", "type": "t_uint256", "offset": 0, "slot": "7"}], "numberOfBytes": "256"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {}}}, "7d264e497c1f2812ee4964cee6d609de04f987c51b1ca930c20216b2f41f03e2": {"address": "0x52A3cf73f0c3C207aD04e2861da5b585992B9e07", "txHash": "0xb830a333d518d6fe59ab72136b05238956a6281b28073bd3b90dbedb46b72524", "layout": {"solcVersion": "0.8.19", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:40"}, {"label": "_owner", "offset": 0, "slot": "51", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "52", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "__gap", "offset": 0, "slot": "101", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "151", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "token", "offset": 0, "slot": "201", "type": "t_contract(StorageToken)5686", "contract": "StorageProof", "src": "contracts\\StorageProof.sol:10"}, {"label": "proofs", "offset": 0, "slot": "202", "type": "t_mapping(t_uint256,t_mapping(t_string_memory_ptr,t_struct(Proof)6143_storage))", "contract": "StorageProof", "src": "contracts\\StorageProof.sol:11"}, {"label": "uploads", "offset": 0, "slot": "203", "type": "t_mapping(t_address,t_mapping(t_string_memory_ptr,t_struct(UploadRequest)6157_storage))", "contract": "StorageProof", "src": "contracts\\StorageProof.sol:12"}, {"label": "removals", "offset": 0, "slot": "204", "type": "t_mapping(t_string_memory_ptr,t_struct(RemovalRequest)6167_storage)", "contract": "StorageProof", "src": "contracts\\StorageProof.sol:13"}, {"label": "storageCostPerTBYear", "offset": 0, "slot": "205", "type": "t_uint256", "contract": "StorageProof", "src": "contracts\\StorageProof.sol:14"}, {"label": "miningRewardPerDay", "offset": 0, "slot": "206", "type": "t_uint256", "contract": "StorageProof", "src": "contracts\\StorageProof.sol:15"}, {"label": "lastRewardDistribution", "offset": 0, "slot": "207", "type": "t_uint256", "contract": "StorageProof", "src": "contracts\\StorageProof.sol:16"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_string_storage)dyn_storage": {"label": "string[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_contract(StorageToken)5686": {"label": "contract StorageToken", "numberOfBytes": "20"}, "t_mapping(t_address,t_mapping(t_string_memory_ptr,t_struct(UploadRequest)6157_storage))": {"label": "mapping(address => mapping(string => struct IStorageProof.UploadRequest))", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_struct(Proof)6143_storage)": {"label": "mapping(string => struct IStorageProof.Proof)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_struct(RemovalRequest)6167_storage)": {"label": "mapping(string => struct IStorageProof.RemovalRequest)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_struct(UploadRequest)6157_storage)": {"label": "mapping(string => struct IStorageProof.UploadRequest)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_string_memory_ptr,t_struct(Proof)6143_storage))": {"label": "mapping(uint256 => mapping(string => struct IStorageProof.Proof))", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Proof)6143_storage": {"label": "struct IStorageProof.Proof", "members": [{"label": "cid", "type": "t_string_storage", "offset": 0, "slot": "0"}, {"label": "timestamp", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "storer", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "poolId", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "replicationCount", "type": "t_uint256", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(RemovalRequest)6167_storage": {"label": "struct IStorageProof.RemovalRequest", "members": [{"label": "cids", "type": "t_array(t_string_storage)dyn_storage", "offset": 0, "slot": "0"}, {"label": "uploader", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "poolId", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "timestamp", "type": "t_uint256", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(UploadRequest)6157_storage": {"label": "struct IStorageProof.UploadRequest", "members": [{"label": "cids", "type": "t_array(t_string_storage)dyn_storage", "offset": 0, "slot": "0"}, {"label": "replicationFactor", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "poolId", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "uploader", "type": "t_address", "offset": 0, "slot": "3"}, {"label": "timestamp", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "currentReplications", "type": "t_uint256", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {}}}, "279fb26b61d6e41e90b7d61e533f48311fc261c86df41a1301606896cf2527e5": {"address": "0x4b6349C2e2787d5Fb7b200D06F91d8f445DBCd48", "txHash": "0x61236046d8f91f1a59238e83a3c1a05329677b7f10397ebffbb7c2654fa71247", "layout": {"solcVersion": "0.8.19", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:40"}, {"label": "_balances", "offset": 0, "slot": "51", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40"}, {"label": "_allowances", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:42"}, {"label": "_totalSupply", "offset": 0, "slot": "53", "type": "t_uint256", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:44"}, {"label": "_name", "offset": 0, "slot": "54", "type": "t_string_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:46"}, {"label": "_symbol", "offset": 0, "slot": "55", "type": "t_string_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:47"}, {"label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)45_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:376"}, {"label": "_owner", "offset": 0, "slot": "101", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_hashedName", "offset": 0, "slot": "151", "type": "t_bytes32", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:40", "renamedFrom": "_HASHED_NAME"}, {"label": "_hashedVersion", "offset": 0, "slot": "152", "type": "t_bytes32", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:42", "renamedFrom": "_HASHED_VERSION"}, {"label": "_name", "offset": 0, "slot": "153", "type": "t_string_storage", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:44"}, {"label": "_version", "offset": 0, "slot": "154", "type": "t_string_storage", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:45"}, {"label": "__gap", "offset": 0, "slot": "155", "type": "t_array(t_uint256)48_storage", "contract": "EIP712Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:204"}, {"label": "_nonces", "offset": 0, "slot": "203", "type": "t_mapping(t_address,t_struct(Counter)2160_storage)", "contract": "ERC20PermitUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\ERC20PermitUpgradeable.sol:28"}, {"label": "_PERMIT_TYPEHASH_DEPRECATED_SLOT", "offset": 0, "slot": "204", "type": "t_bytes32", "contract": "ERC20PermitUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\ERC20PermitUpgradeable.sol:40", "renamedFrom": "_PERMIT_TYPEHASH"}, {"label": "__gap", "offset": 0, "slot": "205", "type": "t_array(t_uint256)49_storage", "contract": "ERC20PermitUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\ERC20PermitUpgradeable.sol:108"}, {"label": "__gap", "offset": 0, "slot": "254", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "304", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "bridgeOperators", "offset": 0, "slot": "354", "type": "t_mapping(t_address,t_bool)", "contract": "StorageTokenV1", "src": "contracts\\StorageTokenV1.sol:13"}, {"label": "poolContracts", "offset": 0, "slot": "355", "type": "t_mapping(t_address,t_bool)", "contract": "StorageTokenV1", "src": "contracts\\StorageTokenV1.sol:14"}, {"label": "proofContracts", "offset": 0, "slot": "356", "type": "t_mapping(t_address,t_bool)", "contract": "StorageTokenV1", "src": "contracts\\StorageTokenV1.sol:15"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)45_storage": {"label": "uint256[45]", "numberOfBytes": "1440"}, "t_array(t_uint256)48_storage": {"label": "uint256[48]", "numberOfBytes": "1536"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(Counter)2160_storage)": {"label": "mapping(address => struct CountersUpgradeable.Counter)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Counter)2160_storage": {"label": "struct CountersUpgradeable.Counter", "members": [{"label": "_value", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {}}}, "5c632939ff6f778c1ac021020e226e759289c8a10877f145023136ceb777893b": {"address": "0xC85a3855F3933dbebb4116c9b9fc3826402Ec602", "txHash": "0x4cfd4c99873dc1acad5057f63f99c188f167011c123d7c21a1708632e346acc7", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "pools", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_struct(LiquidityPool)8584_storage)", "contract": "DAMMModule", "src": "contracts\\DAMMModule.sol:31"}, {"label": "priceHistory", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)", "contract": "DAMMModule", "src": "contracts\\DAMMModule.sol:32"}, {"label": "__gap", "offset": 0, "slot": "2", "type": "t_array(t_uint256)50_storage", "contract": "DAMMModule", "src": "contracts\\DAMMModule.sol:195"}, {"label": "bridgeOperators", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:17"}, {"label": "poolContracts", "offset": 0, "slot": "53", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:18"}, {"label": "proofContracts", "offset": 0, "slot": "54", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:19"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "55", "type": "t_mapping(t_uint256,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:20"}, {"label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)50_storage", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:207"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)70_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)80_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)70_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(EIP712Storage)853_storage": {"label": "struct EIP712Upgradeable.EIP712Storage", "members": [{"label": "_hashedName", "type": "t_bytes32", "offset": 0, "slot": "0"}, {"label": "_hashedVersion", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "_version", "type": "t_string_storage", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(ERC20Storage)401_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)257_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(NoncesStorage)672_storage": {"label": "struct NoncesUpgradeable.NoncesStorage", "members": [{"label": "_nonces", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)197_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)726_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)790_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)70_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_mapping(t_address,t_array(t_uint256)dyn_storage)": {"label": "mapping(address => uint256[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(LiquidityPool)8584_storage)": {"label": "mapping(address => struct DAMMModule.LiquidityPool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_struct(LiquidityPool)8584_storage": {"label": "struct DAMMModule.LiquidityPool", "members": [{"label": "baseReserve", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "quote<PERSON><PERSON><PERSON>", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "lastPrice", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "volatility", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "concentrationFactor", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "baseVolume24h", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "lastUpdateTime", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "priceFeed", "type": "t_address", "offset": 0, "slot": "7"}, {"label": "isActive", "type": "t_bool", "offset": 20, "slot": "7"}], "numberOfBytes": "256"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)70_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Nonces": [{"contract": "NoncesUpgradeable", "label": "_nonces", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\utils\\NoncesUpgradeable.sol:17", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.EIP712": [{"contract": "EIP712Upgradeable", "label": "_hashedName", "type": "t_bytes32", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:39", "offset": 0, "slot": "0"}, {"contract": "EIP712Upgradeable", "label": "_hashedVersion", "type": "t_bytes32", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:41", "offset": 0, "slot": "1"}, {"contract": "EIP712Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:43", "offset": 0, "slot": "2"}, {"contract": "EIP712Upgradeable", "label": "_version", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:44", "offset": 0, "slot": "3"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "41079de4d60a7f735c8a3d9d6300d386566dc14801b394e4c040946216ad9174": {"address": "0x68990131bAFD84742118A088a53EB4b271c94433", "txHash": "0x8be54ce2e2ab8c184dbbb005006fb9d6b1d0fcc9e478d81070a42dba19ff045f", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "pools", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_struct(LiquidityPool)8148_storage)", "contract": "DAMMModule", "src": "contracts\\DAMMModule.sol:31"}, {"label": "priceHistory", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)", "contract": "DAMMModule", "src": "contracts\\DAMMModule.sol:32"}, {"label": "__gap", "offset": 0, "slot": "2", "type": "t_array(t_uint256)50_storage", "contract": "DAMMModule", "src": "contracts\\DAMMModule.sol:229"}, {"label": "bridgeOperators", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:17"}, {"label": "poolContracts", "offset": 0, "slot": "53", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:18"}, {"label": "proofContracts", "offset": 0, "slot": "54", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:19"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "55", "type": "t_mapping(t_uint256,t_bool)", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:20"}, {"label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)50_storage", "contract": "StorageToken", "src": "contracts\\StorageToken.sol:207"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)70_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)80_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)70_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(EIP712Storage)853_storage": {"label": "struct EIP712Upgradeable.EIP712Storage", "members": [{"label": "_hashedName", "type": "t_bytes32", "offset": 0, "slot": "0"}, {"label": "_hashedVersion", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "_version", "type": "t_string_storage", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(ERC20Storage)401_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)257_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(NoncesStorage)672_storage": {"label": "struct NoncesUpgradeable.NoncesStorage", "members": [{"label": "_nonces", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)197_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)726_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)790_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)70_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_mapping(t_address,t_array(t_uint256)dyn_storage)": {"label": "mapping(address => uint256[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(LiquidityPool)8148_storage)": {"label": "mapping(address => struct DAMMModule.LiquidityPool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_struct(LiquidityPool)8148_storage": {"label": "struct DAMMModule.LiquidityPool", "members": [{"label": "baseReserve", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "quote<PERSON><PERSON><PERSON>", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "lastPrice", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "volatility", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "concentrationFactor", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "baseVolume24h", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "lastUpdateTime", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "priceFeed", "type": "t_address", "offset": 0, "slot": "7"}, {"label": "isActive", "type": "t_bool", "offset": 20, "slot": "7"}], "numberOfBytes": "256"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)70_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Nonces": [{"contract": "NoncesUpgradeable", "label": "_nonces", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\utils\\NoncesUpgradeable.sol:17", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.EIP712": [{"contract": "EIP712Upgradeable", "label": "_hashedName", "type": "t_bytes32", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:39", "offset": 0, "slot": "0"}, {"contract": "EIP712Upgradeable", "label": "_hashedVersion", "type": "t_bytes32", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:41", "offset": 0, "slot": "1"}, {"contract": "EIP712Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:43", "offset": 0, "slot": "2"}, {"contract": "EIP712Upgradeable", "label": "_version", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\utils\\cryptography\\EIP712Upgradeable.sol:44", "offset": 0, "slot": "3"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "3e82663eb144d7f85f13f0b1c8904dc9361d3be27d43cb873ff191d7f4401033": {"address": "0x57B6b31BCA8F328De34497781f0314D72C504a61", "txHash": "0x06cbf0efeedebe1d9a42b2f95735fbd11643bbcee2e013b578e3fbacee5514d4", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "adminCount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:70"}, {"label": "proposals", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(UnifiedProposal)10299_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:73"}, {"label": "pendingProposals", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_struct(PendingProposals)10317_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:74"}, {"label": "timeConfigs", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_struct(TimeConfig)10307_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:75"}, {"label": "roleConfigs", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_struct(RoleConfig)10313_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:76"}, {"label": "upgradeProposals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:77"}, {"label": "proposalCount", "offset": 0, "slot": "6", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:80"}, {"label": "proposalRegistry", "offset": 0, "slot": "7", "type": "t_mapping(t_uint256,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:81"}, {"label": "packedVars", "offset": 0, "slot": "8", "type": "t_struct(PackedVars)7904_storage", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:88"}, {"label": "_pendingOwnerRequest", "offset": 0, "slot": "9", "type": "t_address", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:90"}, {"label": "_usedNonces", "offset": 0, "slot": "10", "type": "t_mapping(t_uint256,t_mapping(t_uint256,t_uint8))", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:27"}, {"label": "blacklisted", "offset": 0, "slot": "11", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:28"}, {"label": "treasury", "offset": 0, "slot": "12", "type": "t_contract(Treasury)10092", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:31"}, {"label": "platformFeeBps", "offset": 0, "slot": "13", "type": "t_uint256", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:33"}, {"label": "packedVars", "offset": 0, "slot": "14", "type": "t_struct(PackedVars)7904_storage", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:35"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)355_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)211_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)151_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)583_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)647_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(Treasury)10092": {"label": "contract Treasury", "numberOfBytes": "20"}, "t_mapping(t_address,t_bytes32)": {"label": "mapping(address => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(PendingProposals)10317_storage)": {"label": "mapping(address => struct ProposalTypes.PendingProposals)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(TimeConfig)10307_storage)": {"label": "mapping(address => struct ProposalTypes.TimeConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleConfig)10313_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.RoleConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(UnifiedProposal)10299_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.UnifiedProposal)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bytes32)": {"label": "mapping(uint256 => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_uint256,t_uint8))": {"label": "mapping(uint256 => mapping(uint256 => uint8))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint8)": {"label": "mapping(uint256 => uint8)", "numberOfBytes": "32"}, "t_struct(PackedVars)7904_storage": {"label": "struct GovernanceModule.PackedVars", "members": [{"label": "flags", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "lastEmergencyAction", "type": "t_uint40", "offset": 1, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PendingProposals)10317_storage": {"label": "struct ProposalTypes.PendingProposals", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ProposalConfig)10279_storage": {"label": "struct ProposalTypes.ProposalConfig", "members": [{"label": "expiryTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "executionTime", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "approvals", "type": "t_uint16", "offset": 16, "slot": "0"}, {"label": "status", "type": "t_uint8", "offset": 18, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleConfig)10313_storage": {"label": "struct ProposalTypes.RoleConfig", "members": [{"label": "quorum", "type": "t_uint16", "offset": 0, "slot": "0"}, {"label": "transactionLimit", "type": "t_uint240", "offset": 2, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(TimeConfig)10307_storage": {"label": "struct ProposalTypes.TimeConfig", "members": [{"label": "lastActivityTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "roleChangeTimeLock", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "whitelistLockTime", "type": "t_uint64", "offset": 16, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(UnifiedProposal)10299_storage": {"label": "struct ProposalTypes.UnifiedProposal", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "target", "type": "t_address", "offset": 1, "slot": "0"}, {"label": "id", "type": "t_uint40", "offset": 21, "slot": "0"}, {"label": "role", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "tokenAddress", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "amount", "type": "t_uint96", "offset": 20, "slot": "2"}, {"label": "config", "type": "t_struct(ProposalConfig)10279_storage", "offset": 0, "slot": "3"}, {"label": "hasApproved", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}, "t_uint240": {"label": "uint240", "numberOfBytes": "30"}, "t_uint40": {"label": "uint40", "numberOfBytes": "5"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_uint96": {"label": "uint96", "numberOfBytes": "12"}}, "namespaces": {"erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "f2b218209c53185feaefa51912c6945637f40083b91a1024825399729f70f4d3": {"address": "0xE869b453d0372a06c8445f96B18Dc7abCD427330", "txHash": "0x874d22f355607bb0ed6c8dc003097f2947178d2522df75de620135132aa30271", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "adminCount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:70"}, {"label": "proposals", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(UnifiedProposal)10299_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:73"}, {"label": "pendingProposals", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_struct(PendingProposals)10317_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:74"}, {"label": "timeConfigs", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_struct(TimeConfig)10307_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:75"}, {"label": "roleConfigs", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_struct(RoleConfig)10313_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:76"}, {"label": "upgradeProposals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:77"}, {"label": "proposalCount", "offset": 0, "slot": "6", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:80"}, {"label": "proposalRegistry", "offset": 0, "slot": "7", "type": "t_mapping(t_uint256,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:81"}, {"label": "packedVars", "offset": 0, "slot": "8", "type": "t_struct(PackedVars)7904_storage", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:88"}, {"label": "_pendingOwnerRequest", "offset": 0, "slot": "9", "type": "t_address", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:90"}, {"label": "packedVars", "offset": 0, "slot": "10", "type": "t_struct(PackedVars)7904_storage", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:15"}, {"label": "storageToken", "offset": 0, "slot": "11", "type": "t_contract(IERC20)3488", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:41"}, {"label": "vestingCaps", "offset": 0, "slot": "12", "type": "t_mapping(t_uint256,t_struct(VestingCap)5739_storage)", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:42"}, {"label": "vestingWallets", "offset": 0, "slot": "13", "type": "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)5718_storage))", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:43"}, {"label": "capIds", "offset": 0, "slot": "14", "type": "t_array(t_uint256)dyn_storage", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:44"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)211_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)151_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)583_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)647_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_contract(IERC20)3488": {"label": "contract IERC20", "numberOfBytes": "20"}, "t_mapping(t_address,t_bytes32)": {"label": "mapping(address => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)5718_storage))": {"label": "mapping(address => mapping(uint256 => struct TokenDistributionEngine.VestingWalletInfo))", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(PendingProposals)10317_storage)": {"label": "mapping(address => struct ProposalTypes.PendingProposals)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(TimeConfig)10307_storage)": {"label": "mapping(address => struct ProposalTypes.TimeConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleConfig)10313_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.RoleConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(UnifiedProposal)10299_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.UnifiedProposal)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bytes32)": {"label": "mapping(uint256 => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingCap)5739_storage)": {"label": "mapping(uint256 => struct TokenDistributionEngine.VestingCap)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingWalletInfo)5718_storage)": {"label": "mapping(uint256 => struct TokenDistributionEngine.VestingWalletInfo)", "numberOfBytes": "32"}, "t_struct(PackedVars)7904_storage": {"label": "struct GovernanceModule.PackedVars", "members": [{"label": "flags", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "lastEmergencyAction", "type": "t_uint40", "offset": 1, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PendingProposals)10317_storage": {"label": "struct ProposalTypes.PendingProposals", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ProposalConfig)10279_storage": {"label": "struct ProposalTypes.ProposalConfig", "members": [{"label": "expiryTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "executionTime", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "approvals", "type": "t_uint16", "offset": 16, "slot": "0"}, {"label": "status", "type": "t_uint8", "offset": 18, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleConfig)10313_storage": {"label": "struct ProposalTypes.RoleConfig", "members": [{"label": "quorum", "type": "t_uint16", "offset": 0, "slot": "0"}, {"label": "transactionLimit", "type": "t_uint240", "offset": 2, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(TimeConfig)10307_storage": {"label": "struct ProposalTypes.TimeConfig", "members": [{"label": "lastActivityTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "roleChangeTimeLock", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "whitelistLockTime", "type": "t_uint64", "offset": 16, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(UnifiedProposal)10299_storage": {"label": "struct ProposalTypes.UnifiedProposal", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "target", "type": "t_address", "offset": 1, "slot": "0"}, {"label": "id", "type": "t_uint40", "offset": 21, "slot": "0"}, {"label": "role", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "tokenAddress", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "amount", "type": "t_uint96", "offset": 20, "slot": "2"}, {"label": "config", "type": "t_struct(ProposalConfig)10279_storage", "offset": 0, "slot": "3"}, {"label": "hasApproved", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(VestingCap)5739_storage": {"label": "struct TokenDistributionEngine.VestingCap", "members": [{"label": "totalAllocation", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "cliff", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "vestingTerm", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "vestingPlan", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "initialRelease", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "startDate", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "allocatedToWallets", "type": "t_uint256", "offset": 0, "slot": "7"}, {"label": "wallets", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "8"}], "numberOfBytes": "288"}, "t_struct(VestingWalletInfo)5718_storage": {"label": "struct TokenDistributionEngine.VestingWalletInfo", "members": [{"label": "capId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "claimed", "type": "t_uint256", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}, "t_uint240": {"label": "uint240", "numberOfBytes": "30"}, "t_uint40": {"label": "uint40", "numberOfBytes": "5"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_uint96": {"label": "uint96", "numberOfBytes": "12"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "ba497723c809beda1470b54b186ce2288afa69b34ac179aa473084bac1eb7e27": {"address": "0x647C8cbDDF188CeF04C24C1f9A50873C386A0b72", "txHash": "0x9d50f07ab008ae00cdd5c1e8fdcae175e78ae38faaa8af8b6b4ad997dcb94752", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "adminCount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:69"}, {"label": "proposals", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:72"}, {"label": "pendingProposals", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_struct(PendingProposals)14270_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:73"}, {"label": "timeConfigs", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_struct(TimeConfig)14260_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:74"}, {"label": "roleConfigs", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:75"}, {"label": "upgradeProposals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:76"}, {"label": "proposalIndex", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:77"}, {"label": "proposalCount", "offset": 0, "slot": "7", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:80"}, {"label": "proposalRegistry", "offset": 0, "slot": "8", "type": "t_mapping(t_uint256,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:81"}, {"label": "packedVars", "offset": 0, "slot": "9", "type": "t_struct(PackedVars)11667_storage", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:88"}, {"label": "_pendingOwnerRequest", "offset": 0, "slot": "10", "type": "t_address", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:90"}, {"label": "_usedNonces", "offset": 0, "slot": "11", "type": "t_mapping(t_uint256,t_mapping(t_uint256,t_uint8))", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:27"}, {"label": "blacklisted", "offset": 0, "slot": "12", "type": "t_mapping(t_address,t_bool)", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:28"}, {"label": "treasury", "offset": 0, "slot": "13", "type": "t_contract(Treasury)14045", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:31"}, {"label": "platformFeeBps", "offset": 0, "slot": "14", "type": "t_uint256", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:33"}, {"label": "packedVars", "offset": 0, "slot": "15", "type": "t_struct(PackedVars)11667_storage", "contract": "StorageToken", "src": "contracts\\core\\StorageToken.sol:35"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)355_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)211_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)151_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)583_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)647_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_contract(Treasury)14045": {"label": "contract Treasury", "numberOfBytes": "20"}, "t_mapping(t_address,t_bytes32)": {"label": "mapping(address => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(PendingProposals)14270_storage)": {"label": "mapping(address => struct ProposalTypes.PendingProposals)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(TimeConfig)14260_storage)": {"label": "mapping(address => struct ProposalTypes.TimeConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.RoleConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.UnifiedProposal)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bytes32)": {"label": "mapping(uint256 => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_uint256,t_uint8))": {"label": "mapping(uint256 => mapping(uint256 => uint8))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint8)": {"label": "mapping(uint256 => uint8)", "numberOfBytes": "32"}, "t_struct(PackedVars)11667_storage": {"label": "struct GovernanceModule.PackedVars", "members": [{"label": "flags", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "lastEmergencyAction", "type": "t_uint40", "offset": 1, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PendingProposals)14270_storage": {"label": "struct ProposalTypes.PendingProposals", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ProposalConfig)14232_storage": {"label": "struct ProposalTypes.ProposalConfig", "members": [{"label": "expiryTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "executionTime", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "approvals", "type": "t_uint16", "offset": 16, "slot": "0"}, {"label": "status", "type": "t_uint8", "offset": 18, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleConfig)14266_storage": {"label": "struct ProposalTypes.RoleConfig", "members": [{"label": "quorum", "type": "t_uint16", "offset": 0, "slot": "0"}, {"label": "transactionLimit", "type": "t_uint240", "offset": 2, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(TimeConfig)14260_storage": {"label": "struct ProposalTypes.TimeConfig", "members": [{"label": "lastActivityTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "roleChangeTimeLock", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "whitelistLockTime", "type": "t_uint64", "offset": 16, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(UnifiedProposal)14252_storage": {"label": "struct ProposalTypes.UnifiedProposal", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "target", "type": "t_address", "offset": 1, "slot": "0"}, {"label": "id", "type": "t_uint40", "offset": 21, "slot": "0"}, {"label": "role", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "tokenAddress", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "amount", "type": "t_uint96", "offset": 20, "slot": "2"}, {"label": "config", "type": "t_struct(ProposalConfig)14232_storage", "offset": 0, "slot": "3"}, {"label": "hasApproved", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}, "t_uint240": {"label": "uint240", "numberOfBytes": "30"}, "t_uint40": {"label": "uint40", "numberOfBytes": "5"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_uint96": {"label": "uint96", "numberOfBytes": "12"}}, "namespaces": {"erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "f57fb0cf3cef70fcc915f2532c7db40069537dc8f83e042dea6a616aeb388c69": {"address": "0x205B9c812B605f4bdbA4C8fbae5e71A4e39CDaEc", "txHash": "0x41ffdc8e63046efa85e2bedd8bd824edb8ace936b6b08ba82971e148322972bc", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "adminCount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:69"}, {"label": "proposals", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:72"}, {"label": "pendingProposals", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_struct(PendingProposals)14270_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:73"}, {"label": "timeConfigs", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_struct(TimeConfig)14260_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:74"}, {"label": "roleConfigs", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:75"}, {"label": "upgradeProposals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:76"}, {"label": "proposalIndex", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:77"}, {"label": "proposalCount", "offset": 0, "slot": "7", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:80"}, {"label": "proposalRegistry", "offset": 0, "slot": "8", "type": "t_mapping(t_uint256,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:81"}, {"label": "packedVars", "offset": 0, "slot": "9", "type": "t_struct(PackedVars)11667_storage", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:88"}, {"label": "_pendingOwnerRequest", "offset": 0, "slot": "10", "type": "t_address", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:90"}, {"label": "packedVars", "offset": 0, "slot": "11", "type": "t_struct(PackedVars)11667_storage", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:15"}, {"label": "storageToken", "offset": 0, "slot": "12", "type": "t_contract(IERC20)3488", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:41"}, {"label": "vestingCaps", "offset": 0, "slot": "13", "type": "t_mapping(t_uint256,t_struct(VestingCap)9539_storage)", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:42"}, {"label": "vestingWallets", "offset": 0, "slot": "14", "type": "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)9518_storage))", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:43"}, {"label": "capIds", "offset": 0, "slot": "15", "type": "t_array(t_uint256)dyn_storage", "contract": "TokenDistributionEngine", "src": "contracts\\core\\TokenDistributionEngine.sol:44"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)355_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)211_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)151_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)583_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)647_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_contract(IERC20)3488": {"label": "contract IERC20", "numberOfBytes": "20"}, "t_mapping(t_address,t_bytes32)": {"label": "mapping(address => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)9518_storage))": {"label": "mapping(address => mapping(uint256 => struct TokenDistributionEngine.VestingWalletInfo))", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(PendingProposals)14270_storage)": {"label": "mapping(address => struct ProposalTypes.PendingProposals)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(TimeConfig)14260_storage)": {"label": "mapping(address => struct ProposalTypes.TimeConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.RoleConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.UnifiedProposal)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bytes32)": {"label": "mapping(uint256 => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingCap)9539_storage)": {"label": "mapping(uint256 => struct TokenDistributionEngine.VestingCap)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingWalletInfo)9518_storage)": {"label": "mapping(uint256 => struct TokenDistributionEngine.VestingWalletInfo)", "numberOfBytes": "32"}, "t_struct(PackedVars)11667_storage": {"label": "struct GovernanceModule.PackedVars", "members": [{"label": "flags", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "lastEmergencyAction", "type": "t_uint40", "offset": 1, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PendingProposals)14270_storage": {"label": "struct ProposalTypes.PendingProposals", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ProposalConfig)14232_storage": {"label": "struct ProposalTypes.ProposalConfig", "members": [{"label": "expiryTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "executionTime", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "approvals", "type": "t_uint16", "offset": 16, "slot": "0"}, {"label": "status", "type": "t_uint8", "offset": 18, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleConfig)14266_storage": {"label": "struct ProposalTypes.RoleConfig", "members": [{"label": "quorum", "type": "t_uint16", "offset": 0, "slot": "0"}, {"label": "transactionLimit", "type": "t_uint240", "offset": 2, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(TimeConfig)14260_storage": {"label": "struct ProposalTypes.TimeConfig", "members": [{"label": "lastActivityTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "roleChangeTimeLock", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "whitelistLockTime", "type": "t_uint64", "offset": 16, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(UnifiedProposal)14252_storage": {"label": "struct ProposalTypes.UnifiedProposal", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "target", "type": "t_address", "offset": 1, "slot": "0"}, {"label": "id", "type": "t_uint40", "offset": 21, "slot": "0"}, {"label": "role", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "tokenAddress", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "amount", "type": "t_uint96", "offset": 20, "slot": "2"}, {"label": "config", "type": "t_struct(ProposalConfig)14232_storage", "offset": 0, "slot": "3"}, {"label": "hasApproved", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(VestingCap)9539_storage": {"label": "struct TokenDistributionEngine.VestingCap", "members": [{"label": "totalAllocation", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "cliff", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "vestingTerm", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "vestingPlan", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "initialRelease", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "startDate", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "allocatedToWallets", "type": "t_uint256", "offset": 0, "slot": "7"}, {"label": "wallets", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "8"}], "numberOfBytes": "288"}, "t_struct(VestingWalletInfo)9518_storage": {"label": "struct TokenDistributionEngine.VestingWalletInfo", "members": [{"label": "capId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "claimed", "type": "t_uint256", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}, "t_uint240": {"label": "uint240", "numberOfBytes": "30"}, "t_uint40": {"label": "uint40", "numberOfBytes": "5"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_uint96": {"label": "uint96", "numberOfBytes": "12"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "ecf63b8c8b7096a2c9c60c03905ddcca2b8e9713ecacf603740d743ac9f6a64e": {"address": "0xB72eaf54fE516605eABd17F8741ecBbaDC3D126a", "txHash": "0xb94596a081739d73dc42e6500f953dfcdbad364a04aca45019045ff9a11b719f", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "adminCount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:69"}, {"label": "proposals", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:72"}, {"label": "pendingProposals", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_struct(PendingProposals)14270_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:73"}, {"label": "timeConfigs", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_struct(TimeConfig)14260_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:74"}, {"label": "roleConfigs", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:75"}, {"label": "upgradeProposals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:76"}, {"label": "proposalIndex", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:77"}, {"label": "proposalCount", "offset": 0, "slot": "7", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:80"}, {"label": "proposalRegistry", "offset": 0, "slot": "8", "type": "t_mapping(t_uint256,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:81"}, {"label": "packedVars", "offset": 0, "slot": "9", "type": "t_struct(PackedVars)11667_storage", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:88"}, {"label": "_pendingOwnerRequest", "offset": 0, "slot": "10", "type": "t_address", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:90"}, {"label": "packedVars", "offset": 0, "slot": "11", "type": "t_struct(PackedVars)11667_storage", "contract": "AirdropContract", "src": "contracts\\core\\AirDropContract.sol:15"}, {"label": "storageToken", "offset": 0, "slot": "12", "type": "t_contract(IERC20)3488", "contract": "AirdropContract", "src": "contracts\\core\\AirDropContract.sol:42"}, {"label": "vestingCaps", "offset": 0, "slot": "13", "type": "t_mapping(t_uint256,t_struct(VestingCap)4567_storage)", "contract": "AirdropContract", "src": "contracts\\core\\AirDropContract.sol:43"}, {"label": "vestingWallets", "offset": 0, "slot": "14", "type": "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)4546_storage))", "contract": "AirdropContract", "src": "contracts\\core\\AirDropContract.sol:44"}, {"label": "capIds", "offset": 0, "slot": "15", "type": "t_array(t_uint256)dyn_storage", "contract": "AirdropContract", "src": "contracts\\core\\AirDropContract.sol:45"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ERC20Storage)355_storage": {"label": "struct ERC20Upgradeable.ERC20Storage", "members": [{"label": "_balances", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "0"}, {"label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "offset": 0, "slot": "1"}, {"label": "_totalSupply", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "_name", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "_symbol", "type": "t_string_storage", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(InitializableStorage)211_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)151_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)583_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)647_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_contract(IERC20)3488": {"label": "contract IERC20", "numberOfBytes": "20"}, "t_mapping(t_address,t_bytes32)": {"label": "mapping(address => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)4546_storage))": {"label": "mapping(address => mapping(uint256 => struct AirdropContract.VestingWalletInfo))", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(PendingProposals)14270_storage)": {"label": "mapping(address => struct ProposalTypes.PendingProposals)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(TimeConfig)14260_storage)": {"label": "mapping(address => struct ProposalTypes.TimeConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.RoleConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.UnifiedProposal)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bytes32)": {"label": "mapping(uint256 => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingCap)4567_storage)": {"label": "mapping(uint256 => struct AirdropContract.VestingCap)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingWalletInfo)4546_storage)": {"label": "mapping(uint256 => struct AirdropContract.VestingWalletInfo)", "numberOfBytes": "32"}, "t_struct(PackedVars)11667_storage": {"label": "struct GovernanceModule.PackedVars", "members": [{"label": "flags", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "lastEmergencyAction", "type": "t_uint40", "offset": 1, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PendingProposals)14270_storage": {"label": "struct ProposalTypes.PendingProposals", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ProposalConfig)14232_storage": {"label": "struct ProposalTypes.ProposalConfig", "members": [{"label": "expiryTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "executionTime", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "approvals", "type": "t_uint16", "offset": 16, "slot": "0"}, {"label": "status", "type": "t_uint8", "offset": 18, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleConfig)14266_storage": {"label": "struct ProposalTypes.RoleConfig", "members": [{"label": "quorum", "type": "t_uint16", "offset": 0, "slot": "0"}, {"label": "transactionLimit", "type": "t_uint240", "offset": 2, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(TimeConfig)14260_storage": {"label": "struct ProposalTypes.TimeConfig", "members": [{"label": "lastActivityTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "roleChangeTimeLock", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "whitelistLockTime", "type": "t_uint64", "offset": 16, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(UnifiedProposal)14252_storage": {"label": "struct ProposalTypes.UnifiedProposal", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "target", "type": "t_address", "offset": 1, "slot": "0"}, {"label": "id", "type": "t_uint40", "offset": 21, "slot": "0"}, {"label": "role", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "tokenAddress", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "amount", "type": "t_uint96", "offset": 20, "slot": "2"}, {"label": "config", "type": "t_struct(ProposalConfig)14232_storage", "offset": 0, "slot": "3"}, {"label": "hasApproved", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(VestingCap)4567_storage": {"label": "struct AirdropContract.VestingCap", "members": [{"label": "totalAllocation", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "cliff", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "vestingTerm", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "vestingPlan", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "initialRelease", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "startDate", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "allocatedToWallets", "type": "t_uint256", "offset": 0, "slot": "7"}, {"label": "wallets", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "8"}], "numberOfBytes": "288"}, "t_struct(VestingWalletInfo)4546_storage": {"label": "struct AirdropContract.VestingWalletInfo", "members": [{"label": "name", "type": "t_bytes32", "offset": 0, "slot": "0"}, {"label": "capId", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "claimed", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "lastClaimTime", "type": "t_uint256", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}, "t_uint240": {"label": "uint240", "numberOfBytes": "30"}, "t_uint40": {"label": "uint40", "numberOfBytes": "5"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_uint96": {"label": "uint96", "numberOfBytes": "12"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.ERC20": [{"contract": "ERC20Upgradeable", "label": "_balances", "type": "t_mapping(t_address,t_uint256)", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:33", "offset": 0, "slot": "0"}, {"contract": "ERC20Upgradeable", "label": "_allowances", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:35", "offset": 0, "slot": "1"}, {"contract": "ERC20Upgradeable", "label": "_totalSupply", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:37", "offset": 0, "slot": "2"}, {"contract": "ERC20Upgradeable", "label": "_name", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:39", "offset": 0, "slot": "3"}, {"contract": "ERC20Upgradeable", "label": "_symbol", "type": "t_string_storage", "src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\ERC20Upgradeable.sol:40", "offset": 0, "slot": "4"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "8edd0647ccd6cbddb24d528f226767639089f9bb97627d63f7ea97049d9d6faa": {"address": "0x0Ca8E67a0f58d1F530C315CB17c225EDc2406637", "txHash": "0x906af3ddd5dd792fa6ccb095fff0b7e7ba52eb3377c28333329b37ac1511ce29", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "adminCount", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:69"}, {"label": "proposals", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:72"}, {"label": "pendingProposals", "offset": 0, "slot": "2", "type": "t_mapping(t_address,t_struct(PendingProposals)14270_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:73"}, {"label": "timeConfigs", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_struct(TimeConfig)14260_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:74"}, {"label": "roleConfigs", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:75"}, {"label": "upgradeProposals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:76"}, {"label": "proposalIndex", "offset": 0, "slot": "6", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:77"}, {"label": "proposalCount", "offset": 0, "slot": "7", "type": "t_uint256", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:80"}, {"label": "proposalRegistry", "offset": 0, "slot": "8", "type": "t_mapping(t_uint256,t_bytes32)", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:81"}, {"label": "packedVars", "offset": 0, "slot": "9", "type": "t_struct(PackedVars)11667_storage", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:88"}, {"label": "_pendingOwnerRequest", "offset": 0, "slot": "10", "type": "t_address", "contract": "GovernanceModule", "src": "contracts\\governance\\GovernanceModule.sol:90"}, {"label": "packedVars", "offset": 0, "slot": "11", "type": "t_struct(PackedVars)11667_storage", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:15"}, {"label": "storageToken", "offset": 0, "slot": "12", "type": "t_contract(ERC20Upgradeable)1610", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:17"}, {"label": "tgeTimestamp", "offset": 0, "slot": "13", "type": "t_uint256", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:18"}, {"label": "totalAllocation", "offset": 0, "slot": "14", "type": "t_uint256", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:19"}, {"label": "lastActivityTimestamp", "offset": 0, "slot": "15", "type": "t_uint256", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:20"}, {"label": "nextCapId", "offset": 0, "slot": "16", "type": "t_uint256", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:21"}, {"label": "vestingCapsCount", "offset": 0, "slot": "17", "type": "t_uint256", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:22"}, {"label": "vestingCaps", "offset": 0, "slot": "18", "type": "t_mapping(t_uint256,t_struct(VestingCap)14489_storage)", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:24"}, {"label": "vestingWallets", "offset": 0, "slot": "19", "type": "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)14465_storage))", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:25"}, {"label": "substrateRewardInfo", "offset": 0, "slot": "20", "type": "t_mapping(t_address,t_struct(SubstrateRewards)14494_storage)", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:26"}, {"label": "ethereumToSubstrate", "offset": 0, "slot": "21", "type": "t_mapping(t_address,t_bytes_storage)", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:27"}, {"label": "capIds", "offset": 0, "slot": "22", "type": "t_array(t_uint256)dyn_storage", "contract": "TestnetMiningRewards", "src": "contracts\\core\\TestnetMiningRewards.sol:28"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)24_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(AccessControlStorage)34_storage": {"label": "struct AccessControlUpgradeable.AccessControlStorage", "members": [{"label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(InitializableStorage)211_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)151_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PausableStorage)583_storage": {"label": "struct PausableUpgradeable.PausableStorage", "members": [{"label": "_paused", "type": "t_bool", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)647_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleData)24_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "hasRole", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_bytes_storage": {"label": "bytes", "numberOfBytes": "32"}, "t_contract(ERC20Upgradeable)1610": {"label": "contract ERC20Upgradeable", "numberOfBytes": "20"}, "t_mapping(t_address,t_bytes32)": {"label": "mapping(address => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_address,t_bytes_storage)": {"label": "mapping(address => bytes)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_struct(VestingWalletInfo)14465_storage))": {"label": "mapping(address => mapping(uint256 => struct VestingTypes.VestingWalletInfo))", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(PendingProposals)14270_storage)": {"label": "mapping(address => struct ProposalTypes.PendingProposals)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(SubstrateRewards)14494_storage)": {"label": "mapping(address => struct VestingTypes.SubstrateRewards)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(TimeConfig)14260_storage)": {"label": "mapping(address => struct ProposalTypes.TimeConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleConfig)14266_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.RoleConfig)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(UnifiedProposal)14252_storage)": {"label": "mapping(bytes32 => struct ProposalTypes.UnifiedProposal)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bytes32)": {"label": "mapping(uint256 => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingCap)14489_storage)": {"label": "mapping(uint256 => struct VestingTypes.VestingCap)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(VestingWalletInfo)14465_storage)": {"label": "mapping(uint256 => struct VestingTypes.VestingWalletInfo)", "numberOfBytes": "32"}, "t_struct(PackedVars)11667_storage": {"label": "struct GovernanceModule.PackedVars", "members": [{"label": "flags", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "lastEmergencyAction", "type": "t_uint40", "offset": 1, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(PendingProposals)14270_storage": {"label": "struct ProposalTypes.PendingProposals", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ProposalConfig)14232_storage": {"label": "struct ProposalTypes.ProposalConfig", "members": [{"label": "expiryTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "executionTime", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "approvals", "type": "t_uint16", "offset": 16, "slot": "0"}, {"label": "status", "type": "t_uint8", "offset": 18, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(RoleConfig)14266_storage": {"label": "struct ProposalTypes.RoleConfig", "members": [{"label": "quorum", "type": "t_uint16", "offset": 0, "slot": "0"}, {"label": "transactionLimit", "type": "t_uint240", "offset": 2, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(SubstrateRewards)14494_storage": {"label": "struct VestingTypes.SubstrateRewards", "members": [{"label": "lastUpdate", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_struct(TimeConfig)14260_storage": {"label": "struct ProposalTypes.TimeConfig", "members": [{"label": "lastActivityTime", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "roleChangeTimeLock", "type": "t_uint64", "offset": 8, "slot": "0"}, {"label": "whitelistLockTime", "type": "t_uint64", "offset": 16, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(UnifiedProposal)14252_storage": {"label": "struct ProposalTypes.UnifiedProposal", "members": [{"label": "proposalType", "type": "t_uint8", "offset": 0, "slot": "0"}, {"label": "target", "type": "t_address", "offset": 1, "slot": "0"}, {"label": "id", "type": "t_uint40", "offset": 21, "slot": "0"}, {"label": "role", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "tokenAddress", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "amount", "type": "t_uint96", "offset": 20, "slot": "2"}, {"label": "config", "type": "t_struct(ProposalConfig)14232_storage", "offset": 0, "slot": "3"}, {"label": "hasApproved", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(VestingCap)14489_storage": {"label": "struct VestingTypes.VestingCap", "members": [{"label": "totalAllocation", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "cliff", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "vestingTerm", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "vestingPlan", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "initialRelease", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "startDate", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "allocatedToWallets", "type": "t_uint256", "offset": 0, "slot": "7"}, {"label": "wallets", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "8"}, {"label": "maxRewardsPer<PERSON><PERSON>h", "type": "t_uint256", "offset": 0, "slot": "9"}, {"label": "ratio", "type": "t_uint256", "offset": 0, "slot": "10"}], "numberOfBytes": "352"}, "t_struct(VestingWalletInfo)14465_storage": {"label": "struct VestingTypes.VestingWalletInfo", "members": [{"label": "capId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_bytes32", "offset": 0, "slot": "1"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "claimed", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "monthlyClaimedRewards", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "t_uint256", "offset": 0, "slot": "5"}], "numberOfBytes": "192"}, "t_uint16": {"label": "uint16", "numberOfBytes": "2"}, "t_uint240": {"label": "uint240", "numberOfBytes": "30"}, "t_uint40": {"label": "uint40", "numberOfBytes": "5"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_uint96": {"label": "uint96", "numberOfBytes": "12"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ReentrancyGuardUpgradeable.sol:43", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.AccessControl": [{"contract": "AccessControlUpgradeable", "label": "_roles", "type": "t_mapping(t_bytes32,t_struct(RoleData)24_storage)", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:61", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Pausable": [{"contract": "PausableUpgradeable", "label": "_paused", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\utils\\PausableUpgradeable.sol:21", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}